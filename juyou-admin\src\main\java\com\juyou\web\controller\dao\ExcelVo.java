package com.juyou.web.controller.dao;

public class ExcelVo {
    private String account;
    private String password;
    private String picturePath;
    private String filePath;
    private String cells;
    //钱包区域
    private String walletArea;





    @Override
    public String toString() {
        return "ExcelVo{" +
                "account='" + account + '\'' +
                ", password='" + password + '\'' +
                ", picturePath='" + picturePath + '\'' +
                ", filePath='" + filePath + '\'' +
                ", cells='" + cells + '\'' +
                '}';
    }

    public String getCells() {
        return cells;
    }

    public void setCells(String cells) {
        this.cells = cells;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    public String getWalletArea() {return walletArea;}

    public void setWalletArea(String walletArea) {this.walletArea = walletArea;}
}
