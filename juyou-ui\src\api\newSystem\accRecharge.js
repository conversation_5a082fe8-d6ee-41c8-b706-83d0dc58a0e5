import request from '@/utils/request'

// 查询账号充值列表
export function listAccRecharge(query) {
  return request({
    url: '/new/system/accRecharge/list',
    method: 'get',
    params: query
  })
}
// 一级充值查询账号充值列表
export function oneLevelAccountRechargeList(query) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/list',
    method: 'get',
    params: query
  })
}
// 二级充值查询账号充值列表
export function twoLevelAccountRechargeList(query) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/list',
    method: 'get',
    params: query
  })
}
export function listAccRechargeZone(query) {
  return request({
    url: '/new/system/accRecharge/listZone',
    method: 'get',
    params: query
  })
}

// 查询账号充值详细
export function getAccRecharge(acid) {
  return request({
    url: '/new/system/accRecharge/' + acid,
    method: 'get'
  })
}

// 新增账号充值
export function addAccRecharge(data) {
  return request({
    url: '/new/system/accRecharge',
    method: 'post',
    data: data
  })
}

// 修改账号充值
export function updateAccRecharge(data) {
  return request({
    url: '/new/system/accRecharge',
    method: 'put',
    data: data
  })
}

// 删除账号充值
export function delAccRecharge(acid) {
  return request({
    url: '/new/system/accRecharge/' + acid,
    method: 'delete'
  })
}
// 作废账号充值
export function cancelAccRecharge(acid) {
  return request({
    url: '/new/system/accRecharge/cancel/'+ acid,
    method: 'post',
    data:{}
  })
}
// 一级充值作废账号充值
export function deactivateYourAccount(data) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/cancel',
    method: 'post',
    data:data
  })
}
// 二级充值作废账号充值
export function level2TopUpIsVoid(data) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/cancel',
    method: 'post',
    data:data
  })
}
// 一级充值作废账号充值
export function level1TopUpIsVoid(data) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/cancel',
    method: 'post',
    data:data
  })
}
//作废充值账号新接口
export function newCancelAccRecharge(data) {
  return request({
    url: '/new/system/accRecharge/cancel',
    method: 'post',
    data:data
  })
}
//作废一级账号
export function invalidateTheFirstLevelAccount(data) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/cancel',
    method: 'post',
    data:data
  })
}
//作废二级账号
export function invalidateTheSecondLevelAccount(data) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/cancel',
    method: 'post',
    data:data
  })
}
// 领取账号
export function creceiveAccountRecharge(count,accountZone,idType) {
  return request({
    url: '/new/system/accRecharge/receiveAccount?count='+ count + '&accountZone='+ accountZone+'&idType='+idType,
    method: 'post',
    data:{}
  })
}
// 一级充值领取账号
export function oneLevelAccountRechargeReceiveAccount(data) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/receiveAccount',
    method: 'post',
    data
  })
}
// 二级充值领取账号
export function twoLevelAccountRechargeReceiveAccount(data) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/receiveAccount',
    method: 'post',
    data
  })
}
// 账号充值-充值
export function topUpYourAccount(data) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/recharge',
    method: 'post',
    data
  })
}
// 二级账号充值-充值
export function rechargeTheSecondaryAccount(data) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/recharge',
    method: 'post',
    data
  })
}
// 领取账号
export function getListExcludeMeRecharge(data={}) {
  return request({
    header:{
      repeatSubmit:true
    },
    url: '/new/system/user/getListExcludeMe',
    method: 'post',
    data:data
  })
}

// 转交账号
export function transferAccountRecharge(data={}) {
  return request({
    url: '/new/system/accRecharge/transferAccount',
    method: 'post',
    data:data
  })
}

// 我今日已充值金额
export function getTodayRechargeAmount(accountZone,idType) {
  return request({
    url: '/new/system/accRecharge/getTodayRechargeAmount?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}
// 账号充值-数据面板
export function getDataPanel(accountZone) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/getDataPanel',
    method: 'get',
    params:{
      accountZone:accountZone
    }
  })
}
// 二级账号充值-数据面板
export function getTwoDataPanel(accountZone) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/getDataPanel',
    method: 'get',
    params:{
      accountZone:accountZone
    }
  })
}
export function getAccountRechargeResidualList(accountZone) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/getAccountRechargeResidualList',
    method: 'get',
    params:{
      accountZone:accountZone
    }
  })
}
export function getAccountRechargeResidualListTwo(accountZone) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/getAccountRechargeResidualList',
    method: 'get',
    params:{
      accountZone:accountZone
    }
  })
}
// 账号注册未领取账号数量
export function getUnclaimedAccountCount(accountZone,idType) {
  return request({
    url: '/new/system/accRecharge/getUnclaimedAccountCount?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}

//直接完成充值
export function accRechargeCompleteRecharge(acid) {
  return request({
    url: '/new/system/accRecharge/completeRecharge/' +acid,
    method: 'post',
    data:{}
  })
}
//一级充值直接完成充值
export function theFirstLevelRechargeIsDirectlyCompleted(acid) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/completeRecharge/' +acid,
    method: 'post',
    data:{}
  })
}
//二级充值直接完成充值
export function theSecondLevelRechargeIsDirectlyCompleted(acid) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/completeRecharge/' +acid,
    method: 'post',
    data:{}
  })
}
//一级充值详情
export function oneLevelAccountRechargeDetail(acid) {
  return request({
    url: '/new/manager/oneLevelAccountRecharge/' +acid,
    method: 'get',
  })
}
//二级充值详情
export function twoLevelAccountRechargeDetail(acid) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/' +acid,
    method: 'get',
  })
}
//代充查询详情
export function chargingInquiryDetail(acid) {
  return request({
    url: `/new/manager/chargingInquiry/detail/${acid}`,
    method: 'get',
  })
}
//代充查询详情修改
export function chargingInquiryEdit(data) {
  return request({
    url: `/new/manager/chargingInquiry/edit`,
    method: 'post',
    data
  })
}
//获取操作日志
export function getLogList(params) {
  return request({
    url: `/new/system/log/list`,
    method: 'get',
    params
  })
}
//获取用户列表
export function getUserList(params) {
  return request({
    url: `/new/system/log/listUser`,
    method: 'get',
    params
  })
}
