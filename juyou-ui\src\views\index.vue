<template>
  <div class="app-container home">
    <div v-hasPermi="['system:apple:recharge']" class="box_itme" @click="gotoAccRecharge">
      <div>苹果充值</div>
    </div>
    <div v-hasPermi="['system:apple:sell']" class="box_itme" @click="gotoSellcard">
      <div>苹果出售</div>
    </div>
    <div v-hasPermi="['system:razer:recharge']" class="box_itme" @click="gotoAccRechargeById">
      <div>雷蛇充值</div>
    </div>
    <div v-hasPermi="['system:razer:sell']" class="box_itme" @click="gotoSellcardById">
      <div>雷蛇出售</div>
    </div>
    <div v-hasPermi="['system:account:import']" class="box_itme" @click="uploadAccRecharge">
      <div>账号导入</div>
    </div>
    <el-dialog title="账号导入" :visible.sync="diaUpload" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" label-width="100px" :rules="rules">
        <el-form-item label="账号区域" prop="accountZone">
          <el-select v-model="uploadForm.accountZone" placeholder="请选择">
            <el-option v-for="item in accZone" :key="item.dictValue" :label="item.dictLabel" :value="item.dictLabel">
              {{ item.dictLabel }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号类型" prop="idType">
          <el-select v-model="uploadForm.idType" placeholder="请选择" @change="zoneChange">
            <el-option value="0" label="苹果">苹果</el-option>
            <el-option value="1" label="雷蛇">雷蛇</el-option>
          </el-select>
        </el-form-item>
        <div class="upload-container">
          <el-upload accept=".xls,.xlsx" :action="uploadaccFilePath" :multiple="false" :file-list="fileList"
                     :disabled="disZone"
                     :headers="headers" :on-success="handleUploadSuccess" :on-error="handleUploadError"
                     :on-change="handleChange"
                     :on-remove="handleRemove">
            <el-button type="primary" icon="el-icon-upload">账号导入</el-button>
            <div slot="tip" class="el-upload__tip">只能上传不超过 3MB 的.xls,.xlsx文件</div>
          </el-upload>
          <el-button type="primary" icon="el-icon-download" @click="downloadTemplate" class="download-btn">下载苹果模板
          </el-button>
          <el-button type="primary" icon="el-icon-download" @click="downloadTemplateById" class="download-btn">下载雷蛇模板
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {getDicts} from "@/api/system/dict/data";
import {getToken} from "@/utils/auth";

export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.8.2",
      diaUpload: false,
      accZone: [],
      uploadForm: {},
      uploadaccFilePath: undefined,
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      disZone: true,
      queryParams: {
      }
    };
  },
  created() {
    this.into()
  },
  methods: {
    downloadTemplate() {
      this.download('/new/system/accRecharge/import', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    },
    downloadTemplateById() {
      this.download('/new/system/accRecharge/importById', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    },
    into() {
      getDicts("account_zone").then(response => {
          this.accZone = response.data;
        }
      );
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      if (res.code === 200) {
        this.$modal.confirm(res.msg).then(function () {
        })
        // this.$modal.msgSuccess(res.msg);
        // this.getList();//重新刷新列表
        // this.getTodayR();
      } else {
        this.$modal.msgError(res.msg || '导入失败');
      }
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("导入失败，请重试");
      this.$modal.closeLoading();
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.info(this.fileList);
    },
    // 删除文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止删除。function(file, fileList)
    //上传文件事件
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    zoneChange() {
      if (this.uploadForm.accountZone == undefined) {
        this.$modal.msgError("请先选择国家");
        this.uploadForm.idType = undefined;
        return;
      }
      if (this.uploadForm.idType != undefined) {
        this.uploadaccFilePath = process.env.VUE_APP_BASE_API + "/system/accRecharge/importData?accountZone=" + this.uploadForm.accountZone+"&idType=" + this.uploadForm.idType;
      }
      if (this.uploadaccFilePath != undefined) {
        this.disZone = false;
      }
    },
    gotoAccRecharge() {
      this.$router.push('/acrechargemenu/accRecharge')
    },
    gotoSellcard() {
      this.$router.push('/acrechargemenu/sellcard')
    },
    gotoAccRechargeById() {
      this.$router.push('/acrechargemenu/accRechargeById')
    },
    gotoSellcardById() {
      this.$router.push('/acrechargemenu/sellcardById')
    },
    uploadAccRecharge() {
      this.diaUpload = true;
      this.uploadForm={};
      this.uploadaccFilePath = undefined;
      this.disZone = true;
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.upload-container {
  display: flex;
  align-items: center;
}

.download-btn {
  margin-left: 5px;
  margin-top: -23px;
}

.home {
  display: flex;
  flex-direction: row;

  .box_itme {
    display: flex;
    margin: 10px;
    padding: 10px;
    background: #1890ff;
    color: #fff;
    width: 100px;
    height: 60px;
    align-items: center;
    justify-content: center;
    border-radius: 2px
  }

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>

