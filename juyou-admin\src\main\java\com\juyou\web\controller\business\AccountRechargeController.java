package com.juyou.web.controller.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.domain.entity.SysDictData;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.StringUtils;
import com.juyou.common.utils.file.FileUploadUtils;
import com.juyou.common.utils.file.MimeTypeUtils;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountRechargeExport;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.excel.ExcelAccountRecharge;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.domain.vo.AccountRechargeVo;
import com.juyou.system.domain.vo.AccountSellReportDetailVo;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.params.AccountRechargeCancelParam;
import com.juyou.system.params.AccountRechargeEditParam;
import com.juyou.system.params.AccountRechargePageParam;
import com.juyou.system.params.AccountTransferAccountParam;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IAccountSellService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import com.juyou.system.service.ISysDictDataService;
import com.juyou.system.utils.JuYouBusinessUtil;
import com.juyou.web.controller.dao.ExcelVo;
import com.juyou.web.controller.tool.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 账号充值Controller
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Api(value = "账号充值", tags = "账号充值")
@RestController
@RequestMapping("/system/accRecharge")
@Slf4j
public class AccountRechargeController extends BaseController {


    @Autowired
    private IAccountRechargeService accountRechargeService;

    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private IAccountSellService accountSellService;

    private final ReentrantLock lock = new ReentrantLock();


    @ApiOperation("账号注册未领取账号数量")
    @Log(title = "账号充值-账号注册未领取账号数量", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:unclaimedAccountCount')")
    @PostMapping("/getUnclaimedAccountCount")
    public ResultData<Integer> getUnclaimedAccountCount(String accountZone,String idType) {
        Integer count = this.accountRechargeService.countByStatus(AccountRechargeEnum.STATUS_0.getCode(), accountZone, idType);
        return ResultUtil.success(count);
    }

    @ApiOperation("我今日已充值金额")
    @Log(title = "账号充值-我今日已充值金额", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:todayRechargeAmount')")
    @PostMapping("/getTodayRechargeAmount")
    public ResultData<BigDecimal> getTodayRechargeAmount(String accountZone,String idType) {
        BigDecimal todayRechargeAmount = this.accountRechargeService.getTodayRechargeAmount(super.getUserId(), accountZone,idType);
        System.out.println("++++" + accountZone);
        return ResultUtil.success(todayRechargeAmount);
    }

    @ApiOperation("查询账号充值列表")
    @Log(title = "账号充值-查询账号充值列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountRecharge> list(AccountRechargePageParam accountRecharge) {
        //startPage();
        accountRecharge.setReceiveUser(super.getUsername());
        List<AccountRecharge> list = this.accountRechargeService.selectAccountRechargePage(accountRecharge);

        TableDataInfo<AccountRecharge> dataTable = getDataTable(list);
        if (CollUtil.isNotEmpty(dataTable.getRows())) {
            for (AccountRecharge item : dataTable.getRows()) {
                item.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(item.getBuyAmt(), item.getRechargeAmt()));
            }
        }
        return dataTable;
    }

    @ApiOperation("查询账号充值地区列表")
    @PreAuthorize("@ss.hasPermi('system:accRecharge:listZone')")
    @GetMapping("/listZone")
    public TableDataInfo listZone(AccountRecharge accountRecharge) {
        Map<String, Integer> map = new HashMap<>();
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("account_zone");
        List<SysDictData> data = sysDictDataService.selectDictDataList(sysDictData);
        List<AccountRechargeVo> list = accountRechargeService.selectAccountRechargeVoList(accountRecharge);
        list.forEach(item -> {
            map.put(item.getCountry(), item.getQuantity());
        });
        for (SysDictData item : data){
            if (map.get(item.getDictLabel()) != null){
                item.setDictValue(map.get(item.getDictLabel()).toString());
            } else {
                item.setDictValue("0");
            }
        }

        return getDataTable(data);
    }

    @Value("${file.upload.path}")
    private String fileUploadPath;
    public String fileUpload(@RequestParam("file") MultipartFile file) throws IOException {

        String fileSuffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        String fileName = IdUtil.simpleUUID();
        String filePath = fileUploadPath + fileName + fileSuffix;
        file.transferTo(new File(filePath));

        // FileVo vo = new FileVo();
        // vo.setFileName("/showFile/" + fileName + fileSuffix);
        AjaxResult success = AjaxResult.success();
        return "/showFile/" + fileName + fileSuffix;
    }

    @ApiOperation("账号充值导入")
    @Log(title = "账号充值-账号充值导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasAnyPermi('system:accRecharge:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, String accountZone,String idType) throws Exception {
        ExcelUtil<ExcelAccountRecharge> util = new ExcelUtil<>(ExcelAccountRecharge.class);
        List<ExcelAccountRecharge> list = new ArrayList<ExcelAccountRecharge>();
        if (("0").equals(idType)) {
            list = util.importExcel(file.getInputStream());
        } else {
            String time = String.valueOf(System.currentTimeMillis());
            List<ExcelVo> excelVo = ExcelUtils.dispose(file, fileUploadPath+time,time);
            for (ExcelVo item : excelVo) {
                if (StringUtils.isNotEmpty(item.getAccount()) && StringUtils.isNotEmpty(item.getPassword())) {
                    ExcelAccountRecharge account = new ExcelAccountRecharge();
                    account.setAccountName(item.getAccount());
                    account.setAccountPwd(item.getPassword());
                    account.setImg(item.getPicturePath());
                    account.setWalletArea(item.getWalletArea());
                    list.add(account);
                }
            }
        }
        List<AccountRecharge> accountList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            accountList = list.stream().map(item -> {
                AccountRecharge account = new AccountRecharge();
                account.setAccountName(item.getAccountName());
                account.setAccountPwd(item.getAccountPwd());
                account.setSpareCode(item.getImg());
                account.setWalletArea(item.getWalletArea());
                account.setIdType(idType);
                account.setStatus(AccountRechargeEnum.STATUS_0.getCode());
                account.setCreateTime(new Date());
                account.setAccountZone(accountZone);
                account.setCreateUser(super.getUsername());
                account.setUpdateTime(new Date());
                account.setUpdateUser(super.getUsername());
                return account;
            }).collect(Collectors.toList());
        }
        String result = this.accountRechargeService.importData(accountList);
        if (StrUtil.isNotBlank(result)) {
            return AjaxResult.success(result);
        }
        return AjaxResult.success("导入成功");
    }

    @ApiOperation("导出账号充值列表")
    @Log(title = "账号充值-导出账号充值列表", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountRecharge accountRecharge) {
        List<AccountRecharge> list = accountRechargeService.selectAccountRechargeList(accountRecharge);
        ExcelUtil<AccountRecharge> util = new ExcelUtil<AccountRecharge>(AccountRecharge.class);
        util.exportExcel(response, list, "账号充值数据");
    }

    @ApiOperation("获取账号充值详细信息")
    @Log(title = "账号充值-获取账号充值详细信息", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:query')")
    @GetMapping(value = "/{acid}")
    public ResultData<AccountRechargeDetailVo> getInfo(@PathVariable("acid") Integer acid) {
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空");
        }
        AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(acid);
        GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
        rechargeRecord.setAccount(accountRecharge.getAccountName());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(rechargeRecord);

        AccountRechargeDetailVo vo = new AccountRechargeDetailVo();
        BeanUtil.copyProperties(accountRecharge, vo);
        vo.setGiftCardRechargeRecordList(list);

        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getRechargeAmt()));

        return ResultUtil.success(vo);
    }

    @ApiOperation("新增账号充值")
    @Log(title = "账号充值-新增账号充值", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:add')")
    @PostMapping
    public AjaxResult add(@RequestBody AccountRecharge accountRecharge) {
        // check
        if (StrUtil.isBlank(accountRecharge.getStatus())) {
            throw new ServiceException("状态不能为空!");
        }

        Date now = new Date();

        // 部分充值赋值逻辑
        // 完成充值赋值逻辑
        switch (AccountRechargeEnum.getEnum(accountRecharge.getStatus())) {
            case STATUS_2: {
                accountRecharge.setReceiveTime(now);
                accountRecharge.setCreateTime(now);
                accountRecharge.setUpdateTime(now);
                accountRecharge.setReceiveUser(super.getUsername());
                accountRecharge.setCreateUser(super.getUsername());
                accountRecharge.setUpdateUser(super.getUsername());
                accountRecharge.setReceiveUserId(super.getUserId());
                accountRecharge.setDeptId(super.getDeptId());
            }
            break;
            case STATUS_3: {
                accountRecharge.setReceiveTime(now);
                accountRecharge.setCreateTime(now);
                accountRecharge.setUpdateTime(now);
                accountRecharge.setDoneTime(now);
                accountRecharge.setReceiveUser(super.getUsername());
                accountRecharge.setDoneUser(super.getUsername());
                accountRecharge.setCreateUser(super.getUsername());
                accountRecharge.setUpdateUser(super.getUsername());
                accountRecharge.setReceiveUserId(super.getUserId());
                accountRecharge.setDeptId(super.getDeptId());
            }
            break;
            default:
                throw new ServiceException("状态传值不正确!");
        }
        return toAjax(accountRechargeService.insertAccountRecharge(accountRecharge));
    }

    @ApiOperation("转交账号")
    @Log(title = "账号充值-账号充值", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:transferAccount')")
    @PostMapping("/transferAccount")
    public AjaxResult transferAccount(@RequestBody AccountTransferAccountParam param) {
        if (ObjUtil.isNull(param.getUserId())) {
            throw new ServiceException("用户id不能为空!");
        }
        if (StrUtil.isBlank(param.getUserName())) {
            throw new ServiceException("用户账号不能为空!");
        }
        param.setCurrentUserId(super.getUserId());

        int i = this.accountRechargeService.transferAccount(param);

        return toAjax(i);
    }

    @ApiOperation("领取账号")
    @Log(title = "账号充值-领取账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:receiveAccount')")
    @PostMapping("/receiveAccount")
    public AjaxResult receiveAccount(Integer count, String accountZone,String idType) {
        if (ObjUtil.isNull(count)) {
            throw new ServiceException("数量不能为空!");
        }
        try {
            int i = 0;
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                i = this.accountRechargeService.receiveAccount(count, super.getUserId(), super.getUsername(), super.getDeptId(), accountZone, idType);
                if (i == 0) {
                    throw new ServiceException("请稍后重试，没有待分配的账号或领取账号不能超过10个账号!");
                }
                return toAjax(i);
            }
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } catch (InterruptedException e) {
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation("领取指定钱包区域账户账号")
    @Log(title = "账号充值-领取指定钱包区域账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:receiveAccount')")
    @PostMapping("/designationReceiveAccount")
    public AjaxResult designationReceiveAccount(Integer count, String accountZone,String idType,String walletArea) {
        if (ObjUtil.isNull(count)) {
            throw new ServiceException("数量不能为空!");
        }
        try {
            int i = 0;
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                i = this.accountRechargeService.designationReceiveAccount(count, super.getUserId(), super.getUsername(), super.getDeptId(), accountZone, idType,walletArea);
                if (i == 0) {
                    throw new ServiceException("请稍后重试，没有待分配的账号或领取账号不能超过10个账号!");
                }
                return toAjax(i);
            }
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } catch (InterruptedException e) {
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    @ApiOperation("完成充值")
    @Log(title = "账号充值-完成充值", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:completeRecharge')")
    @PostMapping("/completeRecharge/{acid}")
    public ResultData<Integer> completeRecharge(@PathVariable("acid") Integer acid) {
        // check
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空!");
        }
        Date now = new Date();
        AccountRecharge recharge = this.accountRechargeService.selectAccountRechargeByAcid(acid);

        int count = this.iGiftCardRechargeRecordService.countPledgeThirtyMinutes(recharge.getAccountName());

        if (count > 0) {
            throw new ServiceException("礼品卡代码质押时间未超过30分钟。");
        }

        recharge.setStatus(AccountRechargeEnum.STATUS_3.getCode());
        recharge.setUpdateTime(now);
        recharge.setDoneTime(now);
        recharge.setUpdateBy(super.getUsername());
        recharge.setDoneUser(super.getUsername());
        int i = this.accountRechargeService.updateAccountRecharge(recharge);
        return ResultUtil.success(i);
    }
    @ApiOperation("修改账号充值")
    @Log(title = "账号充值-修改账号充值", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody AccountRechargeEditParam param) {
        //判断进价不能大于7.5
        if (param.getBuyPrice() > 7.5) {
            throw new ServiceException("输入的进价不能大于7.5");
        }
        // check
        if (ObjUtil.isNull(param.getStatus())) {
            throw new ServiceException("状态不能为空!");
        }
        if (StrUtil.isBlank(param.getGiftCard())) {
            throw new ServiceException("礼品卡代码不能为空!");
        }
        // 校验账号,礼品卡是否重复
        GiftCardRechargeRecord giftSelect = new GiftCardRechargeRecord();
        giftSelect.setAccount(param.getAccountName());
        giftSelect.setGiftCard(param.getGiftCard());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(giftSelect);
        if (CollUtil.isNotEmpty(list)) {
            throw new ServiceException("账号与礼品卡在系统中已经存在请确认!");
        }

        // 是否质押30分钟设置默认值，为空设置为否
        if (StrUtil.isBlank(param.getPledgeThirtyMinutes())) {
            param.setPledgeThirtyMinutes("0");
        }

        int resultCount = 0;
        switch (Objects.requireNonNull(AccountRechargeEnum.getEnum(param.getStatus()))) {
            // 部分充值
            case STATUS_2: {
                resultCount = this.accountRechargeService.partialRecharge(param, super.getUsername());
            }
            break;
            case STATUS_3: {
                resultCount = this.accountRechargeService.finishRecharge(param, super.getUsername());
            }
            break;
            default:
                throw new ServiceException("状态传值不正确!");
        }

        return toAjax(resultCount);
    }


    @ApiOperation("账号充值作废")
    @Log(title = "账号充值-账号充值作废", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:cancel')")
    @PostMapping("/cancel")
    public ResultData<Integer> cancel(@RequestBody AccountRechargeCancelParam param) {
        // check
        if (ObjUtil.isNull(param.getAcid())) {
            throw new ServiceException("acid,唯一标识不能为空!");
        }
        if (StrUtil.isBlank(param.getCancelReasonType())) {
            throw new ServiceException("作废原因不能为空!");
        }
        param.setUpdateBy(super.getUsername());
        return ResultUtil.success(accountRechargeService.cancel(param));
    }

    @ApiOperation("删除账号充值")
    @Log(title = "账号充值-删除账号充值", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('system:accRecharge:remove')")
    @DeleteMapping("/{acids}")
    public AjaxResult remove(@PathVariable Integer[] acids) {
        return toAjax(accountRechargeService.deleteAccountRechargeByAcids(acids));
    }

    @ApiOperation("下载模板")
    @Log(title = "下载模板", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public void imports(HttpServletResponse response) {
        List<AccountRechargeExport> list = new ArrayList<>();
        ExcelUtil<AccountRechargeExport> util = new ExcelUtil<AccountRechargeExport>(AccountRechargeExport.class);
        util.exportExcel(response, list, "下载模板数据");
    }

    @ApiOperation("下载模板")
    @Log(title = "下载模板", businessType = BusinessType.IMPORT)
    @PostMapping("/importById")
    public void importById(HttpServletResponse response) {
        List<ExcelAccountRecharge> list = new ArrayList<>();
        ExcelUtil<ExcelAccountRecharge> util = new ExcelUtil<ExcelAccountRecharge>(ExcelAccountRecharge.class);
        util.exportExcel(response, list, "下载模板数据");
    }

}
