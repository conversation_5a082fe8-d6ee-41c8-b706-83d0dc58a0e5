import request from '@/utils/request'
// 查询代充卡记录列表
export function getRecordOfChargingCardsList(data = {}) {
  return request({
    url: '/new/recordOfChargingCards/getRecordOfChargingCardsList',
    method: 'get',
    params: data
  })
}
// 查询未生效列表
export function getNotEffectiveList(data = {}) {
  return request({
    url: '/new/accountRecharge/notEffective/notEffectiveList',
    method: 'get',
    params: data
  })
}
// 批量完成等待
export function updateNotEffectiveList(data = {}) {
  return request({
    url: `/new/accountRecharge/notEffective/updateNotEffectiveList`,
    method: 'put',
    data
  })
}
// 查询用户列表
export function getListAll(data = {}) {
  return request({
    url: '/new/system/user/getListAll',
    method: 'post',
    data: data
  })
}
// 查询来源群列表
export function getSourceGroupsList() {
  return request({
    url: '/new/system/substitutionSettings/sourceGroupsList?status=1',
    method: 'get',
  })
}
// 查询出售群列表
export function sellingGroupsList() {
  return request({
    url: '/new/system/substitutionSettings/sellingGroupsList?status=1',
    method: 'get',
  })
}

// 查询待出售列表
export function getAccountSellPendingSaleList(data = {}) {
  return request({
    url: '/new/accountSellSoldReport/getAccountSellSoldReportList',
    method: 'get',
    params: data
  })
}

export function batchWriteOff(data = {}){
  return request({
    url: '/new/accountSellSoldReport/batchWriteOff',
    method: 'post',
    data: data
  })
}
