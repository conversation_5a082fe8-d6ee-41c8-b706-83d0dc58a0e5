package com.juyou.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 sys_dept_session
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public class SysDeptSession extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** session过期时间（分钟） */
    @Excel(name = "session过期时间", readConverterExp = "分=钟")
    private Long session;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setSession(Long session)
    {
        this.session = session;
    }

    public Long getSession()
    {
        return session;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deptId", getDeptId())
                .append("session", getSession())
                .toString();
    }
}
