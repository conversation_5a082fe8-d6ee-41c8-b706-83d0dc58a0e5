package com.juyou.web.controller.system;

import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.TdOrder;
import com.juyou.system.service.ITdOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单Controller
 * 
 * <AUTHOR>
 * @date 2023-06-17
 */
@RestController
@RequestMapping("/system/order")
public class TdOrderController extends BaseController
{
    @Autowired
    private ITdOrderService tdOrderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(TdOrder tdOrder)
    {
        startPage();
        List<TdOrder> list = tdOrderService.selectTdOrderList(tdOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TdOrder tdOrder)
    {
        List<TdOrder> list = tdOrderService.selectTdOrderList(tdOrder);
        ExcelUtil<TdOrder> util = new ExcelUtil<TdOrder>(TdOrder.class);
        return util.exportExcel(list, "order");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tdOrderService.selectTdOrderById(id));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TdOrder tdOrder)
    {
        return toAjax(tdOrderService.insertTdOrder(tdOrder));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TdOrder tdOrder)
    {
        return toAjax(tdOrderService.updateTdOrder(tdOrder));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tdOrderService.deleteTdOrderByIds(ids));
    }
}
