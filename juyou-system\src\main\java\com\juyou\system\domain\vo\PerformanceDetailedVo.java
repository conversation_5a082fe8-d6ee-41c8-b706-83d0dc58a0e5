package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PerformanceDetailedVo {
   //区域
    @ApiModelProperty(value = "区域")
    private String accountZone;
    //充值总ID余额
    @ApiModelProperty(value = "充值总ID余额")
    private BigDecimal rechargeTotal;
    //充值作废总ID余额
    @ApiModelProperty(value = "充值作废总ID余额")
    private BigDecimal rechargeCancelTotal;
    //出售总ID余额
    @ApiModelProperty(value = "出售总ID余额")
    private BigDecimal sellTotal;
    //出售作废总ID余额
    @ApiModelProperty(value = "出售作废总ID余额")
     private BigDecimal sellCancelTotal;


}
