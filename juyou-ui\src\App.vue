<template>
  <div id="app">
    <router-view v-if="lockStatus !== 'Y'" />
    <SaveLock v-else></SaveLock>
  </div>
</template>

<script>
import SaveLock from '@/components/SaveLock'
import { updateLockStatus } from '@/api/newSystem/user'
export default  {
  name:  'App',
  components:{
    SaveLock
  },
  data(){
    return {
      timer:null,
    }
  },
  created() {
    this.listenForOperations()

  },
  computed: {
    lockStatus() {
      return this.$store.state.user.userInfo.lockStatus
    },
    lockDuration() {
      return this.$store.state.user.userInfo.lockDuration
    }
  },
  methods:{
    lockScreen(){
      if (this.$store.state.user.userInfo.isLock === 'Y'){
        updateLockStatus({
          userId:this.$store.state.user.userInfo.userId,
          lockStatus:'Y',
        }).then(()=>{
          this.$store.dispatch("GetInfo")
        })

      }

    },
    listenForOperations(){
      document.addEventListener('click',(e)=>{
        clearTimeout(this.timer)
        this.timer = setTimeout(e=>{
          this.lockScreen()
        },+this.lockDuration * 60000 || 300000)
      })
      document.addEventListener('keyup',(e)=>{
        clearTimeout(this.timer)
        this.timer = setTimeout(e=>{
          this.lockScreen()
        },this.lockDuration * 60000 || 300000)
      })
    }
  },
  metaInfo() {
      return {
          title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
          titleTemplate: title => {
              return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
          }
      }
  },

  }
</script>
