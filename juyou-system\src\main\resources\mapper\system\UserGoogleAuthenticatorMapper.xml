<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.UserGoogleAuthenticatorMapper">
    
    <resultMap type="UserGoogleAuthenticator" id="UserGoogleAuthenticatorResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="googleSecret"    column="google_secret"    />
        <result property="secretQrCode"    column="secret_qr_code"    />
    </resultMap>

    <sql id="selectUserGoogleAuthenticatorVo">
        select id, user_id, user_name, google_secret, secret_qr_code from user_google_authenticator
    </sql>

    <select id="selectUserGoogleAuthenticatorList" parameterType="UserGoogleAuthenticator" resultMap="UserGoogleAuthenticatorResult">
        <include refid="selectUserGoogleAuthenticatorVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name = #{userName} </if>
            <if test="googleSecret != null  and googleSecret != ''"> and google_secret = #{googleSecret}</if>
            <if test="secretQrCode != null  and secretQrCode != ''"> and secret_qr_code = #{secretQrCode}</if>
        </where>
    </select>
    
    <select id="selectUserGoogleAuthenticatorById" parameterType="Long" resultMap="UserGoogleAuthenticatorResult">
        <include refid="selectUserGoogleAuthenticatorVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserGoogleAuthenticator" parameterType="UserGoogleAuthenticator" useGeneratedKeys="true" keyProperty="id">
        insert into user_google_authenticator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="googleSecret != null and googleSecret != ''">google_secret,</if>
            <if test="secretQrCode != null">secret_qr_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="googleSecret != null and googleSecret != ''">#{googleSecret},</if>
            <if test="secretQrCode != null">#{secretQrCode},</if>
         </trim>
    </insert>

    <update id="updateUserGoogleAuthenticator" parameterType="UserGoogleAuthenticator">
        update user_google_authenticator
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="googleSecret != null and googleSecret != ''">google_secret = #{googleSecret},</if>
            <if test="secretQrCode != null">secret_qr_code = #{secretQrCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserGoogleAuthenticatorById" parameterType="Long">
        delete from user_google_authenticator where id = #{id}
    </delete>

    <delete id="deleteUserGoogleAuthenticatorByIds" parameterType="String">
        delete from user_google_authenticator where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>