package com.juyou.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业绩Vo-List
 */
@Data
@ApiModel("PerformanceVoList")
public class PerformanceVoList implements Serializable {

    private static final long serialVersionUID = 1127748277814945692L;

    @ApiModelProperty("姓名")
    private String nickName;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("充值账号数量")
    private Integer rechargeCount;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmt;

    @ApiModelProperty("出售账号数量")
    private Integer sellCount;

    @ApiModelProperty("出售金额")
    private BigDecimal sellAmt;

    @ApiModelProperty("工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workDate;


}
