/*
 Navicat MySQL Data Transfer

 Source Server         : 121服务器
 Source Server Type    : MySQL
 Source Server Version : 80025 (8.0.25)
 Source Host           : ***************:3306
 Source Schema         : juyou_tool

 Target Server Type    : MySQL
 Target Server Version : 80025 (8.0.25)
 File Encoding         : 65001

 Date: 26/07/2024 10:35:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tow_update_history
-- ----------------------------
DROP TABLE IF EXISTS `tow_update_history`;
CREATE TABLE `tow_update_history`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '序列',
                                       `current_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '当前消息',
                                       `previous_info` bigint NULL DEFAULT NULL COMMENT '上一条消息',
                                       `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                       `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
