package com.juyou.web.controller;

import com.juyou.web.controller.dao.ExcelVo;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

import static com.juyou.web.controller.tool.ExcelUtils.dispose;

@RestController
public class UpFile {

//    @PostMapping("/upload")
//    public ResponseEntity<String> uploadFile(@RequestParam("file") MultipartFile file) {
//        if (file.isEmpty()) {
//            return new ResponseEntity<>("上传失败，请选择文件", HttpStatus.OK);
//        }
//        try {
//         String time= String.valueOf(System.currentTimeMillis());
//         List<ExcelVo> list= dispose(file,"d:/abc/"+time);
//          for (ExcelVo excelVo : list) {
//              System.out.println(excelVo);
//          }
//        } catch (Exception e) {
//            return new ResponseEntity<>("上传失败", HttpStatus.OK);
//        }
//
//        return new ResponseEntity<>("上传成功", HttpStatus.OK);
//    }

}
