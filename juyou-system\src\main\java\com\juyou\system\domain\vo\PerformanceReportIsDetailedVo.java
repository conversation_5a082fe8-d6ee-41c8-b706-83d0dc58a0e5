package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@Data
public class PerformanceReportIsDetailedVo {
   //充值作废率
   @ApiModelProperty("充值作废率")
    private BigDecimal rechargeCancelRate;
    //出售作废率
    @ApiModelProperty("出售作废率")
    private BigDecimal sellCancelRate;
    @ApiModelProperty("退回率")
    private BigDecimal bounceRate;
    //充值数量
    @ApiModelProperty("充值数量")
    private Integer rechargeCount;
    //充值成本总额
    @ApiModelProperty("充值成本总额")
    private BigDecimal rechargeCostTotal;
    //充值作废数量
    @ApiModelProperty("充值作废数量")
    private Integer rechargeCancelCount;
    //充值作废成本总额
    @ApiModelProperty("充值作废成本总额")
    private BigDecimal rechargeCancelCostTotal;
    //出售数量
    @ApiModelProperty("出售数量")
    private Integer sellCount;
    //出售成本总额
    @ApiModelProperty("出售成本总额")
    private BigDecimal sellCostTotal;
    //出售作废数量
    @ApiModelProperty("出售作废数量")
    private Integer sellCancelCount;
    //出售作废成本总额
    @ApiModelProperty("出售作废成本总额")
    private BigDecimal sellCancelCostTotal;
    @ApiModelProperty("详细数据")
    List< PerformanceDetailedVo>  performanceDetailedVoList;

}
