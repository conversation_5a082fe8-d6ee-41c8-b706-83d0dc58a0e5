package com.juyou.web.controller.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.domain.vo.AccountSellVo;
import com.juyou.system.params.AccountSellBatchWriteOffParam;
import com.juyou.system.params.AccountSellPendingSaleSearchParam;
import com.juyou.system.params.AccountSellUpdateChatgroupNameParam;
import com.juyou.system.service.IAccountSellService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账号销售-已出售账号Controller
 */
@Api(value = "账号销售-已出售账号Controller", tags = "账号销售-已出售账号Controller")
@RestController
@RequestMapping("/accountSellSoldReport")
public class AccountSellSoldReportController extends BaseController {

    @Autowired
    private IAccountSellService iAccountSellService;

    @ApiOperation("修改出售群")
    @Log(title = "账号销售-已出售账号-修改出售群", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountSellSoldReport:updateSellChatgroupName')")
    @PostMapping("/updateSellChatgroupName")
    public ResultData<Integer> updateSellChatgroupName(@RequestBody AccountSellUpdateChatgroupNameParam param) {
        // check
        if (ObjUtil.isNull(param.getRechargeId())) {
            throw new ServiceException("rechargeId不能为空!");
        }
        if (StrUtil.isBlank(param.getSellChatgroupName())) {
            throw new ServiceException("出售群不能为空!");
        }

        Integer i = this.iAccountSellService.updateSellChatgroupName(param);

        return ResultUtil.success("ok", i);
    }

    @ApiOperation("批量核对")
    @Log(title = "账号销售-已出售账号-批量核对", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountSellSoldReport:batchWriteOff')")
    @PostMapping("/batchWriteOff")
    public ResultData<Integer> batchWriteOff(@RequestBody AccountSellBatchWriteOffParam param) {
        // check
        if (!CollUtil.isNotEmpty(param.getRechargeIdList())) {
            throw new ServiceException("请选择后操作!");
        }
        Integer i = this.iAccountSellService.batchWriteOff(param);
        return ResultUtil.success("ok", i);
    }


    @ApiOperation("已出售账号列表")
    @Log(title = "账号销售-已出售账号-已出售账号列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountSellSoldReport:list')")
    @GetMapping("/getAccountSellSoldReportList")
    public TableDataInfo<AccountSellVo> getAccountSellSoldReportList(AccountSellPendingSaleSearchParam param) {
        startPage();
        List<AccountSellVo> list = this.iAccountSellService.getAccountSellSoldReportList(param);
        return getDataTable(list);
    }


}
