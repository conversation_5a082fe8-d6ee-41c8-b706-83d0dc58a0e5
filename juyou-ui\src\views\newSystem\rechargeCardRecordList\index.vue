<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="账号：" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="充值状态：" prop="status">
        <el-select v-model="queryParams.status" filterable placeholder="请选择充值状态">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in rechargeStatus"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="充值阶段：" prop="chargeStage">
        <el-select v-model="queryParams.chargeStage" filterable placeholder="请选择充值阶段">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in rechargePhase"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in zonrList"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="完成充值时间：" prop="dateRange">
        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="核对状态：" prop="accountZone">
        <el-select v-model="queryParams.writeOffStatus" filterable placeholder="请选择核对状态">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in writeOffStatus"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="剩余等待时长（分）：" label-width="160px" prop="accountZone">
        <el-input
          style="width: 80px"
          v-model="queryParams.surplusWaitDurationStart"
          placeholder="开始"
          clearable
          @keyup.enter.native="handleQuery"
        />
        ~
        <el-input
          style="width: 80px"
          v-model="queryParams.surplusWaitDurationEnd"
          placeholder="结束"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级充值人：" prop="primaryCharger">
        <el-select v-model="queryParams.primaryCharger" filterable placeholder="请选择一级充值人">
          <el-option
            v-for="(item,index) in oneUserList"
            :key="index"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级充值人：" prop="secondaryCharger">
        <el-select v-model="queryParams.secondaryCharger" filterable placeholder="请选择二级充值人">
          <el-option
            v-for="(item,index) in twoUserList"
            :key="index"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属业务员：" prop="idUser">
        <el-input
          v-model="queryParams.idUser"
          placeholder="请输入归属业务员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="float: right">
      <el-button :disabled="!ids.length" type="primary" @click="downloadData">导出</el-button>
      <el-button :disabled="!ids.length" type="primary" @click="batchCheck">批量核对</el-button>
    </div>
    <el-table :row-class-name="tableRowClassName" @selection-change="handleSelectionChange" v-loading="loading" :data="recordList" :summary-method="getSummaries" show-summary>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" :index="table_index" align="center" type="index" width="50"></el-table-column>
      <el-table-column label="核对状态" align="center" prop="accountName" width="70">
        <template slot-scope="scope">
          {{scope.row.writeOffStatus === '1'?'未核对':'已核对'}}
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
          scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
          scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="accountZone" />
      <el-table-column label="充值状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.recharge_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="充值阶段" align="center" prop="chargeStage">
        <template slot-scope="scope">
          {{scope.row.chargeStage === '1'?'一级阶段':'二级阶段'}}
        </template>
      </el-table-column>
      <el-table-column label="剩余等待时长（分）" align="center" prop="surplusWaitDuration" />
      <el-table-column label="ID余额" align="center" prop="rechargeAmt" />
      <el-table-column label="领取人" align="center" prop="receiveUser"/>
      <el-table-column label="成本金额（CNY）" align="center" prop="buyAmt" />
      <el-table-column label="一级充值人" align="center" prop="primaryChargerName" />
      <el-table-column label="二级充值人" align="center" prop="secondaryChargerName" />
      <el-table-column fixed="right" label="操作" align="center" width="145" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status !== '4'" size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status === '4'" size="mini" type="text" @click="handleDetail(scope.row)">查看</el-button>
          <el-button v-if="scope.row.status === '4'" size="mini" type="text" @click="handleRestore(scope.row)">恢复</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"

    />
  </div>
</template>

<script>
import {
  chargingInquiryBatchWriteOff,
  chargingInquiryList,
  chargingInquiryRecovery,
  getAListOfFirstLevelToppers,
  getAListOfSecondaryToppers,
  sellcardRestore
} from '@/api/newSystem/rechargeCard'
  import {getToken} from "@/utils/auth";
  import {getDicts} from "@/api/newSystem/dict/data";
  import Cookies from "js-cookie";
import { listUser } from '@/api/newSystem/user'

  export default {
    name: "RechargeCardRecordList",
    dicts: ['recharge_status'],
    data() {
      return {
        // 日期范围
        dateRange: [],
        oneUserList:[],
        twoUserList:[],
        rechargeStatus:[],
        rechargePhase:[],
        writeOffStatus:[],
        uploadaccFilePath: process.env.VUE_APP_BASE_API + "/newSystem/record/importData",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        ids:[],
        fileList: [],
        //充值记录列表列表
        recordList: [],
        // 遮罩层
        loading: true,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
        },
        zonrList: [],
      };
    },
    activated() {
      if (this.$route.query.status === '4'){
        this.queryParams.status = '4'
      }
      if (this.$route.query.startTime && this.$route.query.endTime){
        this.dateRange = [this.$route.query.startTime,this.$route.query.endTime]
      }
      this.getList()
    },
    created() {
      if (this.$route.query.status === '4'){
        this.queryParams.status = '4'
      }
      if (this.$route.query.startTime && this.$route.query.endTime){
        this.dateRange = [this.$route.query.startTime,this.$route.query.endTime]
      } else {
        this.theLastSevenDays()
      }
      this.into();
    },
    methods: {
      theLastSevenDays() {
        // 七天前
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 7);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        this.dateRange = [`${yesterday} 00:00:00`, `${today} 23:59:59`]
      },
      tableRowClassName({ row }) {
        if (row.hasEdit == 'Y') {
          return "red-row";
        }
        return "";
      },
      table_index(index) {
        return (+this.queryParams.pageNum - 1) * +this.queryParams.pageSize + index + 1
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.acid)
        this.single = selection.length !== 1
        this.multiple = !selection.length
        console.log(this.ids)
      },
      handleDetail(e){
        console.log(e.chargeStage)
        this.$router.push('/sellCardDetail/depositDetails/'+e.acid+'?chargeStage='+e.chargeStage)
      },
      async handleRestore(e){
        this.$modal.loading('恢复中')
        this.$modal.confirm('账号['+e.accountName+']，请确认是否恢复部分充值？').then(function() {
          return chargingInquiryRecovery(e.acid)
        }).then(() => {
          this.$modal.msgSuccess('恢复成功')
          this.$modal.closeLoading()
          this.getList()
        }).catch(function() {
          this.$modal.closeLoading()
        });
      },
      into() {
        getAListOfFirstLevelToppers().then(response => {
          this.oneUserList = response.data;
        })
        getAListOfSecondaryToppers().then(response => {
          this.twoUserList = response.data;
        })
        getDicts("account_zone").then(response => {
          this.zonrList = response.data;
        })
        getDicts("recharge_status").then(response => {
          this.rechargeStatus = response.data;
        })
        getDicts("recharge_phase").then(response => {
          this.rechargePhase = response.data;
        })
        getDicts("writeOff_status").then(response => {
          this.writeOffStatus = response.data;
        })
        this.getList();//获取未生效状态的账号
      },
      handleUpdate(e){
        console.log(e)
        this.$router.push('/applereg/rechargeModification/'+e.acid)
      },
      yesterday() {
        // 昨天
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 1);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        this.$set(this.queryParams, 'executionTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);

        // const date = new Date();
        // const enddate = new Date(date);
        // enddate.setDate(date.getDate() - 1);
        // const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // this.$set(this.queryParams, 'executionTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
      },
      today() {
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        const end = new Date();
        end.setDate(end.getDate() + 1);
        const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
        this.$set(this.queryParams, 'executionTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);

        // const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        // this.$set(this.queryParams, 'executionTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
      },
      downloadData(){
        this.download('/new/manager/chargingInquiry/export', {
          ids:this.ids
        }, `data_${new Date().getTime()}.xlsx`)
      },
      async batchCheck(){
        if (this.ids.length){
          this.$modal.loading('核对中')
          try{
            await chargingInquiryBatchWriteOff({
              acIdList:this.ids
            })
          }catch (e) {
            this.$modal.closeLoading()
            return
          }
          this.$modal.closeLoading()
          this.$modal.msgSuccess('核对成功')
          this.getList()
        }

      },
      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          if (this.dateRange && this.dateRange.length){
            this.queryParams.doneTimeEnd = this.dateRange[1]
            this.queryParams.doneTimeStart = this.dateRange[0]
          }else{
            this.queryParams.doneTimeEnd = ''
            this.queryParams.doneTimeStart = ''
          }
          const request = {
            ...this.queryParams,

          };
          Cookies.set('rechargeCardRecordList',JSON.stringify(this.queryParams));
          const recordList_res = await chargingInquiryList(request);
          if (recordList_res.code === 200) {
            this.recordList = recordList_res.rows;
            this.total = recordList_res.total;
            this.loading = false;
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: false,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
        }
        this.handleQuery();
      },
      // 上传成功回调
      handleUploadSuccess(res) {
        if (res.code === 200) {
          this.$modal.msgSuccess(res.msg || '导入成功！');
          this.getList();//重新刷新列表
        } else {
          this.$modal.msgError(res.msg || '导入失败');
        }
      },
      // 上传失败
      handleUploadError() {
        this.$modal.msgError("导入失败，请重试");
        this.$modal.closeLoading();
      },
      handleChange(file, fileList) {
        this.fileList = fileList;
      },
      // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
      handleRemove(file, fileList) {
        this.fileList = fileList;
        console.info(this.fileList);
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3, 4, 5,6,7,8,11,12] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' ';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
<style lang="scss">
.el-table .red-row {
  background: #ec808d;
}
</style>
