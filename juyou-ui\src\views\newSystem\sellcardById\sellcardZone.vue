<template>
  <div class="app-container">
    <el-button style="width: 150px;height: 50px;font-size: 18px;margin: 20px" type="primary" v-for="item in table" @click="gotoAccount(item.dictLabel)">{{ item.dictValue?'('+item.dictValue+')'+item.dictLabel:item.dictLabel }}</el-button>
  </div>
</template>

<script>
import {listZone} from "@/api/newSystem/sellcard";
import Link from "@/layout/components/Sidebar/Link";

export default {
  name: "accountZone",
  components: {Link},
  data() {
    return {
      table: []
    }
  },
  created() {
    this.into();
  },
  methods: {
    into() {
      listZone('1').then(res => {
        this.table = res.rows;
      })
    },
    gotoAccount(accountZone) {
      this.$router.push({path: '/applereg/sellcardById/'+accountZone})
    }
  }
}
</script>

<style scoped>

</style>
