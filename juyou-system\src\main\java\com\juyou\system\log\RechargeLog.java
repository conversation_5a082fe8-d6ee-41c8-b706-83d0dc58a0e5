package com.juyou.system.log;

import cn.hutool.core.util.StrUtil;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.common.utils.spring.SpringUtils;
import com.juyou.system.constants.LogTypeConstant;
import com.juyou.system.domain.AccountRechargeLog;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.service.IAccountRechargeLogService;

import java.util.HashMap;
import java.util.Map;

public class RechargeLog {

    private static IAccountRechargeLogService accountRechargeLogService = SpringUtils.getBean(IAccountRechargeLogService.class);

    public static void insertRecharge(AccountRechargeDetailVo oldAcc, AccountRechargeDetailVo newAcc, String type) {
        try {
            String createUser = SecurityUtils.getUsername();
            Long deptId = SecurityUtils.getDeptId();
            AccountRechargeLog accountRechargeLog = new AccountRechargeLog();
            accountRechargeLog.setAcid(Long.valueOf(newAcc.getAcid()));
            accountRechargeLog.setCreateUser(createUser);
            accountRechargeLog.setDeptId(deptId);
            accountRechargeLog.setCreateType(type);
            StringBuffer sb = new StringBuffer();
            if (LogTypeConstant.UPDATE.equals(type)) {
                if (oldAcc.getIdUser() != null && !oldAcc.getIdUser().equals(newAcc.getIdUser())) {
                    sb.append("ID业务员由" + oldAcc.getIdUser() + "变更为" + newAcc.getIdUser() + ";");
                }
                Map<String, GiftCardRechargeRecord> params = new HashMap<>();
                oldAcc.getGiftCardRechargeRecordList().forEach(oldGiftCard -> {
                    params.put(oldGiftCard.getGiftCard(), oldGiftCard);
                });
                Map<String, GiftCardRechargeRecord> paramsNew = new HashMap<>();
                newAcc.getGiftCardRechargeRecordList().forEach(newGiftCard -> {
                    paramsNew.put(newGiftCard.getGiftCard(), newGiftCard);
                    GiftCardRechargeRecord giftCard = params.get(newGiftCard.getGiftCard());
                    if (giftCard == null) {
                        sb.append("新增充值礼品卡" + newGiftCard.getGiftCard());
                    }
                    if (newGiftCard.getFaceValue() != null && !newGiftCard.getFaceValue().equals(giftCard.getFaceValue())) {
                        sb.append("充值礼品卡" + newGiftCard.getGiftCard() + "面值由" + giftCard.getFaceValue() + "变更为" + newGiftCard.getFaceValue() + ";");
                    }
                    if (newGiftCard.getBalance() != null && !newGiftCard.getBuyPrice().equals(giftCard.getBuyPrice())) {
                        sb.append("充值礼品卡" + newGiftCard.getGiftCard() + "汇率由" + giftCard.getBuyPrice() + "变更为" + newGiftCard.getBuyPrice() + ";");
                    }
                    if (newGiftCard.getBalance() != null && !newGiftCard.getSourceGroup().equals(giftCard.getSourceGroup())) {
                        sb.append("充值礼品卡" + newGiftCard.getGiftCard() + "来源群由" + giftCard.getSourceGroup() + "变更为" + newGiftCard.getSourceGroup() + ";");
                    }
                });
                params.forEach((k, v) -> {
                    if (paramsNew.get(k) == null) {
                        sb.append("删除充值礼品卡" + v.getGiftCard());
                    }
                });
                accountRechargeLog.setCreateReason(sb.toString());
            }
            accountRechargeLog.setSellOrRecharge("充值");
            accountRechargeLogService.insertAccountRechargeLog(accountRechargeLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void insertSell(AccountSell oldAcc, AccountSell newAcc,String type) {
        try {
            String createUser = SecurityUtils.getUsername();
            Long deptId = SecurityUtils.getDeptId();
            AccountRechargeLog accountRechargeLog = new AccountRechargeLog();
            accountRechargeLog.setAcid(Long.valueOf(newAcc.getAcid()));
            accountRechargeLog.setCreateUser(createUser);
            accountRechargeLog.setDeptId(deptId);
            accountRechargeLog.setCreateType(type);
            StringBuffer sb = new StringBuffer();
            if (LogTypeConstant.UPDATE.equals(type)) {
                if (oldAcc.getSellChatgroupName() != null && !oldAcc.getSellChatgroupName().equals(newAcc.getSellChatgroupName())) {
                    sb.append("出售群由" + oldAcc.getSellChatgroupName() + "变更为" + newAcc.getSellChatgroupName() + ";");
                }
                if (oldAcc.getSellPrice() != null && !oldAcc.getSellPrice().equals(newAcc.getSellPrice())) {
                    sb.append("出售单价由" + oldAcc.getSellPrice() + "变更为" + newAcc.getSellPrice() + ";");
                }
                if (oldAcc.getCustName() != null && !oldAcc.getCustName().equals(newAcc.getCustName())) {
                    sb.append("售卖人由" + oldAcc.getCustName() + "变更为" + newAcc.getCustName() + ";");
                }
            }
            accountRechargeLog.setCreateReason(sb.toString());
            accountRechargeLog.setSellOrRecharge("出售");
            accountRechargeLogService.insertAccountRechargeLog(accountRechargeLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
