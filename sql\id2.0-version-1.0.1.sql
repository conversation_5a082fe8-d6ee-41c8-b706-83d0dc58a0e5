
-- id2.0是否退回添加退回标识
ALTER TABLE two_account_recharge ADD has_back varchar(255) NULL COMMENT '是否退回:Y N';
ALTER TABLE two_account_recharge ADD comment varchar(255) NULL COMMENT '备注';

ALTER TABLE two_account_recharge ADD completed_time_minutes_num double NULL COMMENT '完成时间分钟数(单位分钟)';

ALTER TABLE two_account_sell ADD has_edit varchar(255) NULL COMMENT '是否修改 Y:N';

INSERT INTO `juyou_tool`.`sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (101, '账号充值定时任务', 'DEFAULT', 'accountRechargeTask.run', '0 0/5 * * * ? ', '3', '1', '0', 'admin', '2023-09-07 16:10:24', 'admin', '2023-09-11 17:46:58', '');

-- id2-账号充值-一级等待分钟
-- id2-账号充值-二级等待分钟
INSERT INTO `juyou_tool`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (106, 'id2-账号充值-一级等待分钟', 'id2.account.recharge.one.level.pending.minutes', '4320', 'N', 'admin', '2024-07-23 03:54:33', 'admin', '2024-07-23 03:54:37', 'id2-账号充值-一级等待分钟');
INSERT INTO `juyou_tool`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (107, 'id2-账号充值-二级等待分钟', 'id2.account.recharge.two.level.pending.minutes', '4320', 'N', 'admin', '2024-07-23 03:55:16', '', NULL, 'id2-账号充值-二级等待分钟');
