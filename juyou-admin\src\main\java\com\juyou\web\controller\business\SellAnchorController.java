package com.juyou.web.controller.business;

import cn.hutool.core.util.ObjUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.SellAnchorVo;
import com.juyou.system.params.AccountRechargePageParam;
import com.juyou.system.params.AccountSellNotEffectiveParam;
import com.juyou.system.params.AccountSellPageParam;
import com.juyou.system.service.IAccountSellService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "903出售", tags = "903出售")
@RestController
@RequestMapping("/system/sellAnchor")
public class SellAnchorController extends BaseController {

    @Autowired
    private IAccountSellService accountSellService;

    @ApiOperation("903出售-列表")
    @PreAuthorize("@ss.hasPermi('system:sellAnchor:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountSell> list(SellAnchorVo param) {
        startPage();
        List<AccountSell> list = accountSellService.selectAccountSellListByAnchor(param);
        return getDataTable(list);
    }

    @ApiOperation("903出售-使用")
    @PreAuthorize("@ss.hasPermi('system:sellAnchor:batch')")
    @PutMapping("/batch")
    public ResultData<Integer> batch(@RequestBody AccountSellNotEffectiveParam ids) {
        return ResultUtil.success(accountSellService.updateAccountSellByAnchor(ids.getRechargeIds()));
    }

    @ApiOperation("903出售-恢复")
    @PreAuthorize("@ss.hasPermi('system:sellAnchor:recovery')")
    @PutMapping("/recovery/{id}")
    public ResultData<Integer> recovery(@PathVariable("id") Integer id) {
        // check
        if(ObjUtil.isNull(id)){
            throw new ServiceException("id不能为空!");
        }
        return ResultUtil.success(accountSellService.updateAccountSellByAnchorRecovery(id));
    }

}
