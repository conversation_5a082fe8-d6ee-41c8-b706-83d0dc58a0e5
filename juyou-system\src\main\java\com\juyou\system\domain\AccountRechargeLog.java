package com.juyou.system.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 two_account_recharge_log
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
@Data
@ApiModel(value = "AccountRechargeLog")
public class AccountRechargeLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 全局唯一 */
    @ApiModelProperty("id")
    private Long id;

    /** 账号id，全局唯一 */
    @Excel(name = "账号id，全局唯一")
    @ApiModelProperty("账号id，全局唯一")
    private Long acid;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private String createUser;

    /** 部门id */
    @Excel(name = "部门id")
    @ApiModelProperty("部门id")
    private Long deptId;

    /** 部门id */
    @Excel(name = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptName;

    /** 操作类型：1 部分充值 2 完成充值 3 退回 4 出售 5 修改 6 恢复 7 作废 */
    @Excel(name = "操作类型：1 部分充值 2 完成充值 3 退回 4 出售 5 修改 6 恢复 7 作废")
    @ApiModelProperty("操作类型：1 部分充值 2 完成充值 3 退回 4 出售 5 修改 6 恢复 7 作废")
    private String createType;


    /** 操作内容 */
    @Excel(name = "操作内容")
    @ApiModelProperty("操作内容")
    private String createReason;

    @Excel(name = "出售或充值")
    @ApiModelProperty("出售或充值")
    private String sellOrRecharge;


    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAcid(Long acid)
    {
        this.acid = acid;
    }

    public Long getAcid()
    {
        return acid;
    }
    public void setCreateUser(String createUser)
    {
        this.createUser = createUser;
    }

    public String getCreateUser()
    {
        return createUser;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setCreateType(String createType)
    {
        this.createType = createType;
    }

    public String getCreateType()
    {
        return createType;
    }

    public void setCreateReason(String createReason)
    {
        this.createReason = createReason;
    }

    public String getCreateReason()
    {
        return createReason;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("acid", getAcid())
                .append("createTime", getCreateTime())
                .append("createUser", getCreateUser())
                .append("deptId", getDeptId())
                .append("createType", getCreateType())
                .append("createReason", getCreateReason())
                .toString();
    }
}
