package com.juyou.system.utils;

import cn.hutool.core.util.ObjectUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 计算工具类
 */
public class MathUtil {


    /**
     * 取两个列表的差值
      * @param list1
     * @param list2
     * @return
     */
    public static List<Long> differenceList(List<Long> list1, List<Long> list2){
        // 取差值
        List<Long> difference = list1.stream()
                .filter(item -> !list2.contains(item))
                .collect(Collectors.toList());
        return difference;
    }

    /**
     * 获取去掉多余的小数零
     * @param valStr
     * @return
     */
    public static BigDecimal getRemoveScaleZero(String valStr) {
        if (ObjectUtil.isNotNull(valStr)) {
            BigDecimal val = new BigDecimal(valStr);
            BigDecimal val1 = val.stripTrailingZeros();
            return val1;
        }
        return null;
    }

    /**
     * 获取去掉多余的小数零
     * @param val
     * @return
     */
    public static BigDecimal getRemoveScaleZero(BigDecimal val) {
        if (ObjectUtil.isNotNull(val)) {
            BigDecimal val1 = val.stripTrailingZeros();
            return val1;
        }
        return null;
    }

    /**
     * 直接取整
     *
     * @param value
     * @return
     */
    public static BigDecimal getDownScale(BigDecimal value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return value.setScale(0, RoundingMode.DOWN);
    }

    public static BigDecimal getScale(BigDecimal value,int scale){
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return value.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 保留小数位
     *
     * @param value
     * @return
     */
    public static BigDecimal getNgnScale(BigDecimal value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return value.setScale(0, RoundingMode.DOWN);
    }

    /**
     * 获取比例：current / previous * 100
     * @param current
     * @param previous
     * @return
     */
    public static BigDecimal getRate(Integer current, Integer previous){
        if (ObjectUtil.isNull(previous) || previous.equals(0)){
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(current).divide(BigDecimal.valueOf(previous),10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.CEILING);
    }

    /**
     * 获取比例：current / previous * 100
     * @param current
     * @param previous
     * @return
     */
    public static BigDecimal getRate(BigDecimal current, BigDecimal previous){
        if (ObjectUtil.isNull(previous) || previous.equals(BigDecimal.ZERO)){
            return BigDecimal.ZERO;
        }
        return current.divide(previous,10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.CEILING);
    }

    /**
     * 获取环比率
     *
     * @param current
     * @param previous
     * @return
     */
    public static BigDecimal getChainReaction(Integer current, Integer previous) {
        // 环比增长率=(本期数-上期数) / 上期数 * 100%
        return getChainReaction(BigDecimal.valueOf(current), BigDecimal.valueOf(previous));
    }

    /**
     * 获取环比率
     *
     * @param current
     * @param previous
     * @return
     */
    public static BigDecimal getChainReaction(BigDecimal current, BigDecimal previous) {
        current = ObjectUtil.isNull(current) ? BigDecimal.ZERO : current;
        previous = ObjectUtil.isNull(previous) ? BigDecimal.ZERO : previous;

        if (BigDecimal.ZERO.compareTo(current) == 0 && BigDecimal.ZERO.compareTo(previous) == 0) {
            return BigDecimal.valueOf(0).setScale(2, RoundingMode.HALF_UP);
        }
        // 环比增长率=(本期数-上期数) / 上期数 * 100%
        if (BigDecimal.ZERO.compareTo(previous) == 0) {
            return BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP);
        }
        return current.subtract(previous).divide(previous, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.CEILING);
    }


}

