<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.AccountRechargeLogMapper">

    <resultMap type="AccountRechargeLog" id="AccountRechargeLogResult">
        <result property="id"    column="id"    />
        <result property="acid"    column="acid"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createType"    column="create_type"    />
        <result property="createReason"    column="create_reason"    />
        <result property="deptName"    column="dept_name"    />
        <result property="sellOrRecharge"    column="sell_or_recharge"    />
    </resultMap>

    <sql id="selectAccountRechargeLogVo">
        select id, acid, create_time, create_user, dept_id, create_type, create_reason,sell_or_recharge from two_account_recharge_log
    </sql>

    <select id="selectAccountRechargeLogList" parameterType="AccountRechargeLog" resultMap="AccountRechargeLogResult">
        select a.id, a.acid, a.create_time, a.create_user, a.dept_id, a.create_type, a.create_reason,d.dept_name ,a.sell_or_recharge
        from two_account_recharge_log a
        left join sys_dept d on a.dept_id = d.dept_id
        <where>
            <if test="acid != null "> and a.acid = #{acid}</if>
            <if test="createUser != null  and createUser != ''"> and a.create_user = #{createUser}</if>
            <if test="deptId != null "> and a.dept_id = #{deptId}</if>
            <if test="createType != null  and createType != ''"> and a.create_type = #{createType}</if>
            <if test="createReason != null  and createReason != ''"> and a.create_reason = #{createReason}</if>
        </where>
    </select>

    <select id="selectAccountRechargeLogById" parameterType="Long" resultMap="AccountRechargeLogResult">
        <include refid="selectAccountRechargeLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertAccountRechargeLog" parameterType="AccountRechargeLog" useGeneratedKeys="true" keyProperty="id">
        insert into two_account_recharge_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="acid != null">acid,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createType != null">create_type,</if>
            <if test="createReason != null">create_reason,</if>
            <if test="sellOrRecharge != null">sell_or_recharge,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="acid != null">#{acid},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createType != null">#{createType},</if>
            <if test="createReason != null">#{createReason},</if>
            <if test="sellOrRecharge != null">#{sellOrRecharge},</if>
        </trim>
    </insert>

    <update id="updateAccountRechargeLog" parameterType="AccountRechargeLog">
        update two_account_recharge_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="acid != null">acid = #{acid},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createType != null">create_type = #{createType},</if>
            <if test="createReason != null">create_reason = #{createReason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountRechargeLogById" parameterType="Long">
        delete from two_account_recharge_log where id = #{id}
    </delete>

    <delete id="deleteAccountRechargeLogByIds" parameterType="String">
        delete from two_account_recharge_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
