package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号出售作废详情-param
 */
@Data
@ApiModel("SellCancelAmtDetailParam")
public class SellCancelAmtDetailParam implements Serializable {

    private static final long serialVersionUID = 5072899679980099849L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("作废开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelStartDate;

    @ApiModelProperty("作废结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelEndDate;

}
