package com.juyou.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juyou.common.annotation.DataScope;
import com.juyou.common.constant.SysEnable;
import com.juyou.common.core.domain.entity.SysMenu;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.DateUtils;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.constants.LogTypeConstant;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.*;
import com.juyou.system.enums.AccountRechArgeChargeStageEnum;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.enums.AccountSellEnum;
import com.juyou.system.enums.AccountSellWriteOffStatusEnum;
import com.juyou.system.log.RechargeLog;
import com.juyou.system.mapper.AccountRechargeMapper;
import com.juyou.system.mapper.AccountSellMapper;
import com.juyou.system.mapper.SysMenuMapper;
import com.juyou.system.params.*;
import com.juyou.system.service.*;
import com.juyou.system.utils.JuYouBusinessUtil;
import com.juyou.system.utils.JuYouDateUtil;
import com.juyou.system.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账号充值Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AccountRechargeServiceImpl extends ServiceImpl<AccountRechargeMapper, AccountRecharge> implements IAccountRechargeService {

    @Autowired
    private AccountRechargeMapper accountRechargeMapper;
    @Autowired
    private AccountSellMapper accountSellMapper;
    @Autowired
    private IAccountSellService accountSellService;
    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysConfigService iSysConfigService;

    /**
     * 获取账号出售待生效分钟数配置没有获取到
     *
     * @return
     */
    private Integer getAccountSellPendingMinutes() {
        //查询参数配置信息
        String accountSellPendingMinutesStr = this.iSysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES);
        if (StrUtil.isBlank(accountSellPendingMinutesStr)) {
            throw new ServiceException("账号出售待生效分钟数配置没有获取到!");
        }
        log.info("获取账号出售待生效分钟:{}", accountSellPendingMinutesStr);
        return Integer.valueOf(accountSellPendingMinutesStr);
    }

    @Override
    public boolean updateNotEffectiveList(Integer[] ids) {
        // 默认生效开始时间
        Date pendingStartDate = DateUtil.parseDateTime("2018-01-01 00:00:00");
        List<Integer> idList = Arrays.stream(ids).collect(Collectors.toList());
        List<AccountRecharge> list = this.listByIds(idList);
        boolean b = false;
        if(CollUtil.isNotEmpty(list)) {
            for (AccountRecharge item : list) {
                item.setPendingStartDate(pendingStartDate);
            }
            b = this.saveOrUpdateBatch(list);
        }
        // 手动触发一级库到时间自动放入二级库
        this.pendingMinutesPutTwoChargeStage();

        return b;
    }

    @Override
    public List<AccountRechargeNotEffectiveListVo> notEffectiveList(AccountRechargeNotEffectiveParam param) {
        List<AccountRechargeNotEffectiveListVo> voList = this.accountRechargeMapper.notEffectiveList(param);
        if(CollUtil.isNotEmpty(voList)){
            for (AccountRechargeNotEffectiveListVo item : voList) {
                if (ObjUtil.isNotNull(item.getPrimaryCharger())) {
                    SysUser sysUser = this.userService.selectUserById(item.getPrimaryCharger());
                    item.setPrimaryChargerName(sysUser.getUserName());
                }
                if (ObjUtil.isNotNull(item.getSecondaryCharger())) {
                    SysUser sysUser = this.userService.selectUserById(item.getSecondaryCharger());
                    item.setSecondaryChargerName(sysUser.getUserName());
                }
            }
        }
        return voList;
    }

    @Override
    public boolean pendingMinutesPutTwoChargeStage() {

        String pendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(pendingMinutes)){
            log.info("id2-账号充值-一级等待分钟没有配置!");
            return false;
        }
        Integer pendingMinutesInt = Integer.valueOf(pendingMinutes);

        // 要取出一级指定等待到时间的完成充值的账号
        List<AccountRecharge> list = this.accountRechargeMapper.pendingMinutesPutTwoChargeStageList(pendingMinutesInt);

        // 修改查询出来的账号充值列表，修改为二级库
        boolean b = false;
        if(CollUtil.isNotEmpty(list)){
            list.stream().forEach(item->{
                item.setChargeStage(AccountRechArgeChargeStageEnum.LEVEL_2.getCode());
                item.setStatus(AccountRechargeEnum.STATUS_0.getCode());
                item.setReceiveUserId(null);
                item.setReceiveUser(null);
                //操作日志自动放入二级库
                AccountRechargeDetailVo newVo = this.getDetail(item.getAcid());
                RechargeLog.insertRecharge(newVo, newVo, LogTypeConstant.AUTO_POST_TWO_STORAGE);
            });
            b = this.saveOrUpdateBatch(list);


            // 放入二级库的账号需要删掉账号出售账号
            {
                List<Integer> accountRechargeIdList = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
                Integer deleteCount = this.accountSellService.deleteByAcidList(accountRechargeIdList);
                log.info("自动放入二级库-需要删除账号出售的数据删除成功!删除条数:{} 数据：{}", deleteCount , accountRechargeIdList);

            }
        }



        return b;
    }

    @Override
    public Boolean editRemark(AccountRechargeEditRemarkParam param) {

        AccountRecharge recharge = this.getById(param.getAcid());

        recharge.setComment(param.getComment());

        boolean b = this.updateById(recharge);

        return b;
    }

    @Override
    public boolean chargingInquiryEdit(AccountRechargeParam param) {
        AccountRechargeDetailVo old = this.getDetail(param.getAcid());

        List<AccountRechargeParam.RechargeGiftCardRechargeRecordListVo> cardList = param.getCardList();

        // 修改是否做过充值修改和出售修改的标识
        AccountRecharge reg = this.getById(param.getAcid());
        String accountName = reg.getAccountName();

        // 先删除全部账号下的礼品卡
        int deleteGiftCardCount = this.iGiftCardRechargeRecordService.deleteByAccountName(reg.getAccountName());
        log.info("删除账号：{} 下的礼品卡删除成功数量:{}", reg.getAccountName(), deleteGiftCardCount);

        // 1、添加或删除卡密；修改面值或者汇率，需要同步ID余额；
        // 2、如果ID被出售，需要同步修改出售金额。

        // 添加礼品卡列表
        this.iGiftCardRechargeRecordService.batchInsert(cardList, reg, param.getCurrentUsername());

        BigDecimal faceValue = this.iGiftCardRechargeRecordService.getFaceValue(accountName);
        BigDecimal buyAmt = this.iGiftCardRechargeRecordService.getBuyAmt(accountName);

        reg.setRechargeAmt(faceValue.doubleValue());
        reg.setBuyAmt(buyAmt.doubleValue());
        reg.setIdUser(param.getIdUser());
        reg.setHasEdit(SysEnable.EANBLE_Y);

        // 更新账号充值
        boolean b = this.updateById(reg);
//        int i = this.recharge(param);

        // 获取出售账号对象
        AccountSell sell = this.accountSellService.selectByAcid(param.getAcid());
        if (ObjUtil.isNotNull(sell)) {
            sell.setCardBalance(reg.getRechargeAmt());//充值金额
            sell.setBuyPrice(reg.getBuyPrice());//成本单价
            sell.setBuyAmt(reg.getBuyAmt());//成本
            return this.accountSellService.updateById(sell);
        }


        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(param.getAcid());
            RechargeLog.insertRecharge(old, newVo, LogTypeConstant.UPDATE);
        }

        return b;
    }

    @Override
    public List<ChargingInquiryListVo> chargingInquiryList(ChargingInquirySearchParam param) {
        Integer accountSellPendingMinutes = getAccountSellPendingMinutes();
        param.setAccountSellPendingMinutes(accountSellPendingMinutes);
        List<ChargingInquiryListVo> voList = this.accountRechargeMapper.chargingInquiryList(param);
        if (CollUtil.isNotEmpty(voList)) {
            for (ChargingInquiryListVo item : voList) {
                if (ObjUtil.isNotNull(item.getSurplusWaitDuration())) {
                    if (item.getSurplusWaitDuration().intValue() < 0) {
                        item.setSurplusWaitDuration(0);
                    }
                }
            }
        }
        return voList;
    }

    @Override
    public boolean recovery(Integer acid) {
        // 充值状态改：部分充值，由上次作废的人继续完成充值
        AccountRechargeDetailVo old = this.getDetail(acid);

        AccountRecharge accountRecharge = this.getById(acid);
        accountRecharge.setStatus(AccountRechargeEnum.STATUS_2.getCode());
        boolean b = this.updateById(accountRecharge);

        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(acid);
            RechargeLog.insertRecharge(old, newVo, LogTypeConstant.RECOVERY);
        }
        return b;
    }

    @Override
    public AccountRechargeDetailVo getDetail(Integer acid) {

        AccountRecharge accountRecharge = this.selectAccountRechargeByAcid(acid);
        GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
        rechargeRecord.setAccount(accountRecharge.getAccountName());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(rechargeRecord);

        AccountRechargeDetailVo vo = new AccountRechargeDetailVo();
        BeanUtil.copyProperties(accountRecharge, vo);
        vo.setGiftCardRechargeRecordList(list);

        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getRechargeAmt()));

        if (ObjUtil.isNotNull(accountRecharge.getPrimaryCharger())) {
            SysUser sysUser = this.userService.selectUserById(accountRecharge.getPrimaryCharger());
            vo.setPrimaryChargerName(sysUser.getUserName());
        }
        if (ObjUtil.isNotNull(accountRecharge.getSecondaryCharger())) {
            SysUser sysUser = this.userService.selectUserById(accountRecharge.getSecondaryCharger());
            vo.setSecondaryChargerName(sysUser.getUserName());
        }

        return vo;
    }

    @Override
    public List<AccountSellResidualAccountVoList> findMyResidualAccountList(AccountRechargeResidualParam param) {

        List<AccountSellResidualAccountVoList> voList = this.accountRechargeMapper.findMyResidualAccountList(param);

        return voList;
    }

    @Override
    public List<AccountSellResidualAccountVoList> findTwoLevelResidualAccountList(AccountRechargeResidualParam param) {
        // check
        List<AccountSellResidualAccountVoList> voList = this.accountRechargeMapper.findTwoLevelResidualAccountList(param);
        return voList;
    }

    @Override
    public List<AccountSellResidualAccountVoList> findOneLevelResidualAccountList(AccountRechargeResidualParam param) {
        List<AccountSellResidualAccountVoList> voList = this.accountRechargeMapper.findOneLevelResidualAccountList(param);
        return voList;
    }

    @Override
    public Integer countByStatus(String status, String accountZone, String idType) {
        return this.accountRechargeMapper.countByStatus(status, accountZone, idType);
    }

    @Override
    public AccountRechargeDataPanelVo getDataPanel(AccountRechargeDataPanelParam param) {

        AccountRechargeDataPanelVo vo = new AccountRechargeDataPanelVo();

        BigDecimal AccountRechargeDataPanelVo = this.getTodayRechargeAmount(param.getLoginUserId(), param.getAccountZone(), null);
        vo.setTodayRechargeAmount(AccountRechargeDataPanelVo);

        BigDecimal rate = this.getMyRechargeCancelRate(param.getLoginUserId());
        vo.setRechargeCancelRate(MathUtil.getScale(rate, 2));

        return vo;
    }

    @Override
    public BigDecimal getMyRechargeCancelRate(Long userId) {
        ClassesDateRangeVo vo = JuYouDateUtil.getClassesBetweenDate();
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        BigDecimal rate = this.accountRechargeMapper.getMyRechargeCancelRate(userId, startDate, endDate);
        return rate;
    }


    /**
     * 获取白班开始时间
     *
     * @param date
     * @return
     */
    private static Date getDayShiftStartDate(Date date) {
        String fDate = DateUtil.formatDate(date);
        String csDate = fDate + " 10:00:00";
        return DateUtil.parseDateTime(csDate);
    }

    /**
     * 获取晚班结束时间
     *
     * @param date
     * @return
     */
    private static Date getDayShiftEndDate(Date date) {
        String fDate = DateUtil.formatDate(date);
        String csDate = fDate + " 21:59:59";
        return DateUtil.parseDateTime(csDate);
    }

    /**
     * 获取晚班开始时间
     *
     * @param date
     * @return
     */
    private static Date getNightShiftStartDate(Date date) {
        String fDate = DateUtil.formatDate(date);
        String csDate = fDate + " 22:00:00";
        return DateUtil.parseDateTime(csDate);
    }

    @Override
    public BigDecimal getTodayRechargeAmount(Long userId, String accountZone, String idType) {
        ClassesDateRangeVo vo = JuYouDateUtil.getClassesBetweenDate();
        return this.accountRechargeMapper.getTodayRechargeAmount(userId, vo.getStartDate(), vo.getEndDate(), accountZone, idType);
    }

    @Override
    public int transferAccount(AccountTransferAccountParam param) {

        // 自己的待充值、部分充值的账号转给其他人
        List<AccountRecharge> list = this.accountRechargeMapper.selectByReceiveUserAndStatus(param.getCurrentUserId(), Arrays.asList(AccountRechargeEnum.STATUS_1.getCode(), AccountRechargeEnum.STATUS_2.getCode()));
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            return this.accountRechargeMapper.batchUpdateReceiveUserByIds(ids, param.getUserName(), param.getUserId());
        }

        throw new ServiceException("该账号名下没有待充值和部分充值的数据！");
    }

    /**
     * 查询账号充值
     *
     * @param acid 账号充值主键
     * @return 账号充值
     */
    @Override
    public AccountRecharge selectAccountRechargeByAcid(Integer acid) {
        return accountRechargeMapper.selectAccountRechargeByAcid(acid);
    }

    @Override
    public int receiveAccount(AccountRechargeReceiveParam param) {

        Integer count = param.getCount();
        BigDecimal value = param.getValue();
        String accountZone = param.getAccountZone();
        String chargeStage = param.getChargeStage();
        Long loginUserId = param.getLoginUserId();
        String loginUsername = param.getLoginUserName();
        Long deptId = param.getDeptId();
        Integer onePendingMinutes = param.getOnePendingMinutes();
        Integer twoPendingMinutes = param.getTwoPendingMinutes();

        // 可领取的数量
        String claimableNumStr = sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_ECHARGE_ONE);
        int claimableNum = Integer.valueOf(claimableNumStr);

        // 未完成充值（待充值和部分充值）的账号不能超过10个账号
        int countExist = this.accountRechargeMapper.countByReceiveUserIdAndStatusList(loginUserId,
                Arrays.asList(AccountRechargeEnum.STATUS_1.getCode(), AccountRechargeEnum.STATUS_2.getCode()));
        if (countExist >= claimableNum) {
            throw new ServiceException("未完成充值（待充值和部分充值）的账号不能超过" + claimableNum + "个账号！");
        }

        // 可领取的数量 - 存在的未完成充值数 = 可领取的数量
        int claimableCount = claimableNum - countExist;

        // 实际领取数量
        int realClaimableCount = 0;
        if (count > claimableCount) {
            realClaimableCount = claimableCount;
        } else {
            realClaimableCount = count;
        }

        if (realClaimableCount == 0) {
            return 0;
        }

        // 更新指定数量的未领取为代充值状态
        List<AccountRecharge> list = null;
        switch (chargeStage) {
            case "1":
                list = this.accountRechargeMapper.selectOneLevelAccount(realClaimableCount, accountZone, value);
                break;
            case "2":
                list = this.accountRechargeMapper.selectTwoLevelAccount(realClaimableCount, accountZone, value, onePendingMinutes);
                break;
            case "3":
                list = this.accountRechargeMapper.selectMyLevelAccount(realClaimableCount, loginUserId, accountZone, value, twoPendingMinutes);
                break;
        }

        log.info("待领取账号列表:{}", CollUtil.isNotEmpty(list)? JSONUtil.toJsonPrettyStr(list) : list);

        int result = 0;
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
             result = this.accountRechargeMapper.batchUpdateAccountRechargeStatus(ids, AccountRechargeEnum.STATUS_1.getCode(), loginUsername, new Date(),
                    loginUserId, deptId);
        }

        // 我的领取-需要删除账号出售的数据
        if ("3".equals(chargeStage)) {
            if (CollUtil.isNotEmpty(list)) {
                List<Integer> accountRechargeIdList = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
                Integer deleteCount = this.accountSellService.deleteByAcidList(accountRechargeIdList);
                log.info("我的领取-需要删除账号出售的数据删除成功!删除条数:{} 数据：{}", deleteCount , accountRechargeIdList);
            }
        }

        /*List<AccountRecharge> list = this.accountRechargeMapper.selectTopByStatus(AccountRechargeEnum.STATUS_0.getStatus(),
                realClaimableCount, accountZone, idType);
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            return this.accountRechargeMapper.batchUpdateAccountRechargeStatus(ids, AccountRechargeEnum.STATUS_1.getStatus(), userName, new Date(),
                    userId, deptId);
        }*/

        return result;
    }

    @Override
    public int receiveAccountTwo(AccountRechargeReceiveParam param) {

        Integer count = param.getCount();
        BigDecimal value = param.getValue();
        String accountZone = param.getAccountZone();
        String chargeStage = param.getChargeStage();
        Long loginUserId = param.getLoginUserId();
        String loginUsername = param.getLoginUserName();
        Long deptId = param.getDeptId();
        Integer onePendingMinutes = param.getOnePendingMinutes();
        Integer twoPendingMinutes = param.getTwoPendingMinutes();

        // 可领取的数量
        String claimableNumStr = sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_ECHARGE_TWO);
        int claimableNum = Integer.valueOf(claimableNumStr);

        // 未完成充值（待充值和部分充值）的账号不能超过10个账号
        int countExist = this.accountRechargeMapper.countByReceiveUserIdAndStatusList(loginUserId,
                Arrays.asList(AccountRechargeEnum.STATUS_1.getCode(), AccountRechargeEnum.STATUS_2.getCode()));
        if (countExist >= claimableNum) {
            throw new ServiceException("未完成充值（待充值和部分充值）的账号不能超过" + claimableNum + "个账号！");
        }

        // 可领取的数量 - 存在的未完成充值数 = 可领取的数量
        int claimableCount = claimableNum - countExist;

        // 实际领取数量
        int realClaimableCount = 0;
        if (count > claimableCount) {
            realClaimableCount = claimableCount;
        } else {
            realClaimableCount = count;
        }

        if (realClaimableCount == 0) {
            return 0;
        }

        // 更新指定数量的未领取为代充值状态
        List<AccountRecharge> list = null;
        switch (chargeStage) {
            case "1":
                list = this.accountRechargeMapper.selectOneLevelAccount(realClaimableCount, accountZone, value);
                break;
            case "2":
                list = this.accountRechargeMapper.selectTwoLevelAccount(realClaimableCount, accountZone, value, onePendingMinutes);
                break;
            case "3":
                list = this.accountRechargeMapper.selectMyLevelAccount(realClaimableCount, loginUserId, accountZone, value, twoPendingMinutes);
                break;
        }

        int result = 0;
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            result = this.accountRechargeMapper.batchUpdateAccountRechargeStatusTwo(ids, AccountRechargeEnum.STATUS_1.getCode(), loginUsername, new Date(),
                    loginUserId, deptId);
        }

        // 我的领取-需要删除账号出售的数据
        if ("3".equals(chargeStage)) {
            if (CollUtil.isNotEmpty(list)) {
                List<Integer> accountRechargeIdList = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
                Integer deleteCount = this.accountSellService.deleteByAcidList(accountRechargeIdList);
                log.info("我的领取-需要删除账号出售的数据删除成功!删除条数:{} 数据：{}", deleteCount , accountRechargeIdList);
            }
        }

        /*List<AccountRecharge> list = this.accountRechargeMapper.selectTopByStatus(AccountRechargeEnum.STATUS_0.getStatus(),
                realClaimableCount, accountZone, idType);
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            return this.accountRechargeMapper.batchUpdateAccountRechargeStatus(ids, AccountRechargeEnum.STATUS_1.getStatus(), userName, new Date(),
                    userId, deptId);
        }*/

        return result;
    }

    @Override
    public int receiveAccount(int count, Long userId, String userName, Long deptId, String accountZone, String idType) {

        // 可领取的数量
        int claimableNum = ConfigConstant.MAX_RECEIVE_COUNT;

        // 未完成充值（待充值和部分充值）的账号不能超过10个账号
        int countExist = this.accountRechargeMapper.countByReceiveUserIdAndStatusList(userId,
                Arrays.asList(AccountRechargeEnum.STATUS_1.getCode(), AccountRechargeEnum.STATUS_2.getCode()));
        if (countExist >= claimableNum) {
            throw new ServiceException("未完成充值（待充值和部分充值）的账号不能超过30个账号！");
        }

        // 可领取的数量 - 存在的未完成充值数 = 可领取的数量
        int claimableCount = claimableNum - countExist;

        // 实际领取数量
        int realClaimableCount = 0;
        if (count > claimableCount) {
            realClaimableCount = claimableCount;
        } else {
            realClaimableCount = count;
        }

        if (realClaimableCount == 0) {
            return 0;
        }

        // 更新指定数量的未领取为代充值状态
        List<AccountRecharge> list = this.accountRechargeMapper.selectTopByStatus(AccountRechargeEnum.STATUS_0.getCode(),
                realClaimableCount, accountZone, idType);
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            return this.accountRechargeMapper.batchUpdateAccountRechargeStatus(ids, AccountRechargeEnum.STATUS_1.getCode(), userName, new Date(),
                    userId, deptId);
        }
        return 0;
    }


    @Override
    public int designationReceiveAccount(int count, Long userId, String userName, Long deptId, String accountZone, String idType, String walletArea) {

        // 可领取的数量
        int claimableNum = ConfigConstant.MAX_RECEIVE_COUNT;

        // 未完成充值（待充值和部分充值）的账号不能超过10个账号
        int countExist = this.accountRechargeMapper.countByReceiveUserIdAndStatusList(userId,
                Arrays.asList(AccountRechargeEnum.STATUS_1.getCode(), AccountRechargeEnum.STATUS_2.getCode()));
        if (countExist >= claimableNum) {
            throw new ServiceException("未完成充值（待充值和部分充值）的账号不能超过30个账号！");
        }

        // 可领取的数量 - 存在的未完成充值数 = 可领取的数量
        int claimableCount = claimableNum - countExist;

        // 实际领取数量
        int realClaimableCount = 0;
        if (count > claimableCount) {
            realClaimableCount = claimableCount;
        } else {
            realClaimableCount = count;
        }

        if (realClaimableCount == 0) {
            return 0;
        }

        // 更新指定数量的未领取为代充值状态
        List<AccountRecharge> list = this.accountRechargeMapper.selectTopByStatusByWalletArea(AccountRechargeEnum.STATUS_0.getCode(), realClaimableCount, accountZone, idType, walletArea);
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> ids = list.stream().map(AccountRecharge::getAcid).collect(Collectors.toList());
            return this.accountRechargeMapper.batchUpdateAccountRechargeStatus(ids, AccountRechargeEnum.STATUS_1.getCode(), userName, new Date(),
                    userId, deptId);
        }
        return 0;
    }

    @Override
    public String importData(List<AccountRecharge> list) {

        StringBuffer sb = new StringBuffer();
        sb.append("操作成功! \n");

        // 导入的数据非空校验
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("导入数据不能为空!");
        }


        // 导入数据属性非空校验
        Iterator<AccountRecharge> iteratorList = list.iterator();
        int i = 1;
        while (iteratorList.hasNext()) {
            i++;
            AccountRecharge accountRecharge = iteratorList.next();
            if (StrUtil.isBlank(accountRecharge.getAccountName())) {
                sb.append("第" + i + "行账号为空!\n");
                iteratorList.remove();
                continue;
            }
            if (StrUtil.isBlank(accountRecharge.getAccountPwd())) {
                sb.append("第" + i + "行密码为空!\n");
                iteratorList.remove();
                continue;
            }

        }

        // excel里的账号去重
        Map<String, AccountRecharge> distinctExcelMap = new TreeMap<>();
        for (AccountRecharge accountRecharge : list) {
            distinctExcelMap.put(accountRecharge.getAccountName(), accountRecharge);
        }
        List<AccountRecharge> distinctExcelList = new ArrayList<>(distinctExcelMap.values());

        // 数据账号去重
        Iterator<AccountRecharge> iterator = distinctExcelList.iterator();
        while (iterator.hasNext()) {
            AccountRecharge accountRecharge = iterator.next();
            AccountRecharge one = this.accountRechargeMapper.getOne(accountRecharge.getAccountName());
            if (ObjUtil.isNotNull(one)) {
                sb.append("数据库中重复账号:" + accountRecharge.getAccountName() + "\n");
                iterator.remove();
                continue;
            }
        }

        // 导入操作
        for (AccountRecharge accountRecharge : distinctExcelList) {
            this.accountRechargeMapper.insertAccountRecharge(accountRecharge);
        }

        return sb.toString();

    }

    @Override
    public void importExhibitors(MultipartFile exhibitors) {
//        try {
//            ImportParams params = new ImportParams();
//            params.setTitleRows(1);
//            params.setHeadRows(1);
//            List<SExhExhibitorImportExcel> result = ExcelImportUtil.importExcel(exhibitors.getInputStream(),SExhExhibitorImportExcel.class, params);
//            //将图片上传到服务器将地址保存
//            result.forEach(exhibitor->{
//                if(StringUtil.isNotEmpty(exhibitor.getCzsLogo())){
//                    try {
//                        File file = new File(exhibitor.getCzsLogo());
//                        FileInputStream fileInputStream = new FileInputStream(file);
//                        /**
//                         *这里需要把file转化为MultipartFile，使用了MockMultipartFile方法，需要加入依赖
//                         <dependency>
//                         <groupId>org.springframework</groupId>
//                         <artifactId>spring-test</artifactId>
//                         <version>5.3.9</version>
//                         </dependency>
//                         **/
//                        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(fileInputStream));
//                        //调用自己的上传接口
//                        String imgUrl = fileService.uploadFile(multipartFile,"exhibitor");
//                        exhibitor.setCzsLogo(imgUrl);
//                        //todo 把中转类的值赋给需要存储的对象
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }
//            });
//            //todo 在这里把需要存储的对象放入库
//        }catch (Exception e){
//            log.error("导入展商模板出错");
//            e.printStackTrace();
//        }
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountRecharge> selectAccountRechargeReportPage(AccountRechargePageParam param) {
        // 没有传领取时间条件，默认为当前班次时间范围
        /*ClassesDateRangeVo vo = JuYouDateUtil.getClassesBetweenDate();
        if(ObjUtil.isNull(param.getStartReceiveTime()) && ObjUtil.isNull(param.getEndReceiveTime())){
            param.setStartReceiveTime(vo.getStartDate());
            param.setEndReceiveTime(vo.getEndDate());
        }*/
        return this.accountRechargeMapper.selectAccountRechargeReportPage(param);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountRecharge> selectAccountRechargePage(AccountRechargePageParam param) {
        return this.accountRechargeMapper.selectAccountRechargePage(param);
    }

    /**
     * 查询账号充值列表
     *
     * @param accountRecharge 账号充值
     * @return 账号充值
     */
    @Override
    public List<AccountRecharge> selectAccountRechargeList(AccountRecharge accountRecharge) {
        return accountRechargeMapper.selectAccountRechargeList(accountRecharge);
    }

    /**
     * 新增账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    @Override
    public int insertAccountRecharge(AccountRecharge accountRecharge) {
        accountRecharge.setCreateTime(DateUtils.getNowDate());
        int row = accountRechargeMapper.insertAccountRecharge(accountRecharge);
        if (row == 1 && accountRecharge.getStatus().equals("3")) {
            row = insertSellRow(accountRecharge);
        }

        return row;
    }

    @Override
    public int recharge(AccountRechargeParam param) {
        AccountRechargeDetailVo old = this.getDetail(param.getAcid());
        String logType = null;

        Date now = new Date();

        Integer acid = param.getAcid();
        BigDecimal rechargeAmt = param.getRechargeAmt();
        List<AccountRechargeParam.RechargeGiftCardRechargeRecordListVo> cardList = param.getCardList();
        Integer status = param.getStatus();
        Integer currentUserId = param.getCurrentUserId();
        String currentUsername = param.getCurrentUsername();
        String idUser = param.getIdUser();

        AccountRecharge accountRecharge = this.accountRechargeMapper.selectAccountRechargeByAcid(acid);
        String accountName = accountRecharge.getAccountName();

        // check
        // 判断进价不能大于7.5
        {
            for (AccountRechargeParam.RechargeGiftCardRechargeRecordListVo card : cardList) {
                if (StrUtil.isBlank(card.getGiftCard())) {
                    throw new ServiceException("卡密不能为空!");
                }
                if (ObjUtil.isNull(card.getFaceValue())) {
                    throw new ServiceException("面值不能为空!");
                }
                if (ObjUtil.isNull(card.getBuyPrice())) {
                    throw new ServiceException("汇率(价格)不能为空!");
                }
                if (card.getBuyPrice().doubleValue() > 7.5) {
                    throw new ServiceException("输入的进价不能大于7.5");
                }
                if (StrUtil.isBlank(card.getSourceGroup())) {
                    throw new ServiceException("来源群不能为空!");
                }
                if (StrUtil.isBlank(card.getPledgeThirtyMinutes())) {
                    throw new ServiceException("是否质押30分钟不能为空!");
                }
                // 校验账号,礼品卡是否重复
                GiftCardRechargeRecord giftSelect = new GiftCardRechargeRecord();
                giftSelect.setAccount(accountName);
                giftSelect.setGiftCard(card.getGiftCard());
                List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(giftSelect);
                if (CollUtil.isNotEmpty(list)) {
                    throw new ServiceException("账号与礼品卡在系统中已经存在请确认!");
                }
            }
            Set<String> repeatList = cardList.stream().map(AccountRechargeParam.RechargeGiftCardRechargeRecordListVo::getGiftCard).collect(Collectors.toSet());
            if(repeatList.size() != cardList.size()){
                throw new ServiceException("请检查充值的礼品卡是否重复!");
            }
        }

//        AccountRechargeEditParam rechargeEditParam = new AccountRechargeEditParam();
//        accountRecharge.setSecondaryCharger(currentUserId.longValue());
        accountRecharge.setStatus(String.valueOf(status));
        accountRecharge.setIdUser(idUser);
        switch (status) {
            case 2:// 部分充值
                accountRecharge.setUpdateTime(now);
                accountRecharge.setUpdateUser(currentUsername);
                logType = LogTypeConstant.PARTIAL_RECHARGE;
                break;
            case 3://完成充值
                // 勾选“质押30分钟”，则不能点击“完成充值”按钮
                if (CollUtil.isNotEmpty(cardList)) {
                    for (AccountRechargeParam.RechargeGiftCardRechargeRecordListVo card : cardList) {
                        if (StrUtil.isNotBlank(card.getPledgeThirtyMinutes()) && StrUtil.equals("1", card.getPledgeThirtyMinutes())) {
                            throw new ServiceException("勾选“质押30分钟”，则不能点击“完成充值”按钮!");
                        }
                    }
                }
                // 礼品卡代码质押时间未超过30分钟
                int count = this.iGiftCardRechargeRecordService.countPledgeThirtyMinutes(accountName);
                if (count > 0) {
                    throw new ServiceException("礼品卡代码质押时间未超过30分钟。");
                }

                accountRecharge.setUpdateTime(now);
                accountRecharge.setDoneTime(now);
                accountRecharge.setPendingStartDate(now);
                accountRecharge.setUpdateUser(currentUsername);
                accountRecharge.setDoneUser(currentUsername);

                logType = LogTypeConstant.COMPLETE_RECHARGE;
                break;
        }

        // 添加礼品卡列表
        this.iGiftCardRechargeRecordService.batchInsert(cardList, accountRecharge, currentUsername);

        BigDecimal faceValue = this.iGiftCardRechargeRecordService.getFaceValue(accountName);
        BigDecimal buyAmt = this.iGiftCardRechargeRecordService.getBuyAmt(accountName);

        accountRecharge.setRechargeAmt(faceValue.doubleValue());
        accountRecharge.setBuyAmt(buyAmt.doubleValue());

        // 更新账号充值
        int count = this.updateAccountRecharge(accountRecharge);

        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(acid);
            RechargeLog.insertRecharge(old, newVo, logType);
        }


        return count;
    }

    @Override
    public int rechargeTwo(AccountRechargeParam param) {
        AccountRechargeDetailVo old = this.getDetail(param.getAcid());
        String logType = null;
        Date now = new Date();

        Integer acid = param.getAcid();
        BigDecimal rechargeAmt = param.getRechargeAmt();
        List<AccountRechargeParam.RechargeGiftCardRechargeRecordListVo> cardList = param.getCardList();
        Integer status = param.getStatus();
        Integer currentUserId = param.getCurrentUserId();
        String currentUsername = param.getCurrentUsername();
        String idUser = param.getIdUser();

        AccountRecharge accountRecharge = this.accountRechargeMapper.selectAccountRechargeByAcid(acid);
        String accountName = accountRecharge.getAccountName();

        // check
        // 判断进价不能大于7.5
        {
            for (AccountRechargeParam.RechargeGiftCardRechargeRecordListVo card : cardList) {
                if (StrUtil.isBlank(card.getGiftCard())) {
                    throw new ServiceException("卡密不能为空!");
                }
                if (ObjUtil.isNull(card.getFaceValue())) {
                    throw new ServiceException("面值不能为空!");
                }
                if (ObjUtil.isNull(card.getBuyPrice())) {
                    throw new ServiceException("汇率(价格)不能为空!");
                }
                if (card.getBuyPrice().doubleValue() > 7.5) {
                    throw new ServiceException("输入的进价不能大于7.5");
                }
                if (StrUtil.isBlank(card.getSourceGroup())) {
                    throw new ServiceException("来源群不能为空!");
                }
                if (StrUtil.isBlank(card.getPledgeThirtyMinutes())) {
                    throw new ServiceException("是否质押30分钟不能为空!");
                }
                // 校验账号,礼品卡是否重复
                GiftCardRechargeRecord giftSelect = new GiftCardRechargeRecord();
                giftSelect.setAccount(accountName);
                giftSelect.setGiftCard(card.getGiftCard());
                List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(giftSelect);
                if (CollUtil.isNotEmpty(list)) {
                    throw new ServiceException("账号与礼品卡在系统中已经存在请确认!");
                }
            }
            Set<String> repeatList = cardList.stream().map(AccountRechargeParam.RechargeGiftCardRechargeRecordListVo::getGiftCard).collect(Collectors.toSet());
            if(repeatList.size() != cardList.size()){
                throw new ServiceException("请检查充值的礼品卡是否重复!");
            }
        }

//        AccountRechargeEditParam rechargeEditParam = new AccountRechargeEditParam();
        accountRecharge.setSecondaryCharger(currentUserId.longValue());
        accountRecharge.setStatus(String.valueOf(status));
        accountRecharge.setIdUser(idUser);
        switch (status) {
            case 2:// 部分充值
                accountRecharge.setUpdateTime(now);
                accountRecharge.setUpdateUser(currentUsername);
                logType = LogTypeConstant.PARTIAL_RECHARGE;
                break;
            case 3://完成充值
                // 勾选“质押30分钟”，则不能点击“完成充值”按钮
                if (CollUtil.isNotEmpty(cardList)) {
                    for (AccountRechargeParam.RechargeGiftCardRechargeRecordListVo card : cardList) {
                        if (StrUtil.isNotBlank(card.getPledgeThirtyMinutes()) && StrUtil.equals("1", card.getPledgeThirtyMinutes())) {
                            throw new ServiceException("勾选“质押30分钟”，则不能点击“完成充值”按钮!");
                        }
                    }
                }
                // 礼品卡代码质押时间未超过30分钟
                int count = this.iGiftCardRechargeRecordService.countPledgeThirtyMinutes(accountName);
                if (count > 0) {
                    throw new ServiceException("礼品卡代码质押时间未超过30分钟。");
                }

                accountRecharge.setUpdateTime(now);
                accountRecharge.setDoneTime(now);
                accountRecharge.setPendingStartDate(now);
                accountRecharge.setUpdateUser(currentUsername);
                accountRecharge.setDoneUser(currentUsername);
                logType = LogTypeConstant.COMPLETE_RECHARGE;
                break;
        }

        // 添加礼品卡列表
        this.iGiftCardRechargeRecordService.batchInsert(cardList, accountRecharge, currentUsername);

        BigDecimal faceValue = this.iGiftCardRechargeRecordService.getFaceValue(accountName);
        BigDecimal buyAmt = this.iGiftCardRechargeRecordService.getBuyAmt(accountName);

        accountRecharge.setRechargeAmt(faceValue.doubleValue());
        accountRecharge.setBuyAmt(buyAmt.doubleValue());

        // 更新账号充值
        int count = this.updateAccountRecharge(accountRecharge);

        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(acid);
            RechargeLog.insertRecharge(old, newVo, logType);
        }

        return count;
    }

    @Override
    public int finishRecharge(AccountRechargeEditParam param, String currentUsername) {
        // check
        // 勾选“质押30分钟”，则不能点击“完成充值”按钮
        if (StrUtil.isNotBlank(param.getPledgeThirtyMinutes()) && StrUtil.equals("1", param.getPledgeThirtyMinutes())) {
            throw new ServiceException("勾选“质押30分钟”，则不能点击“完成充值”按钮!");
        }
        // 礼品卡代码质押时间未超过30分钟
        int count = this.iGiftCardRechargeRecordService.countPledgeThirtyMinutes(param.getAccountName());
        if (count > 0) {
            throw new ServiceException("礼品卡代码质押时间未超过30分钟。");
        }

        Date now = new Date();

        param.setUpdateTime(now);
        param.setDoneTime(now);
        param.setUpdateBy(currentUsername);
        param.setDoneUser(currentUsername);

        // 本次充值金额赋值给礼品卡面值 = 如果本次充值金额没有取实际充值金额赋值，如果有就取本次充值金额
        if (ObjUtil.isNull(param.getTimeAmt())) {
            param.setTimeAmt(BigDecimal.valueOf(param.getRechargeAmt()));
        }

        // 更新账号充值
        count = this.updateAccountRecharge(param);

        // 新增礼品卡记录
        this.iGiftCardRechargeRecordService.insert(param, currentUsername);

        // 删除需要删除的礼品卡充值记录id集合
        if (CollUtil.isNotEmpty(param.getDeleteGiftCardRechargeRecordIds())) {
            Long[] deleteIds = param.getDeleteGiftCardRechargeRecordIds().stream().toArray(Long[]::new);
            this.iGiftCardRechargeRecordService.deleteGiftCardRechargeRecordByIds(deleteIds);
        }

        return count;
    }

    @Override
    public int partialRecharge(AccountRechargeEditParam param, String currentUsername) {

        Date now = new Date();
        param.setUpdateTime(now);
        param.setUpdateUser(currentUsername);

        // 本次充值金额赋值给礼品卡面值 = 如果本次充值金额没有取实际充值金额赋值，如果有就取本次充值金额
        if (ObjUtil.isNull(param.getTimeAmt())) {
            param.setTimeAmt(BigDecimal.valueOf(param.getRechargeAmt()));
        }

        // 更新账号充值
        int count = this.updateAccountRecharge(param);

        // 新增礼品卡记录
        this.iGiftCardRechargeRecordService.insert(param, currentUsername);

        // 删除需要删除的礼品卡充值记录id集合
        if (CollUtil.isNotEmpty(param.getDeleteGiftCardRechargeRecordIds())) {
            Long[] deleteIds = param.getDeleteGiftCardRechargeRecordIds().stream().toArray(Long[]::new);
            this.iGiftCardRechargeRecordService.deleteGiftCardRechargeRecordByIds(deleteIds);
        }

        return count;
    }

    private int insertSellRow(AccountRecharge reg) {
        AccountSell sell = new AccountSell();
        sell.setAcid(reg.getAcid());
        AccountRecharge accountRecharge = accountRechargeMapper.selectAccountRechargeByAcid(reg.getAcid());
        if (accountRecharge != null) {
            if (accountRecharge.getIdType().equals("1")) {
                sell.setCompletedTimeMinutesNum(Double.valueOf(********));
            }
            if (accountRecharge.getSpareCode() != null) {
                sell.setSpareCode(accountRecharge.getSpareCode());
            }
        }
        sell.setAccountName(reg.getAccountName());
        sell.setAccountPwd(reg.getAccountPwd());
        sell.setAccountZone(reg.getAccountZone());
        sell.setWalletArea(reg.getWalletArea());
        sell.setStatus(AccountSellEnum.STATUS_1.getStatus());
        sell.setWriteOffStatus(AccountSellWriteOffStatusEnum.UNCHECKED.getCode());
        sell.setCardBalance(reg.getRechargeAmt());//充值金额
        sell.setBuyPrice(reg.getBuyPrice());//成本单价
        sell.setBuyAmt(reg.getBuyAmt());//成本
        sell.setShouldAmt(reg.getShouldAmt());
        sell.setCreateTime(DateUtils.getNowDate());
        sell.setCreateBy(reg.getUpdateUser());
        sell.setUpdateTime(reg.getUpdateTime());
        sell.setUpdateBy(reg.getUpdateUser());
        sell.setIdType(reg.getIdType());
        return accountSellMapper.insertAccountSell(sell);
    }

    /**
     * 修改账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    @Override
    public int updateAccountRecharge(AccountRecharge accountRecharge) {
        AccountRechargeDetailVo old = this.getDetail(accountRecharge.getAcid());

        accountRecharge.setUpdateTime(DateUtils.getNowDate());
        int row = accountRechargeMapper.updateAccountRecharge(accountRecharge);
        if (row == 1 && accountRecharge.getStatus().equals("3")) {
            row = insertSellRow(accountRecharge);
        }

        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(accountRecharge.getAcid());
            RechargeLog.insertRecharge(old, newVo, LogTypeConstant.UPDATE);
        }
        return row;
    }

    @Override
    public int cancel(AccountRechargeCancelParam param) {
        AccountRechargeDetailVo old = this.getDetail(param.getAcid());

        Date now = new Date();
        AccountRecharge recharge = new AccountRecharge();
        recharge.setAcid(param.getAcid());
        recharge.setStatus(AccountRechargeEnum.STATUS_4.getCode());
        recharge.setCancelReasonType(param.getCancelReasonType());
        recharge.setDoubleProhibitedBalance(param.getDoubleProhibitedBalance());
        recharge.setCancelImgs(param.getCancelImgs());
        recharge.setCancelDate(now);
        recharge.setUpdateUser(param.getUpdateBy());
        recharge.setUpdateTime(now);
        int i = this.accountRechargeMapper.updateAccountRecharge(recharge);


        // 记录日志
        {
            AccountRechargeDetailVo newVo = this.getDetail(param.getAcid());
            RechargeLog.insertRecharge(old, newVo, LogTypeConstant.NULLIFY);
        }

        return i;
    }

    /**
     * 批量删除账号充值
     *
     * @param acids 需要删除的账号充值主键
     * @return 结果
     */
    @Override
    public int deleteAccountRechargeByAcids(Integer[] acids) {
        return accountRechargeMapper.deleteAccountRechargeByAcids(acids);
    }

    /**
     * 删除账号充值信息
     *
     * @param acid 账号充值主键
     * @return 结果
     */
    @Override
    public int deleteAccountRechargeByAcid(Integer acid) {
        return accountRechargeMapper.deleteAccountRechargeByAcid(acid);
    }

    /**
     * 批量核对
     *
     * @param param
     * @return
     */
    @Override
    public Integer batchWriteOff(AccountRechargeBatchWriteOffParam param) {

        if (CollUtil.isNotEmpty(param.getAcIdList())) {
            List<AccountRecharge> list = param.getAcIdList().stream().map(item -> {
                AccountRecharge sell = new AccountRecharge();
                sell.setAcid(item);
                sell.setWriteOffStatus(AccountSellWriteOffStatusEnum.CHECKED.getCode());
                return sell;
            }).collect(Collectors.toList());
            int i = 0;
            for (AccountRecharge item : list) {
                i += this.accountRechargeMapper.updateAccountRecharge(item);
            }
            return i;
        }
        return null;
    }

    @Override
//    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountRechargeVo> selectAccountRechargeVoList(AccountRecharge accountRecharge) {
        return accountRechargeMapper.selectAccountRechargeVoList(accountRecharge);
    }

    @Override
    public int putAccountRecharge(AccountRecharge accountRecharge) {

        return accountRechargeMapper.updatePutAccountRecharge(accountRecharge);
    }

    @Override
    public List<KeyValueVo> getlevel1UserList() {

        return  accountRechargeMapper.getlevel1UserList();
    }

    @Override
    public List<KeyValueVo> getlevel2UserList() {

        return  accountRechargeMapper.getlevel2UserList();
    }


}
