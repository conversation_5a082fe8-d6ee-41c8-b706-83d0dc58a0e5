package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 代充查询-param
 */
@Data
@ApiModel("ChargingInquirySearchParam")
public class ChargingInquirySearchParam extends BaseEntity {

    private static final long serialVersionUID = 1372954552955722720L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("充值状态")
    private String status;

    @ApiModelProperty("充值阶段")
    private Long chargeStage;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("完成充值时间:开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date doneTimeStart;

    @ApiModelProperty("完成充值时间:结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date doneTimeEnd;

    @ApiModelProperty("核对状态：1 未核对 2 已核对")
    private String writeOffStatus;

    @ApiModelProperty("剩余等待时长:开始")
    private Integer surplusWaitDurationStart;

    @ApiModelProperty("剩余等待时长:结束")
    private Integer surplusWaitDurationEnd;

    @ApiModelProperty("一级充值人")
    private Long primaryCharger;

    @ApiModelProperty("二级充值人")
    private Long secondaryCharger;

    @ApiModelProperty("归属业务员名称")
    private String idUser;

    @ApiModelProperty("账号出售待生效分钟：后端使用")
    private Integer accountSellPendingMinutes;

}

