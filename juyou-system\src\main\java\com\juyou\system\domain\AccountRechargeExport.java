package com.juyou.system.domain;

import com.juyou.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
@ApiModel(value = "AccountRecharge")
public class AccountRechargeExport {

    @Excel(name = "账号")
    @ApiModelProperty("账号")
    private String accountName;

    @Excel(name = "密码")
    @ApiModelProperty("密码")
    private String accountPwd;

//    @Excel(name = "图片",cellType = Excel.ColumnType.IMAGE)
//    @ApiModelProperty("图片")
//    private byte[] img;
}
