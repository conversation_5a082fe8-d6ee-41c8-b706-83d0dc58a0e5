package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 充值明细核销-parma
 */
@Data
@ApiModel("AccountRechargeBatchWriteOffParam")
public class AccountRechargeBatchWriteOffParam implements Serializable {
    private static final long serialVersionUID = -5025314332925098784L;

    @ApiModelProperty("acid集合")
    private List<Integer> acIdList;
}
