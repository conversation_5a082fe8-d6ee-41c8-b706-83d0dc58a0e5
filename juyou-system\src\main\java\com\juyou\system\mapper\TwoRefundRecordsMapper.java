package com.juyou.system.mapper;

import com.juyou.system.domain.vo.TwoRefundRecords;

import java.util.List;


/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface TwoRefundRecordsMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TwoRefundRecords selectTwoRefundRecordsById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TwoRefundRecords> selectTwoRefundRecordsList(TwoRefundRecords twoRefundRecords);

    /**
     * 新增【请填写功能名称】
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 结果
     */
    public int insertTwoRefundRecords(TwoRefundRecords twoRefundRecords);

    /**
     * 修改【请填写功能名称】
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 结果
     */
    public int updateTwoRefundRecords(TwoRefundRecords twoRefundRecords);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTwoRefundRecordsById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTwoRefundRecordsByIds(Long[] ids);
}
