package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号充值作废金额明细SearchParam
 */
@Data
@ApiModel("RechargeCancelAmtDetailSearchParam")
public class RechargeCancelAmtDetailSearchParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 4098442187151837327L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("作废开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelStartDate;

    @ApiModelProperty("作废结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelEndDate;

}
