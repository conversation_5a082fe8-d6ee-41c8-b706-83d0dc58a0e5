package com.juyou.web.controller.statistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.*;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IAccountSellService;
import com.juyou.system.service.IAccountSellStatisticsService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 账号销售统计Controller
 */
@Api(value = "账号销售统计", tags = "账号销售统计")
@RestController
@RequestMapping("/system/sellStatistics")
public class AccountSellStatisticsController extends BaseController {


    @Autowired
    private IAccountSellService iAccountSellService;

    @Autowired
    private IAccountRechargeService iAccountRechargeService;

    @Autowired
    private IAccountSellStatisticsService iAccountSellStatisticsService;

    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @ApiOperation("礼品卡充值来源群统计详情列表")
    @Log(title = "账号销售统计-礼品卡充值来源群统计详情列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:giftCardRechargeStatisticsDetail')")
    @GetMapping("/getGiftCardRechargeStatisticsDetailList")
    public TableDataInfo<GiftCardRechargeRecord> getGiftCardRechargeStatisticsDetailList(GiftCardRechargeStatisticsDetailSearchParam param) {
        if (StrUtil.isBlank(param.getSourceGroup())) {
            throw new ServiceException("充值来源群不能为空!");
        }
        // startPage();
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.getGiftCardRechargeStatisticsDetailList(param);
        return getDataTable(list);
    }

    @ApiOperation("礼品卡充值来源群统计")
    @Log(title = "账号销售统计-礼品卡充值来源群统计", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:giftCardRechargeStatistics')")
    @GetMapping("/getGiftCardRechargeStatisticsList")
    public TableDataInfo<GiftCardRechargeStatisticsVo> getGiftCardRechargeStatisticsList(GiftCardRechargeStatisticsSearchParam param) {
        // startPage();
        List<GiftCardRechargeStatisticsVo> list = this.iAccountSellStatisticsService.getGiftCardRechargeStatisticsList(param);
        return getDataTable(list);
    }

    @ApiOperation("出售账号作废金额明细列表")
    @Log(title = "账号销售统计-出售账号作废金额明细列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:sellCancelAmtDetailList')")
    @GetMapping("/getSellCancelAmtDetailList")
    public TableDataInfo<AccountSellStatisticsDetailVo> getSellCancelAmtDetailList(SellCancelAmtDetailParam param) {
        // startPage();
        List<AccountSell> list = this.iAccountSellStatisticsService.getSellCancelAmtDetailList(param);

        List<AccountSellStatisticsDetailVo> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            voList = list.stream().map(item ->{
                AccountSellStatisticsDetailVo vo = new AccountSellStatisticsDetailVo();
                BeanUtil.copyProperties(item,vo);
                vo.setSellUsername(item.getDoneUser());
                AccountRecharge accountRecharge = this.iAccountRechargeService.selectAccountRechargeByAcid(item.getAcid());
                if(ObjUtil.isNotNull(accountRecharge)){
                    vo.setRechargeUsername(accountRecharge.getDoneUser());
                }
                return vo;
            }).collect(Collectors.toList());
        }
        return getDataTable(voList);
    }

    @ApiOperation("充值账号作废金额明细列表")
    @Log(title = "账号销售统计-充值账号作废金额明细列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:rechargeCancelAmtDetailList')")
    @GetMapping("/getRechargeCancelAmtDetailList")
    public TableDataInfo<AccountRechargeStatisticsDetailVo> getRechargeCancelAmtDetailList(RechargeCancelAmtDetailSearchParam param) {
        // startPage();
        List<AccountRecharge> list = this.iAccountSellStatisticsService.getRechargeCancelAmtDetailList(param);

        List<AccountRechargeStatisticsDetailVo> voList = new ArrayList<>();
        if(CollUtil.isNotEmpty(list)){
            voList = list.stream().map(item -> {
                AccountRechargeStatisticsDetailVo vo = new AccountRechargeStatisticsDetailVo();
                BeanUtil.copyProperties(item, vo);
                vo.setRechargeUsername(item.getDoneUser());
                return vo;
            }).collect(Collectors.toList());
        }

        return getDataTable(voList);
    }

    @ApiOperation("出售群统计明细列表")
    @Log(title = "账号销售统计-出售群统计明细列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:sellGroupDetailList')")
    @GetMapping("/getSellGroupDetailList")
    public TableDataInfo<AccountSellStatisticsDetailVo> getSellGroupDetailList(SellGroupDetailSearchParam param) {
        // check
        if (StrUtil.isBlank(param.getSellChatgroupName())) {
            throw new ServiceException("出售群不能为空!");
        }
        // startPage();
        List<AccountSell> list = this.iAccountSellStatisticsService.getSellGroupDetailList(param);

        List<AccountSellStatisticsDetailVo> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            voList = list.stream().map(item -> {
                AccountSellStatisticsDetailVo vo = new AccountSellStatisticsDetailVo();
                BeanUtil.copyProperties(item, vo);
                vo.setSellUsername(item.getDoneUser());
                AccountRecharge accountRecharge = this.iAccountRechargeService.selectAccountRechargeByAcid(item.getAcid());
                if (ObjUtil.isNotNull(accountRecharge)) {
                    vo.setRechargeUsername(accountRecharge.getDoneUser());
                }
                return vo;
            }).collect(Collectors.toList());
        }
        return getDataTable(voList);
    }

    @ApiOperation("作废统计列表")
    @Log(title = "账号销售统计-作废统计列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:cancelList')")
    @GetMapping("/getCancelList")
    public ResultData<List<AccountStatisticsCancelVoList>> getCancelList(AccountStatisticsCancelParam param) {
        List<AccountStatisticsCancelVoList> listVo = this.iAccountSellStatisticsService.getCancelList(param);
        return ResultUtil.success(listVo);
    }

    @ApiOperation("未出售合计(累计)")
    @Log(title = "账号销售统计-未出售合计(累计)", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:unsoldTotal')")
    @GetMapping("/getUnsoldTotal")
    public ResultData<AccountUnsoldTotalVo> getUnsoldTotal() {
        AccountUnsoldTotalVo vo = this.iAccountSellService.getUnsoldTotal();
        return ResultUtil.success(vo);
    }

    @ApiOperation("账号销售列表统计分页")
    @Log(title = "账号销售统计-账号销售列表统计分页", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:sellChatgroupList')")
    @GetMapping("/getSellChatgroupList")
    public TableDataInfo<SellChatgroupVo> getSellChatgroupList(SellChatgroupParam param) {
        // startPage();
        List<SellChatgroupVo> list = this.iAccountSellService.getSellChatgroupList(param);
        return getDataTable(list);
    }

    @ApiOperation("导出")
    @Log(title = "账号销售统计-导出", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellStatistics:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SellChatgroupParam param) {
        List<SellChatgroupVo> list = iAccountSellService.getSellChatgroupList(param);
        ExcelUtil<SellChatgroupVo> util = new ExcelUtil<SellChatgroupVo>(SellChatgroupVo.class);
        util.exportExcel(response, list, "账号销售统计列表");
    }
}
