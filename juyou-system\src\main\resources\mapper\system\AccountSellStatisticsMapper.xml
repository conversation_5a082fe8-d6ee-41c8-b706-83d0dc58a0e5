<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.AccountSellStatisticsMapper">


    <select id="getCancelList" resultType="com.juyou.system.domain.vo.AccountStatisticsCancelVoList"
            parameterType="com.juyou.system.params.AccountStatisticsCancelParam">
        SELECT
            (case when ar.`charge_stage` = 1 then '一级充值' else '二级充值' end) as 'cancelType',
                COUNT(*) as 'cancelTotal',
                SUM(ar.recharge_amt) as 'cancelAmt',
                SUM(ar.buy_amt) as 'lossAmt'
        FROM two_account_recharge ar
        where ar.`status` = 4
        <if test="param.cancelStartDate != null and param.cancelEndDate != null">
            and ar.cancel_date BETWEEN #{param.cancelStartDate} and #{param.cancelEndDate}
        </if>
        GROUP BY ar.`charge_stage`
        UNION ALL

        select
            '出售' as 'cancelType',
                COUNT(*) as 'cancelTotal',
                SUM(ase.card_balance) as 'cancelAmt',
                SUM(ase.buy_amt) as 'lossAmt'
        FROM two_account_sell ase
        WHERE ase.`status` = 4
        <if test="param.cancelStartDate != null and param.cancelEndDate != null">
            and ase.cancel_date BETWEEN #{param.cancelStartDate} and #{param.cancelEndDate}
        </if>
    </select>

    <select id="getRechargeCancelAmtDetailList" resultType="com.juyou.system.domain.AccountRecharge"
            parameterType="com.juyou.system.params.RechargeCancelAmtDetailSearchParam">
        <include refid="com.juyou.system.mapper.AccountRechargeMapper.selectAccountRechargeListVo"/>
        <if test="param.giftCard != null and param.giftCard != ''">
            inner join ( select gcrr.account FROM two_gift_card_recharge_record gcrr WHERE gcrr.gift_card LIKE CONCAT('%',#{param.giftCard},'%') GROUP BY gcrr.account ) as gcrr on gcrr.account = ar.account_name
        </if>
        where ar.`status` = 4
        <if test="param.accountName != null and param.accountName != ''">
            and ar.account_name LIKE CONCAT('%',#{param.accountName},'%')
        </if>
        <if test="param.cancelStartDate != null and param.cancelEndDate != null">
            and ar.cancel_date BETWEEN #{param.cancelStartDate} and #{param.cancelEndDate}
        </if>
    </select>

    <select id="getSellCancelAmtDetailList" resultType="com.juyou.system.domain.AccountSell"
            parameterType="com.juyou.system.params.SellCancelAmtDetailParam">
        <include refid="com.juyou.system.mapper.AccountSellMapper.selectAccountSellListVo"/>
        <if test="param.giftCard != null and param.giftCard != ''">
            inner join ( select gcrr.account FROM two_gift_card_recharge_record gcrr WHERE gcrr.gift_card LIKE CONCAT('%',#{param.giftCard},'%') GROUP BY gcrr.account ) as gcrr on gcrr.account = ase.account_name
        </if>
        WHERE ase.`status` = 4
        <if test="param.accountName != null and param.accountName != ''">
            and ase.account_name LIKE CONCAT('%',#{param.accountName},'%')
        </if>
        <if test="param.cancelStartDate != null and param.cancelEndDate != null">
            and ase.cancel_date BETWEEN #{param.cancelStartDate} and #{param.cancelEndDate}
        </if>
    </select>
    <select id="getGiftCardRechargeStatisticsList" resultType="com.juyou.system.domain.vo.GiftCardRechargeStatisticsVo"
            parameterType="com.juyou.system.params.GiftCardRechargeStatisticsSearchParam">
        select
            g.source_group as sourceGroup,
            SUM(g.face_value) as faceValue,
            ROUND(SUM(g.buy_amt) / SUM(g.face_value),2) as avgPrice,
            SUM(g.buy_amt) as buyAmt
        from two_gift_card_recharge_record g
        <where>
            <if test="param.startRechargeCompleteTime != null and param.endRechargeCompleteTime != null ">
                AND g.create_time BETWEEN #{param.startRechargeCompleteTime} AND #{param.endRechargeCompleteTime}
            </if>
            <if test="param.sourceGroup != null and param.sourceGroup != ''">AND g.source_group LIKE CONCAT('%',#{param.sourceGroup},'%')</if>
        </where>
        GROUP BY g.source_group
        ORDER BY
        CASE
        WHEN g.source_group REGEXP '^[0-9]+$' THEN CAST(g.source_group AS UNSIGNED)
        ELSE 9999
        END
        DESC
    </select>
</mapper>
