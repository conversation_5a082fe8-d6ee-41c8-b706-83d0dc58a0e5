package com.juyou.system.mapper;

import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.RecordOfChargingCardsVo;
import com.juyou.system.params.GiftCardRechargeRecordPageParam;
import com.juyou.system.params.GiftCardRechargeStatisticsDetailSearchParam;
import com.juyou.system.params.RecordOfChargingCardsParam;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 礼品卡充值记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
public interface GiftCardRechargeRecordMapper 
{

    /**
     * 获取账号的总成本
     * @param account
     * @return
     */
    BigDecimal getBuyAmt(@Param("account") String account);


    /**
     * 获取面值总值
     * @param account
     * @return
     */
    BigDecimal getFaceValue(@Param("account") String account);

    /**
     * 礼品卡充值来源群统计详情列表
     * @param param
     * @return
     */
    List<GiftCardRechargeRecord> getGiftCardRechargeStatisticsDetailList(@Param("param") GiftCardRechargeStatisticsDetailSearchParam param);


    /**
     * 统计质押30分钟
     * @param account
     * @return
     */
    int countPledgeThirtyMinutes(@Param("account") String account);

    /**
     * 查询礼品卡充值记录
     * 
     * @param id 礼品卡充值记录主键
     * @return 礼品卡充值记录
     */
    public GiftCardRechargeRecord selectGiftCardRechargeRecordById(Long id);

    /**
     * 查询礼品卡充值记录分页列表
     * @param param
     * @return
     */
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordPage(@Param("param") GiftCardRechargeRecordPageParam param);

    /**
     * 查询礼品卡充值记录列表
     * 
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 礼品卡充值记录集合
     */
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordList(GiftCardRechargeRecord giftCardRechargeRecord);

    /**
     * 新增礼品卡充值记录
     * 
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    public int insertGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord);

    /**
     * 修改礼品卡充值记录
     * 
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    public int updateGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord);

    /**
     * 批量删除
     * @param accountName
     * @return
     */
    int deleteByAccountName(@Param("accountName") String accountName);


    /**
     * 删除礼品卡充值记录
     * 
     * @param id 礼品卡充值记录主键
     * @return 结果
     */
    public int deleteGiftCardRechargeRecordById(Long id);

    /**
     * 批量删除礼品卡充值记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGiftCardRechargeRecordByIds(Long[] ids);

    /**
     * 代充卡记录
     *
     * @param  param
     * @return 结果
     */
    public List<RecordOfChargingCardsVo>  getRecordOfChargingCards(RecordOfChargingCardsParam param);

    public List<RecordOfChargingCardsVo>  getRecordOfChargingCardsAll(RecordOfChargingCardsParam param);

    public List<GiftCardRechargeRecord>     getOldAccountCaedRechargeInfo(String accountName );
}
