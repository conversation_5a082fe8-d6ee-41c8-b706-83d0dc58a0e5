package com.juyou.web.controller.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.domain.vo.ChargingInquiryExcelVo;
import com.juyou.system.domain.vo.ChargingInquiryListVo;
import com.juyou.system.enums.AccountRechArgeChargeStageEnum;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.enums.AccountSellWriteOffStatusEnum;
import com.juyou.system.params.AccountRechargeBatchWriteOffParam;
import com.juyou.system.params.AccountRechargeParam;
import com.juyou.system.params.ChargingInquirySearchParam;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.ISysUserService;
import com.juyou.system.utils.EnumUtil;
import com.juyou.system.domain.vo.KeyValueVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 代充查询-manager controller
 */
@Api(value = "代充查询", tags = "代充查询")
@RestController
@RequestMapping("/manager/chargingInquiry")
public class ChargingInquiryController extends BaseController {

    @Autowired
    private IAccountRechargeService accountRechargeService;

    @Autowired
    private ISysUserService sysUserService;


    @ApiOperation("代充查询")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:list')")
    @GetMapping("/list")
    public TableDataInfo<ChargingInquiryListVo> list(ChargingInquirySearchParam param) {
        startPage();
        List<ChargingInquiryListVo> voList = this.accountRechargeService.chargingInquiryList(param);
        return getDataTable(voList);
    }

    @ApiOperation("导出")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChargingInquirySearchParam param) {
        List<ChargingInquiryListVo> voList = this.accountRechargeService.chargingInquiryList(param);
        ExcelUtil<ChargingInquiryExcelVo> util = new ExcelUtil<ChargingInquiryExcelVo>(ChargingInquiryExcelVo.class);

        List<ChargingInquiryExcelVo> excelVoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(voList)) {
            Long i = 1L;
            for (ChargingInquiryListVo item : voList) {
                ChargingInquiryExcelVo excelVo = new ChargingInquiryExcelVo();
                BeanUtil.copyProperties(item, excelVo);
                excelVo.setId(i++);

                AccountSellWriteOffStatusEnum writeOffStatusEnum = EnumUtil.getEnum(AccountSellWriteOffStatusEnum.class, item.getWriteOffStatus());
                excelVo.setWriteOffStatus(writeOffStatusEnum.getCode());

                AccountRechargeEnum statusEnum = EnumUtil.getEnum(AccountRechargeEnum.class, item.getStatus());
                excelVo.setStatus(statusEnum.getDesc());

                AccountRechArgeChargeStageEnum chargeStageEnum = EnumUtil.getEnum(AccountRechArgeChargeStageEnum.class, item.getChargeStage());
                excelVo.setChargeStage(chargeStageEnum.getDesc());

                if (ObjUtil.isNotNull(item.getPrimaryCharger())) {
                    SysUser sysUser = this.sysUserService.selectUserById(item.getPrimaryCharger());
                    excelVo.setPrimaryCharger(sysUser.getUserName());
                }

                if(ObjUtil.isNotNull(item.getSecondaryCharger())){
                    SysUser sysUser = this.sysUserService.selectUserById(item.getSecondaryCharger());
                    excelVo.setSecondaryCharger(sysUser.getUserName());
                }
                excelVoList.add(excelVo);
            }
        }
        util.exportExcel(response, excelVoList, "导出");
    }

    @ApiOperation("批量核对")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:batchWriteOff')")
    @PostMapping("/batchWriteOff")
    public ResultData<Integer> batchWriteOff(@RequestBody AccountRechargeBatchWriteOffParam param) {
        // check
        if (!CollUtil.isNotEmpty(param.getAcIdList())) {
            throw new ServiceException("请选择后操作!");
        }
        Integer i = this.accountRechargeService.batchWriteOff(param);
        return ResultUtil.success("ok");
    }

    @ApiOperation("代充详情")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:detail')")
    @GetMapping("/detail/{acid}")
    public ResultData<AccountRechargeDetailVo> getDetail(@PathVariable("acid") Integer acid) {
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空!");
        }
        AccountRechargeDetailVo detail = this.accountRechargeService.getDetail(acid);
        return ResultUtil.success(detail);
    }

    @ApiOperation("恢复")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:recovery')")
    @PostMapping("/recovery/{acid}")
    public ResultData<Boolean> recovery(@PathVariable("acid") Integer acid) {
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空!");
        }
        boolean recovery = this.accountRechargeService.recovery(acid);
        return ResultUtil.success(recovery);
    }

    @ApiOperation("修改")
    @PreAuthorize("@ss.hasPermi('system:chargingInquiry:edit')")
    @PostMapping("/edit")
    public ResultData<Boolean> edit(@RequestBody AccountRechargeParam param) {
        // check
        if (ObjUtil.isNull(param.getAcid())) {
            throw new ServiceException("账号充值id不能为空!");
        }
        if (ObjUtil.isNull(param.getRechargeAmt())) {
            throw new ServiceException("ID余额不能为空!");
        }
        if (CollUtil.isEmpty(param.getCardList())) {
            throw new ServiceException("卡密列表不能为空!");
        }
        if (CollUtil.isEmpty(param.getCardList())) {
            throw new ServiceException("卡列表不能为空!");
        }
//        if(StrUtil.isBlank(param.getIdUser())){
//            throw new ServiceException("ID归属业务员不能为空!");
//        }

        param.setStatus(Integer.valueOf(AccountRechargeEnum.STATUS_3.getCode()));
        param.setCurrentUserId(getUserId().intValue());
        param.setCurrentUsername(getUsername());

        boolean b = this.accountRechargeService.chargingInquiryEdit(param);

        return ResultUtil.success(b);
    }



    @ApiOperation("获取一级充值列表")
    @GetMapping("/getLevel1")
    public ResultData<List<KeyValueVo>> getLevel1(){

//        List<KeyValueVo>  list =  accountRechargeService.getlevel1UserList();

        return ResultUtil.success(accountRechargeService.getlevel1UserList());
    }
    @ApiOperation("获取一级充值列表")
    @GetMapping("/getLevel2")
    public ResultData<List<KeyValueVo>> getLevel2(){

        return ResultUtil.success(accountRechargeService.getlevel2UserList());
    }

}
