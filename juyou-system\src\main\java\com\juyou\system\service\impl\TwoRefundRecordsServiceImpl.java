package com.juyou.system.service.impl;

import java.util.List;
import com.juyou.common.utils.DateUtils;
import com.juyou.system.domain.vo.TwoRefundRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.TwoRefundRecordsMapper;

import com.juyou.system.service.ITwoRefundRecordsService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
public class TwoRefundRecordsServiceImpl implements ITwoRefundRecordsService 
{
    @Autowired
    private TwoRefundRecordsMapper twoRefundRecordsMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TwoRefundRecords selectTwoRefundRecordsById(Long id)
    {
        return twoRefundRecordsMapper.selectTwoRefundRecordsById(id);
    }
    /**
     * 查询【请填写功能名称】列表
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TwoRefundRecords> selectTwoRefundRecordsList(TwoRefundRecords twoRefundRecords)
    {
        return twoRefundRecordsMapper.selectTwoRefundRecordsList(twoRefundRecords);
    }
    /**
     * 新增【请填写功能名称】
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTwoRefundRecords(TwoRefundRecords twoRefundRecords)
    {
        twoRefundRecords.setCreateTime(DateUtils.getNowDate());

        return twoRefundRecordsMapper.insertTwoRefundRecords(twoRefundRecords);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param twoRefundRecords 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTwoRefundRecords(TwoRefundRecords twoRefundRecords)
    {
        twoRefundRecords.setUpdateTime(DateUtils.getNowDate());
        return twoRefundRecordsMapper.updateTwoRefundRecords(twoRefundRecords);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTwoRefundRecordsByIds(Long[] ids)
    {
        return twoRefundRecordsMapper.deleteTwoRefundRecordsByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTwoRefundRecordsById(Long id)
    {
        return twoRefundRecordsMapper.deleteTwoRefundRecordsById(id);
    }
}
