ALTER TABLE `two_gift_card_recharge_record` ADD COLUMN `source_group` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源群' AFTER `update_by`;

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '账号充值列表查询枚举', 'two_account_recharge_list_type', '0', 'admin', '2023-09-14 16:59:14', '', NULL, NULL);

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2050, '账号注册未领取账号数量', 2004, 10, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:accRecharge:unclaimedAccountCount', '#', 'admin', '2023-09-14 16:37:00', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2051, '我今日已充值金额', 2004, 11, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:accRecharge:todayRechargeAmount', '#', 'admin', '2023-09-14 16:37:19', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2052, '获取账号销售系统剩余账号列表', 2011, 11, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellcard:getSystemResidualAccount', '#', 'admin', '2023-09-14 16:37:44', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2053, '我今日已出售金额', 2011, 12, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellcard:todayAmountSold', '#', 'admin', '2023-09-14 16:38:01', '', NULL, '');

UPDATE `sys_menu` SET `menu_name` = '账号出售', `parent_id` = 2010, `order_num` = 2, `path` = 'sellcard', `component` = 'system/sellcard/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:sellcard:list', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-06 20:52:27', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:06', `remark` = 'sellcard菜单' WHERE `menu_id` = 2011;

UPDATE `sys_menu` SET `menu_name` = '未生效账号', `parent_id` = 2010, `order_num` = 3, `path` = 'notEffectiveList', `component` = 'system/sellNotEffectiveList/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:sellcard:notEffectiveList', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-11 14:15:56', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:27', `remark` = '' WHERE `menu_id` = 2025;

UPDATE `sys_menu` SET `menu_name` = '卡充值记录', `parent_id` = 2010, `order_num` = 4, `path` = 'rechargeCardRecordList', `component` = 'system/rechargeCardRecordList/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:record:list', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-12 16:23:50', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:33', `remark` = '' WHERE `menu_id` = 2032;

UPDATE `sys_menu` SET `menu_name` = '充值明细报表', `parent_id` = 2010, `order_num` = 5, `path` = 'accountRechargeReport', `component` = 'system/accountRechargeReport/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:accountRechargeReport:list', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-12 17:07:17', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:39', `remark` = '' WHERE `menu_id` = 2035;

UPDATE `sys_menu` SET `menu_name` = '出售明细报表', `parent_id` = 2010, `order_num` = 6, `path` = 'sellcardReport', `component` = 'system/sellcardReport/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:sellcardReport:list', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-12 17:09:55', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:46', `remark` = '' WHERE `menu_id` = 2039;

UPDATE `sys_menu` SET `menu_name` = '账号销售统计', `parent_id` = 2010, `order_num` = 7, `path` = 'sellStatistics', `component` = 'system/sellStatistics/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'system:sellStatistics:sellChatgroupList', `icon` = '#', `create_by` = 'admin', `create_time` = '2023-09-12 17:12:23', `update_by` = 'admin', `update_time` = '2023-09-14 17:47:53', `remark` = '' WHERE `menu_id` = 2045;
