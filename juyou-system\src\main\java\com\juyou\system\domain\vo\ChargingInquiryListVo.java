package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountRecharge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代充查询-list-vo
 */
@Data
@ApiModel("ChargingInquiryListVo")
public class ChargingInquiryListVo extends AccountRecharge {

    private static final long serialVersionUID = 3284282245955084937L;

    @ApiModelProperty("剩余等待时长(分)")
    private Integer surplusWaitDuration;

    @ApiModelProperty("一级充值人名称")
    private String primaryChargerName;

    @ApiModelProperty("二级充值人名称")
    private String secondaryChargerName;

}

