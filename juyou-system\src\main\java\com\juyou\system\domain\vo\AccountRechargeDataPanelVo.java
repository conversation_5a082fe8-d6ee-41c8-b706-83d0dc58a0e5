package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 账号充值-数据面板
 */
@Data
@ApiModel("AccountRechargeDataPanelVo")
public class AccountRechargeDataPanelVo implements Serializable {
    private static final long serialVersionUID = -7764307168737584968L;

    @ApiModelProperty("我今日已充值金额")
    private BigDecimal todayRechargeAmount;

    @ApiModelProperty("我的充值作废率")
    private BigDecimal rechargeCancelRate;

}
