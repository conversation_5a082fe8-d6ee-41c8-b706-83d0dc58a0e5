package com.juyou.web.controller.business;

import com.alibaba.fastjson.JSONArray;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.Quotation;
import com.juyou.system.service.IQuotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 报价Controller
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@RestController
@RequestMapping("/system/quotation")
public class QuotationController extends BaseController
{
    @Autowired
    private IQuotationService quotationService;

    /**
     * 查询报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:list')")
    @GetMapping("/list")
    public TableDataInfo list(Quotation quotation)  
    {
        startPage();
        if (quotation != null && quotation.getMaxFaceValue  () != null) {
            if (quotation.getMaxFaceValue().compareTo(new BigDecimal("-1")) == 0) {
                quotation.setMaxFaceValue(new BigDecimal("99999999"));
            }
        }
        List<Quotation> list = quotationService.selectQuotationList(quotation);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:quotation:list')")
    @GetMapping("/groupList")
    public TableDataInfo groupList()
    {
        List<Quotation> list = quotationService.selectQuotationGroup();
        return getDataTable(list);
    }

    /**
     * 导出报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:export')")
    @Log(title = "报价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Quotation quotation)
    {
        List<Quotation> list = quotationService.selectQuotationList(quotation);
        ExcelUtil<Quotation> util = new ExcelUtil<Quotation>(Quotation.class);
        util.exportExcel(response, list, "报价数据");
    }

    /**
     * 获取报价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(quotationService.selectQuotationById(id));
    }

    /**
     * 新增报价
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:add')")
    @Log(title = "报价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Quotation quotation)
    {
        return toAjax(quotationService.insertQuotation(quotation));
    }

    /**
     * 批量新增报价
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:add')")
    @Log(title = "报价", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batch(@RequestBody Quotation quotationList)
    {
        List<Quotation> list = JSONArray.parseArray(quotationList.getGroupNumber(), Quotation.class);
        return toAjax(quotationService.batch(list));
    }

    /**
     * 修改报价
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Quotation quotation)
    {
        return toAjax(quotationService.updateQuotation(quotation));
    }

    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价", businessType = BusinessType.INSERT)
    @PostMapping("/batchUpdateQuotation")
    public AjaxResult batchUpdateQuotation(@RequestBody Quotation quotationList)
    {
        List<Quotation> list = JSONArray.parseArray(quotationList.getGroupNumber(), Quotation.class);
        return toAjax(quotationService.batchUpdateQuotation(list));
    }

    /**
     * 删除报价
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:remove')")
    @Log(title = "报价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(quotationService.deleteQuotationByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价", businessType = BusinessType.UPDATE)
    @PutMapping("/openQuotation/{ids}")
    public AjaxResult openQuotation(@PathVariable Long[] ids)
    {
        return toAjax(quotationService.openQuotation(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价", businessType = BusinessType.UPDATE)
    @PutMapping("/openList/{ids}")
    public AjaxResult openList(@PathVariable Long[] ids)
    {
        return toAjax(quotationService.openList(ids));
    }
}
