package com.juyou.system.service.impl;

import java.util.List;
import com.juyou.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.SourceGroupsMapper;
import com.juyou.system.domain.SourceGroups;
import com.juyou.system.service.ISourceGroupsService;

/**
 * 来源群信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Service
public class SourceGroupsServiceImpl implements ISourceGroupsService
{
    @Autowired
    private SourceGroupsMapper sourceGroupsMapper;

    /**
     * 查询来源群信息
     *
     * @param id 来源群信息主键
     * @return 来源群信息
     */
    @Override
    public SourceGroups selectSourceGroupsById(Long id)
    {
        return sourceGroupsMapper.selectSourceGroupsById(id);
    }

    /**
     * 查询来源群信息列表
     *
     * @param sourceGroups 来源群信息
     * @return 来源群信息
     */
    @Override
    public List<SourceGroups> selectSourceGroupsList(SourceGroups sourceGroups)
    {
        return sourceGroupsMapper.selectSourceGroupsList(sourceGroups);
    }

    /**
     * 新增来源群信息
     *
     * @param sourceGroups 来源群信息
     * @return 结果
     */
    @Override
    public int insertSourceGroups(SourceGroups sourceGroups)
    {
        return sourceGroupsMapper.insertSourceGroups(sourceGroups);
    }

    /**
     * 修改来源群信息
     *
     * @param sourceGroups 来源群信息
     * @return 结果
     */
    @Override
    public int updateSourceGroups(SourceGroups sourceGroups)
    {
        sourceGroups.setUpdateTime(DateUtils.getNowDate());
        return sourceGroupsMapper.updateSourceGroups(sourceGroups);
    }

    /**
     * 批量删除来源群信息
     *
     * @param ids 需要删除的来源群信息主键
     * @return 结果
     */
    @Override
    public int deleteSourceGroupsByIds(Long[] ids)
    {
        return sourceGroupsMapper.deleteSourceGroupsByIds(ids);
    }

    /**
     * 删除来源群信息信息
     *
     * @param id 来源群信息主键
     * @return 结果
     */
    @Override
    public int deleteSourceGroupsById(Long id)
    {
        return sourceGroupsMapper.deleteSourceGroupsById(id);
    }
}
