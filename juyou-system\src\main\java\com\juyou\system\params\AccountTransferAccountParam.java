package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号充值-转交账号-参数
 */
@Data
@ApiModel("AccountTransferAccountParam")
public class AccountTransferAccountParam implements Serializable {


    private static final long serialVersionUID = 4544962398894482543L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户账号")
    private String userName;

    // 登录账号-前端不穿
    private Long currentUserId;

}

