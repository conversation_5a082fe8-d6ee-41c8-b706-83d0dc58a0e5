<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="单个新增" name="first">
        <el-form :model="form" :inline="true" :rules="rules" ref="form">
          <el-form-item label="群号" style="width: 30%;padding-left: 3%" prop="groupNumber">
            <el-input v-model="form.groupNumber" placeholder="请输入群号"></el-input>
          </el-form-item>
          <el-form-item label="卡种" style="width: 60%;padding-left: 3%;">
            <el-select v-model="form.cardType" placeholder="请选择卡类型" filterable>
              <el-option
                v-for="item in cardTypeList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="国家" style="width: 30%;padding-left: 3%;" prop="country">
            <el-input v-model="form.country" placeholder="请输入国家"></el-input>
          </el-form-item>
          <el-form-item label="面值范围" style="width: 30%;padding-left: 3%;" prop="minFaceValue">
            <el-input v-model="form.minFaceValue" placeholder="最小面值" type="number" style="width: 100px;"></el-input>
             ~
            <el-input v-model="form.maxFaceValue" placeholder="最大面值" type="number" style="width: 100px;"></el-input>
            (面值范围-1为无限)
          </el-form-item>
          <el-form-item label="价格" style="width: 30%;padding-left: 3%;" prop="price">
            <el-input v-model="form.price" placeholder="请输入价格" type="number"></el-input>
          </el-form-item>
          <el-form-item label="备注" style="width: 100%;padding-left: 3%;" prop="remark">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 5}"
              placeholder=""
              v-model="form.remark" style="width: 500px;">
            </el-input>
          </el-form-item>
          <hr>
          <div style="text-align: center">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="addOffer">提交</el-button>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="批量新增" name="second">
        <el-form :model="forms" :inline="true" :rules="rulesForms" ref="forms">
          <el-form-item label="群号" style="width: 30%;padding-left: 3%" prop="groupNumber">
            <el-input v-model="forms.groupNumber" placeholder="请输入群号"></el-input>
          </el-form-item>
          <el-form-item label="卡种" style="width: 60%;padding-left: 3%;">
            <el-select v-model="forms.cardType" placeholder="请选择卡类型" filterable>
              <el-option
                v-for="item in cardTypeList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报价信息：" prop="cardCode" style="width: 100%;padding-left: 3%;">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 100}"
              placeholder=""
              v-model="forms.cardCode" @blur="AddListRowTest()"
              style="width: 450px;">
            </el-input>
            <div style="color: #999b9f;font-size: 12px">
              格式：  国家---面值范围---价格---备注
              <br>(面值范围: 50/100 0/-1 注意-1为无限)
            </div>
          </el-form-item>
          <el-form-item label="报价列表：" prop="cardCode" style="width: 100%;padding-left: 3%;">
            <el-form size="mini" :inline="true" :model="carMiList" :rules="rules" ref="offerList">
              <el-table :data="carMiList" style="width: 1000px;" border stripe>
                <el-table-column align="center" label="卡种">
                  <template slot-scope="scope">
                    <el-select
                      @change=""
                      filterable size="mini" style="width: 140px"
                      v-model="scope.row.cardType"
                      placeholder="请选择">
                      <el-option v-for="item in cardTypeList  " :key="item.dictValue" :label="item.dictLabel"
                                 :value="item.dictValue"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="发行国家">
                  <template slot-scope="scope">
                    <el-input
                      @change=""
                      size="mini" style="width: 140px"
                      v-model="scope.row.country"
                      maxlength="10"></el-input>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="minFaceValue" label="面值范围">
                  <template slot-scope="scope">
                    <el-input
                      @change="" type="number"
                      size="mini" style="width: 70px"
                      v-model="scope.row.minFaceValue"
                      maxlength="10"></el-input> ~
                    <el-input
                      @change="" type="number"
                      size="mini" style="width: 70px"
                      v-model="scope.row.maxFaceValue"
                      maxlength="10"></el-input>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="price" label="价格">
                  <template slot-scope="scope">
                    <el-input
                      @change=""
                      size="mini" style="width: 140px"
                      v-model="scope.row.price"
                      maxlength="10"></el-input>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="备注" prop="remark">
                  <template slot-scope="scope">
                    <el-input
                      @change=""
                      size="mini" style="width: 140px"
                      v-model="scope.row.remark"
                      maxlength="10"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </el-form-item>
          <hr>
          <div style="text-align: center">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="addOffers">提交</el-button>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {getDicts} from "@/api/newSystem/dict/data";
import {addQuotation, batch} from "@/api/newSystem/offer";

export default {
  name: "addOffer",
  data() {
    return {
      activeName: 'first',
      form: {},
      cardTypeList: [],
      rules: {
        groupNumber: [
          {required: true, message: '请输入群号', trigger: 'blur'}
        ],
        cardType: [
          {required: true, message: '请选择卡类型', trigger: 'change'}
        ],
        country: [
          {required: true, message: '请输入国家', trigger: 'blur'}
        ],
        price: [
          {required: true, message: '请输入价格', trigger: 'blur'}
        ],
        remark: [
          {required: true, message: '请输入备注', trigger: 'blur'}
        ]
      },
      forms: {},
      rulesForms: {
        groupNumber: [
          {required: true, message: '请输入群号', trigger: 'blur'}
        ],
        cardType: [
          {required: true, message: '请选择卡类型', trigger: 'change'}
        ],
        cardCode: [
          {required: true, message: '请输入报价信息', trigger: 'blur'}
        ]
      },
      carMiList: []
    }
  },
  created() {
    getDicts("business_gift_card_types").then(response => {
        this.cardTypeList = response.data;
      }
    );
  },
  methods: {
    cancel() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({path: "/offer/offer"}).catch(() => {})
    },
    AddListRowTest() {
      if (this.forms.cardCode === '' || this.forms.cardCode === null || this.forms.cardCode === undefined) {
        this.$message({
          message: '报价信息不能为空',
          type: 'warning'
        });
        return;
      }
      if (this.forms.groupNumber === '' || this.forms.groupNumber === null || this.forms.groupNumber === undefined) {
        this.$message({
          message: '群号不能为空',
          type: 'warning'
        });
        return;
      }
      if (this.forms.cardType === '' || this.forms.cardType === null || this.forms.cardType === undefined) {
        this.$message({
          message: '卡种不能为空',
          type: 'warning'
        });
        return;
      }
      this.carMiList = [];
      let row = this.forms.cardCode.split('\n');
      for (let i = 0; i < row.length; i++) {
        let rowData = row[i].split('---');
        if (rowData.length === 4) {
          let obj = {};
          obj.country = rowData[0];
          obj.cardType = this.forms.cardType;
          obj.groupNumber = this.forms.groupNumber;
          obj.price = rowData[2];
          let faceValue = rowData[1].split('/');
          if (faceValue.length === 2) {
            obj.minFaceValue = faceValue[0];
            obj.maxFaceValue = faceValue[1];
          } else {
            this.$message({
              message: '面值范围格式错误(例如：美国---50/100---50---备注)',
              type: 'warning'
            });
            return;
          }
          obj.remark = rowData[3];
          this.carMiList.push(obj);
        } else {
          this.$message({
            message: '报价信息格式错误(格式：国家---面值范围---价格---备注)',
            type: 'warning'
          });
          return;
        }
      }
    },
    addOffers() {
      this.$refs["forms"].validate(valid => {
        if (valid) {
          if (this.carMiList.length === 0) {
            this.$message({
              message: '报价信息不能为空',
              type: 'warning'
            });
            return;
          }
          let quotationList = {groupNumber : JSON.stringify(this.carMiList)}
          batch(quotationList).then(response => {
            if (response.code === 200) {
              this.$message({
                message: '新增成功',
                type: 'success'
              });
              this.$store.dispatch('tagsView/delView', this.$route)
              this.$router.push({path: "/offer/offer"}).catch(() => {})
            } else {
              this.$message({
                message: response.msg,
                type: 'warning'
              });
            }
          })
        }
      })
    },
    addOffer() {
      this.$refs["form"].validate(valid => {
        if (valid) {
         if (!this.form.minFaceValue || !this.form.maxFaceValue) {
           this.$message({
             message: '面值范围不能为空',
             type: 'warning'
           });
           return;
         }
          addQuotation(this.form).then(response => {
            if (response.code === 200) {
              this.$message({
                message: '新增成功',
                type: 'success'
              });
              this.$store.dispatch('tagsView/delView', this.$route)
              this.$router.push({path: "/offer/offer"}).catch(() => {})
            } else {
              this.$message({
                message: response.msg,
                type: 'warning'
              });
            }
          })
        }
      })
    },
  }
}
</script>

<style scoped>

</style>
