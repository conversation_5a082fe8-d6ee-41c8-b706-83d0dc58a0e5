package com.juyou.system.params;

import com.juyou.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账号充值-参数
 */
@Data
@ApiModel("AccountRechargeParam")
public class AccountRechargeParam implements Serializable {

    @ApiModelProperty("账号充值id")
    private Integer acid;

    @ApiModelProperty("ID余额")
    private BigDecimal rechargeAmt;

    @ApiModelProperty("卡密列表")
    private List<RechargeGiftCardRechargeRecordListVo> cardList;

    @ApiModelProperty("状态: 2 部分充值 3 完成充值")
    private Integer status;

    @ApiModelProperty("归属业务员名称")
    private String  idUser;

    @ApiModelProperty("登录用户id")
    private Integer currentUserId;

    @ApiModelProperty("当前登录用户id：后端使用")
    private String currentUsername;

    /**
     * 充值礼品卡记录
     */
    @Data
    @ApiModel("RechargeGiftCardRechargeRecord")
    public static class RechargeGiftCardRechargeRecordListVo{

        @ApiModelProperty("卡密")
        private String giftCard;

        @ApiModelProperty("面值")
        private BigDecimal faceValue;

        @ApiModelProperty("进价(充值账号的单价)")
        private BigDecimal buyPrice;

        @ApiModelProperty("来源群")
        private String sourceGroup;

        @ApiModelProperty("是否质押30分钟: 0 否 1 是")
        private String pledgeThirtyMinutes;

    }

}
