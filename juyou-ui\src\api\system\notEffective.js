import request from '@/utils/request'

// 查询未生效列表
export function notEffectiveList(data = {}) {
  return request({
    url: '/system/sellcard/notEffectiveList',
    method: 'get',
    params: data
  })
}

// 查询用户列表
export function getListAll(data = {}) {
  return request({
    url: '/system/user/getListAll',
    method: 'post',
    data: data
  })
}


// 查询待出售列表
export function getAccountSellPendingSaleList(data = {}) {
  return request({
    url: '/accountSellSoldReport/getAccountSellSoldReportList',
    method: 'get',
    params: data
  })
}

export function batchWriteOff(data = {}){
  return request({
    url: '/accountSellSoldReport/batchWriteOff',
    method: 'post',
    data: data
  })
}
