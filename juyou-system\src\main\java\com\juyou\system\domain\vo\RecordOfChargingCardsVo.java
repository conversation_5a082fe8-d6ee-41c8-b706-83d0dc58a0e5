package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class RecordOfChargingCardsVo {

    //礼品卡
    @ApiModelProperty("礼品卡")
    private String giftCard;
    //区域
    @ApiModelProperty("区域")
    private String accountZone;
    //来源群
    @ApiModelProperty("来源群")
    private String sourceGroup;
    // 汇率
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
    //面值
    @ApiModelProperty("面值")
    private BigDecimal faceValue;
    //成本金额

    @ApiModelProperty("成本金额")
    private BigDecimal buyAmt;

//    public void setAccountPwd(String accountPwd) {
//        this.accountPwd = "******";
//    }

    //是否压制30分钟
    @ApiModelProperty("是否压制30分钟")
    private String pledgeThirtyMinutes;
    //账户
    @ApiModelProperty("账户")
    private String account;
    //账号密码
    @ApiModelProperty("密码")
    private String accountPwd;
    //执行时间
    @ApiModelProperty("执行时间")
    private String executionTime;
    //充值人
    @ApiModelProperty("充值人")
    private String receiveUser;



}
