package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号销售系统剩余账号-voList
 */
@Data
@ApiModel("AccountSellResidualAccountVoList-账号销售系统剩余账号-voList")
public class AccountSellResidualAccountVoList implements Serializable {

    private static final long serialVersionUID = -8172334494369747134L;

    @ApiModelProperty("面值(实际充值金额)")
    private Double cardBalance;

    @ApiModelProperty("库等级")
    private String chargeStage;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("钱包区域")
    private String walletArea;



}
