package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 出售群统计查询参数
 */
@Data
@ApiModel("SellGroupDetailSearchParam")
public class SellGroupDetailSearchParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -1635654909192557133L;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("面值(实际充值金额)")
    private BigDecimal cardBalance;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellTime;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellTime;

}
