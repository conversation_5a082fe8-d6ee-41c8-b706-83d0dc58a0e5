package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号统计作废参数
 */
@Data
@ApiModel("AccountStatisticsCancelParam-账号统计作废参数")
public class AccountStatisticsCancelParam implements Serializable {

    private static final long serialVersionUID = -8217283079742126694L;

    @ApiModelProperty("作废开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelStartDate;

    @ApiModelProperty("作废结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelEndDate;

}
