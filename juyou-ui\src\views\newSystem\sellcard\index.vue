<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="95px"
    >
      <el-form-item label="账号：" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="充值金额：">
        <el-col :span="11">
          <el-input
            v-model="queryParams.startShouldAmt"
            placeholder="请输入充值金额"
          ></el-input>
        </el-col>
        <el-col :span="1"><span>~</span></el-col>
        <el-col :span="11">
          <el-input
            v-model="queryParams.endShouldAmt"
            placeholder="请输入充值金额"
          ></el-input>
        </el-col>
      </el-form-item>

      <el-form-item label="出售状态：" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择出售状态"
          :style="{ width: '80%' }"
          clearable
        >
          <el-option
            v-for="dict in [
              { label: '全部', value: '1' },
              { label: '待出售', value: '2' },
              { label: '已出售', value: '3' },
              { label: '作废', value: '4' },
            ]"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="领取日期：" prop="receiveTime">
        <el-date-picker
          v-model="queryParams.receiveTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          clearable
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="出售日期：" prop="sellTime">
        <el-date-picker
          v-model="queryParams.sellTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" size="mini" @click="yesterday"
          >昨天</el-button
        >
        <el-button type="primary" size="mini" @click="today">今天</el-button>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="7">
        <div style="font-size: 18px; margin: 5px">
          我今日已出售金额：
          <span style="font-size: 24px">${{ todayAmountSold }}</span>
        </div>
      </el-col>

      <el-col :span="7">
        <div style="font-size: 18px; margin: 5px">
          我的出售作废率：
          <span style="font-size: 24px">{{ obsolescenceRate? obsolescenceRate.toFixed(2) : 0}}%</span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <div style="font-size: 18px; padding: 8px">
          <span>一级库剩余账号：</span>
        </div>
      </el-col>

      <el-col :span="2" v-for="(itme, index) in level1Library" :key="index">
        <div style="font-size: 18px">
          <el-button
            style="font-size: 18px"
            type="text"
            @click="handleClaimAccount(itme)"
          >${{ itme.cardBalance }}：{{ itme.count }}个
          </el-button>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <div style="font-size: 18px; padding: 8px">
          <span>二级库剩余账号：</span>
        </div>
      </el-col>

      <el-col :span="2" v-for="(itme, index) in level2Library" :key="index">
        <div style="font-size: 18px">
          <el-button
            style="font-size: 18px"
            type="text"
            @click="handleClaimAccount(itme)"
          >${{ itme.cardBalance }}：{{ itme.count }}个
          </el-button>
        </div>
      </el-col>
    </el-row>
      <el-button type="primary" v-hasPermi="['system:sellcard:remove']" @click="putItIntoTheSecondaryLibrary"
      >放入二级库</el-button
      >
      <el-button type="primary" v-hasPermi="['system:sellcard:pinned']" @click="batchCopyToClip(sellcardList)"
        >批量复制账号</el-button
      >
      <el-button type="primary" v-hasPermi="['system:sellcard:batchSell']" @click="handleBulkSales">批量出售</el-button>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>


    <!--     <el-table v-loading="loading" :data="sellcardList" @selection-change="handleSelectionChange">-->
    <el-table
      v-loading="loading"
      :data="sellcardList"
      tooltip-effect="dark"
      border
      :summary-method="getSummaries"
      show-summary
      @select="selectFn"
      @select-all="selectAllFn"
      ref="mutipleTable"
      :row-class-name="tableRowClassName"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :selectable="selectableFn"
      />
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">
            {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
          scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
        scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="accountZone" />
      <el-table-column label="出售状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ formatStatus(scope.row.status) }}</span>
        </template>
      </el-table-column>
       <el-table-column label="充值阶段" align="center" prop="chargeStage" >
         <template slot-scope="{row}">
           <span>{{ row.chargeStage === '1' ?"一级充值":"二级充值" }}</span>
         </template>
       </el-table-column>
      <el-table-column label="ID余额" align="center" prop="cardBalance" />
      <el-table-column label="出售单价(CNY)" align="center" prop="sellPrice" />
      <el-table-column label="出售金额(CNY)" align="center" prop="sellAmt" />
      <el-table-column label="领取人" align="center" prop="receiveUser" />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        width="180"
      ></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="170"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.pinnedStatus == 2"
            @click="pinned(scope.row)"
            v-hasPermi="['system:sellcard:pinned']"
            >置顶
          </el-button>
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.pinnedStatus == 1"
            @click="pinned(scope.row)"
            v-hasPermi="['system:sellcard:pinned']"
            >取消置顶
          </el-button>
          <el-button
            v-if="scope.row.status === '2'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:sellcard:edit']"
            >出售
          </el-button>
          <el-button
            v-if="(scope.row.chargeStage === '1' || scope.row.chargeStage === '2') &&  scope.row.status==='2'"
            size="mini"
            type="text"
            icon="el-icon-back"
            @click="regressionData(scope.row)"
            v-hasPermi="['system:sellcard:edit']"
          >退回
          </el-button>
          <el-button
            v-if="scope.row.status !== '4'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:sellcard:restore']"
            >作废
          </el-button>
          <el-button
            v-if="scope.row.status !== '2'"
            size="mini"
            type="text"
            @click="sellDetail(scope.row)"
            v-hasPermi="['system:sellcard:save']"
            >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 领取账号对话 -->
    <el-dialog
      title="账号领取"
      :visible.sync="accountOpen"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="750px"
      append-to-body
    >
      <el-form
        ref="accountForm"
        :model="accountForm"
        :rules="accountRules"
        label-width="90px"
        label-position="right"
        :inline="true"
      >
        <el-form-item label="领取个数" prop="count">
          <el-input
            v-model="accountForm.count"
            placeholder="请输入领取个数"
            :disabled="false"
          />
        </el-form-item>
        <el-form-item label="面值" prop="shouldAmtList">
          <el-input
            v-model="accountForm.shouldAmtList"
            placeholder="请输入面值"
            :disabled="false"
          />
          <!-- <el-select v-model="accountForm.shouldAmtList" multiple placeholder="请选择面值" :style="{ width: '95%' }" clearable>
            <el-option v-for="dict in dict.type.receive_face_value" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAccountForm">领取</el-button>
        <el-button @click="ccountCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 转交账号对话 -->
    <el-dialog
      title="转交账号"
      :visible.sync="transmitOpen"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      append-to-body
    >
      <div>请将你已领取的账号转交给你的小伙伴</div>
      <el-form
        ref="transferForm"
        :model="transferForm"
        :rules="transferRules"
        label-width="90px"
        label-position="right"
        :inline="true"
      >
        <el-form-item label="伙伴姓名" prop="userId">
          <el-select
            v-model="transferForm.userId"
            placeholder="请选择伙伴姓名"
            :style="{ width: '80%' }"
            clearable
          >
            <el-option
              v-for="(itme,index) in partneList"
              :key="index"
              :label="itme.userName"
              :value="itme.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTransferForm">确定</el-button>
        <el-button @click="transmitCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 单个出售 -->
    <el-dialog
      title="单个出售"
      :visible.sync="individualSalesVisible"
      width="750px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <el-form
        ref="individualSalesForm"
        :rules="individualSalesRules"
        :model="individualSalesForm"
        label-width="100px"
        label-position="right"
        :inline="true"
      >
        <el-form-item label="账号" prop="accountName">
          <el-input
            v-model="individualSalesForm.accountName"
            placeholder="请输入账号"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="密码" prop="accountPwd">
          <el-input
            v-model="individualSalesForm.accountPwd"
            placeholder="请输入密码"
            :disabled="true"
          />
        </el-form-item>

        <el-form-item label="区域" prop="accountZone">
          <el-select
            v-model="individualSalesForm.accountZone"
            filterable
            placeholder="请选择区域"
          >
            <el-option value="" label="全部"></el-option>
            <el-option
              v-for="(item,index) in zonrList"
              :key="index"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="应充值金额" prop="shouldAmt">
          <el-input v-model="individualSalesForm.shouldAmt" placeholder="请输入应充值金额" :disabled="true" />
        </el-form-item> -->
        <el-form-item label="出售单价" prop="sellPrice">
          <el-input
            v-model="individualSalesForm.sellPrice"
            placeholder="请输入出售单价"
            @input="individualSalesCheckCartAmt"
          />
        </el-form-item>

        <el-form-item label="实际充值金额" prop="cardBalance">
          <el-input
            v-model="individualSalesForm.cardBalance"
            placeholder="请输入实际充值金额"
            :disabled="true"
          />
        </el-form-item>

        <el-form-item label="出售金额" prop="sellAmt">
          <el-input
            v-model="individualSalesForm.sellAmt"
            placeholder="请输入出售金额"
            :disabled="true"
          />
        </el-form-item>
        <br />
        <el-form-item label="出售对象" prop="objectsForSale">
          <el-radio-group @input="onceChooseObjectsForSale" v-model="individualSalesForm.objectsForSale">
            <el-radio label="0">外部出卡群</el-radio>
            <el-radio label="1">内部903</el-radio>
          </el-radio-group>
        </el-form-item>
        <br />
        <el-form-item label="出售群" prop="sellChatgroupName">
          <el-select :disabled="individualSalesForm.objectsForSale === '1'" v-model="individualSalesForm.sellChatgroupName" placeholder="请选择出售群">
            <el-option
              v-for="item in groupList"
              :key="item.groupNumber"
              :label="item.groupNumber"
              :value="item.groupNumber">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="售卖人" prop="custName">
          <el-input
            :disabled="individualSalesForm.objectsForSale === '1'"
            v-model="individualSalesForm.custName"
            placeholder="请输入售卖人"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">出售</el-button>
        <el-button
          type="primary"
          @click="
            copyToClip(
              `${individualSalesForm.accountName}----${individualSalesForm.accountPwd}`
            )
          "
          >一键复制账号
        </el-button>
        <el-button @click="individualSaleCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量出售弹窗 -->
    <el-dialog
      title="批量出售"
      :visible.sync="bulkSalesVisible"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="()=>{getList()}"
    >
      <el-form
        ref="bulkSalesForm"
        :model="bulkSalesForm"
        :rules="bulkSalesRules"
        label-width="100px"
        label-position="right"
        :inline="true"
      >
        <el-form-item label="出售对象" prop="objectsForSale">
          <el-radio-group @input="chooseObjectsForSale" v-model="bulkSalesForm.objectsForSale">
            <el-radio label="0">外部出卡群</el-radio>
            <el-radio label="1">内部903</el-radio>
          </el-radio-group>
        </el-form-item>
        <br />
        <el-form-item label="出售群" prop="sellChatgroupName">
          <el-select
            :disabled="bulkSalesForm.objectsForSale === '1'"
            v-model="bulkSalesForm.sellChatgroupName"
            placeholder="请选择出售群">
            <el-option
              v-for="item in groupList"
              :key="item.groupNumber"
              :label="item.groupNumber"
              :value="item.groupNumber">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="售卖人-客户" prop="custName">
          <el-input
            :disabled="bulkSalesForm.objectsForSale === '1'"
            v-model="bulkSalesForm.custName"
            placeholder="请输入售卖人-客户"
          />
        </el-form-item>
        <el-form-item label="出售单价" prop="sellPrice">
          <el-input
            v-model="bulkSalesForm.sellPrice"
            placeholder="请输入出售单价"
            @input="checkCartAmt"
          />
        </el-form-item>
        <el-form-item label="区域" prop="accountZone">
          <el-select
            v-model="bulkSalesForm.accountZone"
            filterable
            placeholder="请选择区域"
          >
            <el-option
              v-for="(item,index) in zonrList"
              :key="index"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出售数量" prop="sellPrice">
          <el-input
            :value="bulkSalesSelectsMap.length"
            placeholder="请输入出售数量"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="出售总额" prop="sellPrice">
          <el-input
            v-model="bulkSalesForm.totalSalesAmount"
            placeholder="请输入出售总额"
            :disabled="true"
          />
        </el-form-item>
      </el-form>
      <el-form :model="bulkSalesQueryForm" :inline="true">
        <el-form-item label="账号：" prop="accountName">
          <el-input
            v-model="bulkSalesQueryForm.accountName"
            placeholder="请输入账号搜索定位"
            clearable
            @keyup.enter.native="bulkSalesQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="bulkSalesQuery"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>

      <el-table
        :data="bulkSalesSelectsMap"
        max-height="260"
        ref="rw_table"
        :summary-method="getBulkSummaries"
        style="width: 720px"
        highlight-current-row
        show-summary
      >
        <el-table-column
          label="账号"
          align="center"
          prop="accountName"
          width="200"
        >
          <template slot-scope="scope">
            <el-button style="font-size: 18px;" type="text"
                       @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">{{
                scope.row.accountName?
                  scope.row.accountName.substr(0,4) + '***' +
                  scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="密码"
          align="center"
          prop="accountPwd"
          width="200"
        >
          <template slot-scope="scope">
            {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
            scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
          </template>
        </el-table-column>
        <el-table-column
          label="出售状态"
          align="center"
          prop="status"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ formatStatus(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="应充值金额" align="center" prop="shouldAmt" /> -->
        <el-table-column
          label="实际充值金额"
          align="center"
          prop="cardBalance"
        />
        <el-table-column label="出售单价" align="center" prop="sellPrice" />
        <el-table-column label="出售金额" align="center" prop="sellAmt" />
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="145"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status !== '4'"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:sellcard:remove']"
              >作废
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination v-show="bulkSalesSelectsMap.length > 0" :total="bulkSalesSelectsMap.length" :page="1" :limit="5" /> -->
      <div slot="title" class="dialog-heard">
        <el-button type="primary" @click="bulkSalesVisibleSubmit"
          >批量出售</el-button
        >
        <el-button type="primary" @click="batchCopyToClip(bulkSalesSelectsMap)"
          >一键复制账号</el-button
        >
        <el-button @click="bulkSalesCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 作废弹窗 -->
    <el-dialog title="作废｜作废后无法再出售，请确认是否作废？" :visible.sync="nullifyVisible"
               :close-on-click-modal="false"
               :close-on-press-escape="false" width="600px" append-to-body>
      <el-form ref="nullifyForm" :model="nullifyForm" :rules="nullifyFormRules" label-width="100px"
               label-position="right">

        <el-form-item label="账号：" prop="accountName">
          <el-input type="text" v-model="nullifyForm.accountName" :disabled="true"/>
        </el-form-item>

        <el-form-item label="作废原因：" prop="cancelReason">
          <el-input type="text" placeholder="请输入作废原因" v-model="nullifyForm.cancelReason" />
        </el-form-item>

        <el-form-item label="作废图片:">
          <image-upload v-model="nullifyForm.cancelImgs" :limit="3" :uploadImgUrl="uploadImgUrl" :isShowTip="false"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitNullifyForm">作废</el-button>
        <el-button @click="nullifyCancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  listSellcard,
  getSellcard,
  delSellcard,
  addSellcard,
  updateSellcard,
  updatePinned,
  cancelSellcard,
  sellcardBatchAccountSell,
  receiveAccount,
  sellcardTransferAccount,
  sellcardSell,
  getSystemResidualAccountList,
  getTodayAmountSold,
  getAwaitReceiveTotalAmount,
  putItIntoTheSecondaryLibrary,
  getSellVoidedBalances,
  returnAccountSell,
  verifyGoogleCaptcha
} from '@/api/newSystem/sellcard'
import {
  deactivateYourAccount,
  getListExcludeMeRecharge,
  theFirstLevelRechargeIsDirectlyCompleted
} from '@/api/newSystem/accRecharge'
import { getDicts } from "@/api/newSystem/dict/data";
import Cookies from "js-cookie";
import { resetUserPwd } from '@/api/newSystem/user'
import { sellingGroupsList } from '@/api/newSystem/notEffective'
import log from "@/views/monitor/job/log.vue";

export default {
  name: "Sellcard",
  dicts: ["sell_status", "receive_face_value", "recharge_status"],
  data() {
    return {
      groupList:[],
      pinnedStatus: 1, //置顶状态
      bulkSalesQueryForm: {}, //批量出售定位查询
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/commonFile/fileUpload", //,//dev-api/commonFile/fileUpload
      todayAmountSold: 0,
      obsolescenceRate: 0,
      level1Library: [], //一级库剩余账户
      level2Library: [], //二级库剩余账户
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      //领取日期时间范围数组
      receiveTime: [],
      //出售日期时间范围数组
      sellTime: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // sellcard表格数据
      sellcardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        receiveTime:[],
        acid: null,
        accountName: null,
        accountPwd: null,
        accountZone: null,
        status: null,
        cardBalance: null,
        buyPrice: null,
        buyAmt: null,
        sellPrice: null,
        sellAmt: null,
        sellChatgroupName: null,
        custName: null,
        createTime: null,
        createBy: null,
      },
      //领取账号弹出
      accountOpen: false,
      //转交账号弹窗
      transmitOpen: false,
      // 领取账号相关数据
      accountForm: {},
      accountRules: {
        accountNum: [
          { required: true, message: "请输入领取个数", trigger: "blur" },
        ],
      },
      individualSalesRules: {
        sellPrice: [
          { required: true, message: "请输入出售单价", trigger: "blur" },
        ],
        sellChatgroupName: [
          { required: true, message: "请输入出售群", trigger: "blur" },
        ],
        custName: [
          { required: true, message: "请输入售卖人", trigger: "blur" },
        ],
      },
      partneList: [],
      transferForm: {},
      transferRules: {
        partneName: [
          { required: true, message: "请输入领取个数", trigger: "blur" },
        ],
      },

      // 批量出售弹窗数据
      bulkSalesRules: {
        sellPrice: [
          { required: true, message: "请输入出售单价", trigger: "blur" },
        ],
        sellChatgroupName: [
          { required: true, message: "请选择出售群", trigger: "blur" },
        ],
        custName: [
          { required: true, message: "请输入售卖人", trigger: "blur" },
        ],
      },
      bulkSalesVisible: false,
      bulkSalesForm: {
        objectsForSale:'0'
      },
      bulkSalesSelects: [], //批量出售选中数组
      copySelects: [], //批量复制选中的数组
      bulkSalesSelectsMap: [],
      //单个出售弹窗数据
      individualSalesVisible: false,
      individualSalesForm: {
        objectsForSale:'0'
      },

      //出售详情弹窗
      sellDetailVisible: false,
      sellDetailForm: {},

      // 出售账号详情弹窗
      sellcardReportDetailsVisible: false,
      sellcardReportDetailsForm: {},

      //作废弹窗相关数据
      nullifyVisible: false,
      nullifyForm: {},
      nullifyFormRules: {
        cancelReasonType: [
          { required: true, message: "请输入作废原因", trigger: "blur" },
        ],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      zonrList: [],
    };
  },
  created() {
    this.getTodayAmountSold();
    this.getListExcludeMe();
    this.getList();
    // this.setTime()
    let zone = this.$route.params.accountZone;
    if (zone != undefined || zone != "") {
      this.$set(this.queryParams, "accountZone", zone);
      this.$set(this.queryParams, "idType", "0");
    }
    this.into();

  },
  activated() {
    setTimeout(()=>{
      this.getTodayAmountSold();
      this.getListExcludeMe();
      this.getList();
    },600)

  },
  methods: {
    setTime(){
        // 七天前
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 7);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.$set(this.queryParams, "receiveTime", [
        `${yesterday} 00:00:00`,
        `${today} 00:00:00`,
      ]);
      this.$set(this.queryParams, "sellTime", [
        `${yesterday} 00:00:00`,
        `${today} 00:00:00`,
      ]);
    },
    chooseObjectsForSale(e){
      if (e == '1'){
        this.bulkSalesForm.sellChatgroupName = '903'
        this.bulkSalesForm.custName = '903'
      }else {
        this.bulkSalesForm.sellChatgroupName = ''
        this.bulkSalesForm.custName = ''
      }
    },
    onceChooseObjectsForSale(e){
      if (e == '1'){
        this.individualSalesForm.sellChatgroupName = '903'
        this.individualSalesForm.custName = '903'
      }else {
        this.individualSalesForm.sellChatgroupName = ''
        this.individualSalesForm.custName = ''
      }
    },
    regressionData(e){

      let  level_title= '退回一级充值'
      if (e.chargeStage==='2')
        level_title = '退回二级充值'
      this.$prompt('请输入退回原因', level_title, {
        confirmButtonText: level_title,
        cancelButtonText: "取消",
        inputPattern: /^.{1,1000}$/,
        inputErrorMessage: '退回原因不能为空',
        closeOnClickModal: false,
      }).then(async({ value }) => {
        e.cancelReason = value
        await returnAccountSell(e)
        this.$modal.msgSuccess('退回成功')
        this.getList()
      }).catch(() => {});
    },

    async putItIntoTheSecondaryLibrary(){
      this.$modal.confirm(`请确认是否将已选择的账号，放入代充二级库？`).then(async () => {
        try{
          await putItIntoTheSecondaryLibrary(this.copySelects);
        }catch (e) {
          this.loading = false
          return
        }
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('放入成功')
      })
    },
    into() {
      getDicts("account_zone").then((response) => {
        this.zonrList = response.data;
      });
      sellingGroupsList().then(e=>{
        this.groupList = e.rows
      })

    },
    yesterday() {
      // 昨天
      const date = new Date();
      const enddate = new Date(date);
      enddate.setDate(date.getDate() - 1);
      const yesterday =
        enddate.getFullYear() +
        "-" +
        (enddate.getMonth() + 1) +
        "-" +
        enddate.getDate();
      // 今天
      const today = this.parseTime(new Date(), "{y}-{m}-{d}");
      this.$set(this.queryParams, "receiveTime", [
        `${yesterday} 00:00:00`,
        `${today} 00:00:00`,
      ]);
      this.$set(this.queryParams, "sellTime", [
        `${yesterday} 00:00:00`,
        `${today} 00:00:00`,
      ]);

      // const date = new Date();
      // const enddate = new Date(date);
      // enddate.setDate(date.getDate() - 1);
      // const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
      this.$set(this.queryParams, 'receiveTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
      this.$set(this.queryParams, 'sellTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
    },
    today() {
      const today = this.parseTime(new Date(), "{y}-{m}-{d}");
      const end = new Date();
      end.setDate(end.getDate() + 1);
      const endStr =
        end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
      this.$set(this.queryParams, "receiveTime", [
        `${today} 00:00:00`,
        `${endStr} 00:00:00`,
      ]);
      this.$set(this.queryParams, "sellTime", [
        `${today} 00:00:00`,
        `${endStr} 00:00:00`,
      ]);
      // const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.$set(this.queryParams, 'receiveTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
      this.$set(this.queryParams, 'sellTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
    },
    //我今日已出售金额
    async getTodayAmountSold() {
      const todayAmountSold_res = await getTodayAmountSold(
        0
      );
      if (todayAmountSold_res.code === 200) {
        this.todayAmountSold = todayAmountSold_res.data || 0;
      }
      const accountList_res = await getSystemResidualAccountList(
        this.queryParams.accountZone,
        this.queryParams.idType
      );
      if (accountList_res.code === 200) {
        this.level1Library = accountList_res.data.filter(
          (e) => e.chargeStage === "1"
        );
        this.level2Library = accountList_res.data.filter(
          (e) => e.chargeStage === "2"
        );
      }

    },
    //单个出售单价计算
    individualSalesCheckCartAmt() {
      if (this.individualSalesForm.sellPrice) {
        this.individualSalesForm.sellAmt = (
          Number(this.individualSalesForm.cardBalance) *
          Number(this.individualSalesForm.sellPrice)
        ).toFixed(2);
      }
    },
    //计算出售金额
    checkCartAmt() {
      let totalSalesAmount = 0;
      let sellAmt = 0;
      this.bulkSalesSelectsMap.forEach((itme) => {
        if (this.bulkSalesForm.sellPrice) {
          //单条出售金额
          sellAmt = (
            Number(itme.cardBalance) * Number(this.bulkSalesForm.sellPrice)
          ).toFixed(2);
          itme.sellPrice = this.bulkSalesForm.sellPrice;
          itme.sellAmt = sellAmt;
          totalSalesAmount = (
            Number(totalSalesAmount) + Number(sellAmt)
          ).toFixed(2);
        }
      });
      this.bulkSalesForm.totalSalesAmount = totalSalesAmount;
    },
    //获取转交人列表
    getListExcludeMe() {
      getListExcludeMeRecharge({}).then((res) => {
        this.partneList = res.rows;
      });
    },
    //批量出售点击事件
    handleBulkSales() {
      if (this.bulkSalesSelectsMap.length > 0) {

        this.bulkSalesForm = {
          sellPrice:'',
          custName:'',
          sellChatgroupName:'',
          objectsForSale:'0',
          accountZone:''
        };
        this.bulkSalesVisible = true;
      } else {
        this.$modal.msgError(`请选择待出售的账号进行出售！`);
      }
    },
    //批量出售提交
    bulkSalesVisibleSubmit() {
      this.$refs["bulkSalesForm"].validate(async (valid) => {
        if (valid) {
          if (
            !this.bulkSalesForm.sellPrice ||
            this.bulkSalesForm.sellPrice <= 0
          ) {
            this.$modal.msgError(`出售单价必须大于零`);
            return;
          }
          try {
            let requestList = [];
            this.bulkSalesSelectsMap.forEach((itme) => {
              let request = {
                accountZone: this.bulkSalesForm.accountZone, //区域
                custName: this.bulkSalesForm.custName, //售卖人
                rechargeId: itme.rechargeId, //账号唯id
                sellAmt: itme.sellAmt, //出售金额
                sellChatgroupName: this.bulkSalesForm.sellChatgroupName, //出售群
                sellPrice: this.bulkSalesForm.sellPrice, //出售单价
              };
              requestList.push(request);
            });
            let sellcardBatchAccountSell_res = await sellcardBatchAccountSell(
              requestList
            );
            if (sellcardBatchAccountSell_res.code === 200) {
              this.getTodayAmountSold();
              this.getList();
              this.$modal.msgSuccess("出售账号成功！");
              this.bulkSalesVisible = false;
              this.bulkSalesSelects = [];
              this.bulkSalesSelectsMap = [];
              this.bulkSalesForm = {};
            }
          } catch (err) {
          }
        }
      });
    },
    bulkSalesCancel() {
      this.bulkSalesVisible = false;
      // this.bulkSalesSelects = [];
      // this.bulkSalesSelectsMap = [];
      this.bulkSalesForm = {};
    },
    // 账号领取弹窗点击
    handleClaimAccount(itme) {
      this.accountForm = {
        count: itme.count,
        shouldAmtList: itme.cardBalance,
        chargeStage: itme.chargeStage,
      };
      this.accountOpen = true;
    },
    // 领取账号提交
    submitAccountForm() {

      this.$refs["accountForm"].validate((valid) => {
        if (valid) {
          const request = {
            count: this.accountForm.count,
            cardBalanceList: [this.accountForm.shouldAmtList],
            chargeStage: this.accountForm.chargeStage
          };
          receiveAccount(request).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.getTodayAmountSold();
              this.accountOpen = false;
              this.$modal.msgSuccess("领取账号成功！");
            }
          });
        }
      });
    },
    // 转交账号弹窗
    handleTransferAccount() {
      this.transferForm = {
        userName: null,
      };
      this.transmitOpen = true;
    },
    // 账号转交表单提交
    submitTransferForm() {
      this.$refs["transferForm"].validate((valid) => {
        if (valid) {
          let findItme = this.partneList.find((itme) => {
            return itme.userId === this.transferForm.userId;
          });
          this.transferForm.userName = findItme.userName;
          sellcardTransferAccount(this.transferForm).then((res) => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("转交成功！");
              this.transmitOpen = false;
            }
          });
        }
      });
    },
    ccountCancel() {
      this.accountOpen = false;
    },
    //转交取消
    transmitCancel() {
      this.transmitOpen = false;
    },
    /**转译售卡状态 */
    formatStatus(status) {
      let data = "";
      switch (status) {
        case "1":
          data = "未生效";
          break;
        case "2":
          data = "待出售";
          break;
        case "3":
          data = "已出售";
          break;
        case "4":
          data = "作废";
          break;
      }
      return data;
    },
    /** 查询sellcard列表 */
    async getList() {
      try {
        this.loading = true;
        const request = {
          ...this.queryParams,
          startReceiveTime: this.queryParams.receiveTime
            ? this.queryParams.receiveTime[0]
            : null, //开始领取时间
          endReceiveTime: this.queryParams.receiveTime
            ? this.queryParams.receiveTime[1]
            : null, //结束领取时间
          startSellTime: this.queryParams.sellTime
            ? this.queryParams.sellTime[0]
            : null, //开始出售时间
          endSellTime: this.queryParams.sellTime
            ? this.queryParams.sellTime[1]
            : null, //结束出售时间
        };
        const listSellcard_res = await listSellcard(request);
        if (listSellcard_res.code === 200) {
          this.sellcardList = listSellcard_res.rows;
          this.total = listSellcard_res.total;
          this.loading = false;
          this.bulkSalesSelectsMap = [];
          this.copySelects = [];
          this.bulkSalesSelects = [];
          console.log("清空批量复制的数组:copySelects:", this.copySelects);
        }
        const res = await getSellVoidedBalances({
          start:this.queryParams.receiveTime[0],
          end:this.queryParams.receiveTime[1],
        });
        this.obsolescenceRate = res.data;
      } catch (err) {
        this.loading = false;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        rechargeId: null,
        acid: null,
        accountName: null,
        accountPwd: null,
        accountZone: null,
        status: "0",
        cardBalance: null,
        buyPrice: null,
        buyAmt: null,
        sellPrice: null,
        sellAmt: null,
        sellChatgroupName: null,
        custName: null,
        sellTime: null,
        remark: null,
        createTime: null,
        createBy: null,
        doneTime: null,
        doneUser: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.startShouldAmt = null;
      this.queryParams.endShouldAmt = null;
      this.queryParams = {};
      let zone = this.$route.params.accountZone;
      if (zone != undefined || zone != "") {
        this.$set(this.queryParams, "accountZone", zone);
        this.$set(this.queryParams, "idType", "0");
      }
      this.bulkSalesSelectsMap = [];
      this.copySelects = [];
      this.bulkSalesSelects = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      if (scope.pinnedStatus == 1) {
      } else {
        this.ids = selection.map((item) => item.rechargeId);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加sellcard";
    },
    /** 单个出售按钮点击 */
    handleUpdate(row) {
      this.individualSalesForm = row;
      this.individualSalesForm.updateUser = this.$store.state.user.name;
      this.individualSalesForm.accountZone = this.queryParams.accountZone;
      this.individualSalesForm.objectsForSale  = '0'
      this.individualSalesVisible = true;
    },
    /** 单个出售提交按钮 */
    submitForm() {
      this.$refs["individualSalesForm"].validate((valid) => {
        if (valid) {
          if (this.individualSalesForm.sellPrice <= 0) {
            this.$modal.msgError(`出售单价必须大于零`);
            return;
          }
          const request = {
            accountZone: this.individualSalesForm.accountZone, //区域
            custName: this.individualSalesForm.custName, //收买人-客户
            rechargeId: this.individualSalesForm.rechargeId, //账号唯一id
            sellAmt: this.individualSalesForm.sellAmt, //出售金额
            sellChatgroupName: this.individualSalesForm.sellChatgroupName, //出售群
            sellPrice: this.individualSalesForm.sellPrice, //出售单价
          };
          sellcardSell(request).then((response) => {
            if (response.code === 200) {
              this.$modal.msgSuccess("单个出售成功");
              this.getTodayAmountSold();
              this.individualSalesVisible = false;
              this.getList();
            }
          });
        }
      });
    },
    //单个出售取消
    individualSaleCancel() {
      this.individualSalesVisible = false;
      this.individualSalesForm = {};
    },
    /** 查看详情按钮操作 */
    async sellDetail(row) {
      this.$router.push('/sellCardDetail/sellCardDetail/'+row.rechargeId+'?type=detail')
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/new/system/sellcard/export",
        {
          ...this.queryParams,
        },
        `sellcard_${new Date().getTime()}.xlsx`
      );
    },
    selectFn(selection, row) {
      //todo
      let flag = selection.some((itme) => {
        return itme.rechargeId === row.rechargeId;
      });
      const index = this.arrFindObjIndex(this.copySelects, row, "rechargeId");
      if (!flag) {
        if (index !== -1) {
          this.copySelects.splice(index, 1);
          this.bulkSalesSelects.splice(index, 1);
        }
      } else {
        if (index === -1) {
          this.copySelects.push(row);
        }
        if (index === -1 && row.status === "2") {
          this.bulkSalesSelects.push(row);
        }
      }
      this.bulkSalesSelectsMap = JSON.parse(
        JSON.stringify(this.bulkSalesSelects)
      );
    },
    selectAllFn(selection) {
      if (!selection.length) {
        this.sellcardList.forEach((itme) => {
          const index = this.arrFindObjIndex(
            this.copySelects,
            itme,
            "rechargeId"
          );
          if (index !== -1) {
            this.copySelects.splice(index, 1);
            this.bulkSalesSelects.splice(index, 1);
          }
        });
      } else {
        selection.forEach((itme) => {
          const index = this.arrFindObjIndex(
            this.copySelects,
            itme,
            "rechargeId"
          );
          if (index === -1) {
            this.copySelects.push(itme);
          }
          if (index === -1 && itme.status === "2") {
            this.bulkSalesSelects.push(itme);
          }
        });
      }
      this.bulkSalesSelectsMap = JSON.parse(
        JSON.stringify(this.bulkSalesSelects)
      );
    },
    arrFindObjIndex(list, obj, key) {
      let index = -1;
      list.forEach((itme, idx) => {
        if (itme[key] === obj[key]) {
          index = idx;
        }
      });
      return index;
    },
    // 一键复制账号
    copyToClip(content) {
      const input = document.createElement("input");
      input.setAttribute("value", content);
      document.body.appendChild(input);
      input.select();
      document.execCommand("copy");
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    /**批量出售筛选定位按钮搜索*/
    bulkSalesQuery() {
      const idx = this.bulkSalesSelectsMap.findIndex(
        (item) => item.accountName === this.bulkSalesQueryForm.accountName
      );
      if (idx >= 0) {
        this.getTableScrollTop(idx + 1);
      } else {
        this.$modal.msgError(`未查到到账号！`);
      }
    },
    /**批量复制账号和密码*/
    batchCopyToClip() {
      if (this.copySelects.length <= 0) {
        this.$modal.msgError(`请选择账号进行复制！`);
        return;
      }
      this.$prompt('请输入谷歌验证码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
      }).then(({ value }) => {
        verifyGoogleCaptcha(value).then(response => {
          const name = this.copySelects
              .map((item) => {
                return `${item.accountName}----${item.accountPwd}\r\n`;
              })
              .join("");
          const input = document.createElement("textarea");
          input.value = name;
          document.body.appendChild(input);
          input.select();
          document.execCommand("copy");
          document.body.removeChild(input);
          this.$modal.msgSuccess("已复制到黏贴版");
        });
      }).catch(() => {
      });

    },
    /**详情取消*/
    detailCancel() {
      this.sellcardReportDetailsVisible = false;
      // this.sellDetailVisible = false;
    },
    /**作废按钮事件*/
    handleDelete(row) {
      this.nullifyVisible = true;
      this.nullifyForm = {
        ...row
      };
    },
    /**作废提交按钮事件*/
    submitNullifyForm() {
      this.$refs["nullifyForm"].validate(async (valid) => {
        if (valid) {
          const request = {
            ...this.nullifyForm,
          };
          const newCance_res = await cancelSellcard(request);
          if (newCance_res.code === 200) {
            const index = this.bulkSalesSelectsMap.findIndex(itme => itme.rechargeId == this.nullifyForm.rechargeId);
            if (this.bulkSalesVisible && index !== -1) {//如果批量出售里作废需要刷新批量出售的列表
              this.bulkSalesSelectsMap.splice(index, 1);
            }else {
              this.getList()
            }


            this.nullifyVisible = false;
            this.$modal.msgSuccess("作废成功");
          }
        }
      });
    },
    formatRechargeStatus(status) {
      let statusName = "";
      if (status) {
        let rechargeItme = this.dict.type.recharge_status.find((itme) => {
          return itme.value === status;
        });
        if (rechargeItme) {
          statusName = rechargeItme.label;
        }
      }
      return statusName;
    },
    getLimit(limitStr) {
      if (limitStr) return limitStr.split(",").length;
    },
    nullifyCancel() {
      this.nullifyVisible = false;
    },
    getSummaries(param) {
      const notTotals = [1, 2, 3, 4,5, 9]; //不需要小计的列数组
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "小计";
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = "";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += " ";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    getSubSummaries(param) {
      const notTotals = [1, 2, 5, 6]; //不需要小计的列数组
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "小计";
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = "";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += " ";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    getBulkSummaries(param) {
      const notTotals = [1, 2, 4, 6]; //不需要小计的列数组
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "小计";
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = "";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += " ";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /*搜素定位表格*/
    getTableScrollTop(index) {
      const elTable = this.$refs["rw_table"].$el;
      const scrollParent = elTable.querySelector(".el-table__body-wrapper");
      const targetHeight =
        elTable.querySelectorAll(".el-table__body tr")[index - 1].clientHeight; // 该行的高度
      let moveHeight;
      /*超过显示范围，向下移动*/
      let row = 0;
      if (index > 2) {
        row = targetHeight;
      }
      const num = Math.floor((260 - row) / targetHeight) - 1;
      if (index >= num) {
        moveHeight = (index + 1 - num) * targetHeight;
      }

      this.$refs.rw_table.setCurrentRow(this.bulkSalesSelectsMap[index - 1]);
      // if (index === this.bulkSalesSelectsMap.length - 1) {
      //   moveHeight = 0
      // }
      debugger;
      scrollParent.scrollTop = moveHeight;
    },
    /** 置顶 */
    async pinned(row) {
      const h = this.$createElement;
      const isPinned = row.pinnedStatus == 1; // 判断当前项的置顶状态是否为2
      try {
        const { value } = await this.$prompt("", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputPattern: /^[\s\S]*$/, // 设置输入格式的正则表达式，允许任意内容，包括空字符串
          inputErrorMessage: "", // 不设置输入错误信息，即允许空输入
          customClass: "custom-prompt", // 自定义样式类名
          message: h(
            "div",
            { style: "text-align: center" },
            isPinned ? "确定要取消置顶此项吗？" : "确定要置顶此项吗？"
          ),
          inputPlaceholder: "请输入备注", // 输入框提示
        });

        const newPinned = isPinned ? 1 : 2; // 如果当前项的置顶状态为2，则将置顶状态改为1；否则置顶状态改为2。
        updatePinned({ ...row, pinnedStatus: newPinned, remark: value })
          .then(() => {
            // 更新置顶状态并传入输入的参数
            this.getList();
            this.$message.success("操作成功");
          })
          .catch((error) => {
            this.$message.error("操作失败：" + error.message);
          });
      } catch (error) {
      }
    },
    tableRowClassName({ row }) {
      if (row.pinnedStatus == 1) {
        return "success-row";
      }
      if (row.status === '2'){
        if (row.chargeStage === '2'){
          return "level2-row";
        }
      }

      return "";
    },
    selectableFn(row) {
      if (row.pinnedStatus == 1) {
        return false;
      } else {
        return true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog-heard {
  text-align: right;
  margin-right: 30px;
}

.el-row {
  margin-bottom: 25px;
  color: #606266;
  font-size: 14px;
  font-weight: 700;
}
</style>
<style lang="scss">
.el-table .success-row {
  background: #facd91;
}
.el-table .level2-row {
  background: #c8ecfc;
}
</style>
