<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门：" prop="deptId">
        <el-select
          v-model="queryParams.deptId"
          placeholder="请选择部门"
          clearable
          style="width: 240px"
          @change="selectDept"
        >
          <el-option
            v-for="dict in deptList"
            :key="dict.deptId"
            :label="dict.deptName"
            :value="dict.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人：" prop="createUser">
        <el-select
          v-model="queryParams.createUser"
          placeholder="请选择操作人"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in operNameList"
            :key="dict.userId"
            :label="dict.nickName"
            :value="dict.nickName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="createType">
        <el-select
          v-model="queryParams.createType"
          placeholder="操作类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in theTypeOfOperation"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" v-loading="loading" :data="list" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作时间" align="center" prop="createTime" />
      <el-table-column label="部门" align="center" prop="deptName" />
      <el-table-column label="操作人" align="center" prop="createUser" />
      <el-table-column label="操作" align="center" prop="createType">
        <template slot-scope="{row}">
          {{getTypeOfOperation(row)}}
        </template>
      </el-table-column>
      <el-table-column label="操作内容" align="center" prop="createReason" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import { getLogList, getUserList } from '@/api/newSystem/accRecharge'
import { listDept, LogListDept } from '@/api/newSystem/dept'

export default {
  name: "Operlog",
  dicts: ['sys_oper_type', 'sys_common_status'],
  data() {
    return {
      operNameList:[],
      deptList:[],
      theTypeOfOperation:[
        {
          value:'1',
          label:'部分充值'
        },
        {
          value:'2',
          label:'完成充值'
        },
        {
          value:'3',
          label:'退回'
        },
        {
          value:'4',
          label:'出售'
        },
        {
          value:'5',
          label:'修改'
        },
        {
          value:'6',
          label:'恢复'
        },
        {
          value:'7',
          label:'作废'
        },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: {prop: 'operTime', order: 'descending'},
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        title: undefined,
        operName: undefined,
        businessType: undefined,
        status: undefined
      }
    };
  },
  mounted() {
    this.getList();
    LogListDept({
      parentId:100
    }).then(res=>{
      this.deptList = res.data
      console.log(res.data)
    })
  },
  methods: {
    selectDept(e){
      getUserList({
        deptId:e,
        pageNum:1,
        pageSize:10000,
      }).then(res=>{
        this.operNameList = res.rows
        console.log(res)
      })
    },
    getTypeOfOperation(e){
      let data = null
      switch (e.createType) {
        case '1':
          data = '部分充值'
          break;
        case '2':
          data = '完成充值'
          break;
        case '3':
          data = '退回'
          break;
        case '4':
          data = '出售'
          break;
        case '5':
          data = '修改'
          break;
        case '6':
          data = '恢复'
          break;
        case '7':
          data = '作废'
          break;
      }
      return data
    },
    /** 查询登录日志 */
    getList() {
      this.loading = true;
      this.queryParams.acid = this.$route.params.acid ?  this.$route.params.acid: this.$route.params.id
      getLogList(this.queryParams).then( response => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 操作日志类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.dict.type.sys_oper_type, row.businessType);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.operId)
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
  }
};
</script>

