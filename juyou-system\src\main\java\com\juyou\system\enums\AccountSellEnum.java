package com.juyou.system.enums;

import lombok.Getter;

/**
 * 账号出售
 * 售卡状态：0 未生效 1 已生效 2 待出售 3 已出售 4 作废
 */
@Getter
public enum AccountSellEnum {
    STATUS_1("1","未生效"),
    STATUS_2("2","待出售"),
    STATUS_3("3","已出售"),
    STATUS_4("4", "作废")
    ;

    AccountSellEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 状态
     */
    private String status;

    /**
     * 描述
     */
    private String desc;


}
