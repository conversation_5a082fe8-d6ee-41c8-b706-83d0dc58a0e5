-- 新建用户谷歌验证器关联表
CREATE TABLE `user_google_authenticator`
(
    `id`             bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id`        bigint                                  NOT NULL COMMENT '用户id',
    `user_name`      varchar(30) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户账号',
    `google_secret`  varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '谷歌验证秘钥',
    `secret_qr_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '谷歌密钥二维码(给Authenticator app扫描得到code用于校验)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户谷歌验证器关联表';

-- 谷歌验证器权限菜单
INSERT INTO `juyou_tool`.`sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('生成谷歌秘钥,二维码', 100, 10, '', NULL, NULL, 1, 0, 'F', '0', '0', 'support:googleAuthenticator:generateGoogleSecret', '#', 'admin', '2023-10-07 11:34:16', '', NULL, '');

-- 账号出售待生效分钟数
INSERT INTO `juyou_tool`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '账号出售待生效分钟数', 'account.sell.pending.minutes', '12960', 'N', 'manager', '2023-10-07 15:39:05', 'manager', '2023-10-07 15:39:09', '账号出售待生效分钟数');

-- 字典
INSERT INTO `juyou_tool`.`sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, '账号出售-未生效账号-生效时间条件', 'two_account_sell_not_activated_effective_time', '0', 'admin', '2023-10-07 16:44:23', '', NULL, '账号出售-未生效账号-生效时间条件');

INSERT INTO `juyou_tool`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (114, 0, '5天以内', '0-6000', 'two_account_sell_not_activated_effective_time', NULL, 'success', 'N', '0', 'admin', '2023-10-07 16:45:55', 'admin', '2023-10-07 16:46:53', NULL);
INSERT INTO `juyou_tool`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (115, 0, '5-7天', '6001-10080', 'two_account_sell_not_activated_effective_time', NULL, 'primary', 'N', '0', 'admin', '2023-10-07 16:46:42', '', NULL, NULL);
INSERT INTO `juyou_tool`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (116, 0, '7天以上', '10081-********', 'two_account_sell_not_activated_effective_time', NULL, 'info', 'N', '0', 'admin', '2023-10-07 16:47:30', '', NULL, NULL);
