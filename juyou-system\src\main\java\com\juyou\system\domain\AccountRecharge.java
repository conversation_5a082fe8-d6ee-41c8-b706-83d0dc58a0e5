package com.juyou.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 账号充值对象 two_account_recharge
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel(value = "AccountRecharge")
@TableName("two_account_recharge")
public class AccountRecharge extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("全局唯一")
    private Integer acid;


    @ApiModelProperty("账号名称，全局唯一")
    @Excel(name = "账号名称，全局唯一")
    private String accountName;

    @ApiModelProperty("密码")
    @Excel(name = "密码")
    private String accountPwd;

    @ApiModelProperty("账号区域")
    @Excel(name = "账号区域")
    private String accountZone;

    @ApiModelProperty("钱包区域")
    @Excel(name = "钱包区域")
    @Deprecated
    private String walletArea;

    @ApiModelProperty("idType")
    @Excel(name = "idType")
    @Deprecated
    private String idType;

    @ApiModelProperty("备用码")
    @Excel(name = "备用码")
    @Deprecated
    private String spareCode;

    @ApiModelProperty("卡状态: 0 未领取 1 待充值  2 部分充值 3 完成充值 4 作废")
    @Excel(name = "卡状态")
    private String status;

    @ApiModelProperty("核销状态：1 未核对 2 已核对")
    @Excel(name = "核销状态：1 未核对 2 已核对")
    private String writeOffStatus;

    @ApiModelProperty("收卡单价")
    @Excel(name = "收卡单价")
    @Deprecated
    private Double buyPrice;

    @ApiModelProperty("成本金额(CNY)")
    @Excel(name = "成本金额(CNY`)")
    private Double buyAmt;

    @ApiModelProperty("应充值金额-废弃")
    @Excel(name = "应充值金额-废弃")
    @Deprecated
    private Double shouldAmt;

    @ApiModelProperty("ID余额:(充值金额)")
    @Excel(name = "ID余额:(充值金额)")
    private Double rechargeAmt;

    @ApiModelProperty("作废原因类型: 1 锁定 2 双禁 3 双重验证")
    @Excel(name = "作废原因类型: 1 锁定 2 双禁 3 双重验证")
    private String cancelReasonType;

    @ApiModelProperty("双禁余额")
    @Excel(name = "双禁余额")
    private Double doubleProhibitedBalance;

    @ApiModelProperty("作废图片")
    private String cancelImgs;

    @ApiModelProperty("作废时间")
    @Excel(name = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelDate;

    @ApiModelProperty("操作人")
    @Excel(name = "操作人")
    private String createUser;

    @ApiModelProperty("更新人")
    @Excel(name = "更新人")
    private String updateUser;

    @ApiModelProperty("完成时间")
    @Excel(name = "完成时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date doneTime;

    @ApiModelProperty("完成人")
    @Excel(name = "完成人")
    private String doneUser;

    @ApiModelProperty("领取时间")
    @Excel(name = "领取时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    @ApiModelProperty("领取人")
    @Excel(name = "领取人")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String receiveUser;

    @ApiModelProperty("领取人id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long receiveUserId;

    @ApiModelProperty("部门id")
    private Long deptId;

    @TableField(exist = false)
    private String createBy;

    @TableField(exist = false)
    private String updateBy;

    @ApiModelProperty("充值阶段")
    private String chargeStage;

    @ApiModelProperty("一级充值人")
    private Long primaryCharger;

    @ApiModelProperty("二级充值人")
    private Long secondaryCharger;

    /*@ApiModelProperty("当前处理人")
    private Long currentHandler;*/

    @ApiModelProperty("退回原因:出售退回")
    private String  backReason;

    @ApiModelProperty("归属业务员名称")
    private String  idUser;

    @ApiModelProperty("是否做过充值修改和出售修改的: Y N")
    private String hasEdit;

    @ApiModelProperty("是否退回添加退回标识")
    private String hasBack;

    @ApiModelProperty("备注")
    private String comment;

    @ApiModelProperty("等待起始时间:正常情况和完成时间一致")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pendingStartDate;

}
