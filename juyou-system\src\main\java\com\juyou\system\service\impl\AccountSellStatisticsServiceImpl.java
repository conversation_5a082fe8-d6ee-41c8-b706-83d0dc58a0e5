package com.juyou.system.service.impl;

import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.AccountStatisticsCancelVoList;
import com.juyou.system.domain.vo.GiftCardRechargeStatisticsVo;
import com.juyou.system.mapper.AccountSellMapper;
import com.juyou.system.mapper.AccountSellStatisticsMapper;
import com.juyou.system.params.*;
import com.juyou.system.service.IAccountSellStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 账号销售统计ServiceImpl
 */
@Service
public class AccountSellStatisticsServiceImpl implements IAccountSellStatisticsService {

    @Autowired
    private AccountSellStatisticsMapper accountSellStatisticsMapper;
    @Autowired
    private AccountSellMapper accountSellMapper;

    @Override
    public List<GiftCardRechargeStatisticsVo> getGiftCardRechargeStatisticsList(GiftCardRechargeStatisticsSearchParam param) {
        return this.accountSellStatisticsMapper.getGiftCardRechargeStatisticsList(param);
    }

    @Override
    public List<AccountSell> getSellCancelAmtDetailList(SellCancelAmtDetailParam param) {
        return this.accountSellStatisticsMapper.getSellCancelAmtDetailList(param);
    }

    @Override
    public List<AccountRecharge> getRechargeCancelAmtDetailList(RechargeCancelAmtDetailSearchParam param) {
        return this.accountSellStatisticsMapper.getRechargeCancelAmtDetailList(param);
    }

    @Override
    public List<AccountSell> getSellGroupDetailList(SellGroupDetailSearchParam param) {
        return this.accountSellMapper.getSellGroupDetailList(param);
    }

    @Override
    public List<AccountStatisticsCancelVoList> getCancelList(AccountStatisticsCancelParam param) {
        return this.accountSellStatisticsMapper.getCancelList(param);
    }
}
