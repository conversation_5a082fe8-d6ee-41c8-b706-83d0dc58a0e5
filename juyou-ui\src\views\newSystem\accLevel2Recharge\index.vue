<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="账号：" prop="accountName">
        <el-input v-model="queryParams.accountName" placeholder="请输入账号" clearable
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="充值状态：" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择充值状态" :style="{ width: '80%' }" clearable>
          <el-option
            v-for="dict in [{ label: '全部', value: '' }, { label: '待充值', value: '1' }, { label: '部分充值', value: '2' }, { label: '作废', value: '4' }]"
            :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="礼品卡代码：" prop="giftCard">
        <el-input v-model="queryParams.giftCard" placeholder="请输入礼品卡代码"/>
      </el-form-item>

      <!-- <el-form-item label="充值状态：" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择充值状态" :style="{ width: '80%' }" clearable>
          <el-option v-for="dict in dict.type.recharge_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="区域：" prop="accountZone">
        <el-input v-model="queryParams.accountZone" placeholder="请输入账号区域" :style="{ width: '80%' }" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->

      <el-form-item label="领取日期：" prop="receiveTime">
        <el-date-picker v-model="queryParams.receiveTime" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="完成日期：" prop="doneTime">
        <el-date-picker v-model="queryParams.doneTime" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
        <el-button type="primary" size="mini" @click="today">今天</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="7">
        <div style="font-size: 18px;margin: 5px;">我今日已充值金额：
          <span style="font-size: 24px;">${{ todayRechargeAmount }}</span>
        </div>
      </el-col>

      <el-col :span="7">
        <div style="font-size: 18px;margin: 5px;">我的充值作废率：
          <span style="font-size: 24px">{{rechargeCancelRate }}%</span>
        </div>
      </el-col>


    </el-row>
    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <div style="font-size: 18px; padding: 8px">
          <span>一级库剩余账号：</span>
        </div>
      </el-col>

      <el-col :span="2" v-for="(itme, index) in level1Library" :key="index">
        <div style="font-size: 18px">
          <el-button
            style="font-size: 18px"
            type="text"
            @click="handleClaimAccount(itme)"
          >${{ itme.cardBalance }}：{{ itme.count }}个
          </el-button>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <div style="font-size: 18px; padding: 8px">
          <span>二级库剩余账号：</span>
        </div>
      </el-col>

      <el-col :span="2" v-for="(itme, index) in level2Library" :key="index">
        <div style="font-size: 18px">
          <el-button
            style="font-size: 18px"
            type="text"
            @click="handleClaimAccount(itme)"
          >${{ itme.cardBalance }}：{{ itme.count }}个
          </el-button>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <div style="font-size: 18px; padding: 8px">
          <span>我的充值账号：</span>
        </div>
      </el-col>

      <el-col :span="2" v-for="(itme, index) in myList" :key="index">
        <div style="font-size: 18px">
          <el-button
            style="font-size: 18px"
            type="text"
            @click="handleClaimAccount(itme)"
          >${{ itme.cardBalance }}：{{ itme.count }}个
          </el-button>
        </div>
      </el-col>
    </el-row>
    <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    <el-table :row-class-name="tableRowClassName" v-loading="loading" :data="accRechargeList" :summary-method="getSummaries"
              show-summary>
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">
            {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
          scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
        scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <el-table-column label="充值状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.recharge_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="充值阶段" align="center" prop="">
        <template slot-scope="scope">
          {{scope.row.chargeStage == '2' ?'二级':'一级'}}
        </template>
      </el-table-column>
      <el-table-column label="ID余额" align="center" prop="rechargeAmt"/>
      <el-table-column label="成本金额(CNY)" align="center" prop="buyAmt"/>
      <el-table-column label="领取人" align="center" prop="receiveUser"/>
      <el-table-column label="领取时间" align="center" width="200" prop="receiveTime"/>
      <el-table-column label="回退原因" align="center" prop="backReason"/>
      <el-table-column label="备注" align="center" prop="comment">
        <template slot-scope="scope">
          <div v-if="!scope.row.remarkFlag">{{scope.row.comment}}<i style="margin-left: 4px" class="el-icon-edit" @click="showRemarkEdit(scope)"></i></div>
          <el-input autofocus @keyup.enter.native="inputRemarkFlag(scope.row)" v-else size="mini" v-model="scope.row.comment" placeholder="请输入备注"></el-input>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="145" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-if="scope.row.status == 1 || scope.row.status == 2" v-hasPermi="['system:twoLevelAccountRecharge:recharge']">充值
          </el-button>

          <el-button size="mini" type="text" @click="handleComplete(scope.row)" v-if="scope.row.status == 2"
                     v-hasPermi="['system:twoLevelAccountRecharge:completeRecharge']">完成充值
          </el-button>

          <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 2" type="text" icon="el-icon-delete"
                     @click="handleDelete(scope.row)" v-hasPermi="['system:twoLevelAccountRecharge:cancel']">作废
          </el-button>

          <el-button size="mini" type="text" @click="handleDetail(scope.row)"
                     v-if="scope.row.status == 3 || scope.row.status == 4" v-hasPermi="['system:twoLevelAccountRecharge:detail']">查看
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改账号充值对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" :close-on-press-escape="false"
               width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="right" :inline="true">

        <el-form-item label="账号：" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入账号名称"
                    :disabled="form.status === '1' || form.status === '2'"/>
        </el-form-item>

        <el-form-item label="密码：" prop="accountPwd">
          <el-input v-model="form.accountPwd" placeholder="请输入密码"
                    :disabled="form.status === '1' || form.status === '2'"/>
        </el-form-item>

        <el-form-item label="来源群：" prop="sourceGroup">
          <el-input v-model="form.sourceGroup" placeholder="请输入来源群"/>
        </el-form-item>

        <el-form-item label="操作人：" prop="updateUser">
          <el-input v-model="form.updateUser" :disabled="true"/>
        </el-form-item>
        <!-- <div style="display: flex;">
          <el-form-item style="flex: 1;" label="礼品卡代码：" prop="giftCard">
            <el-input style="width: 305px;" v-model="form.giftCard" />
          </el-form-item>
        </div> -->

        <el-form-item label="礼品卡代码：" prop="giftCard">
          <el-input v-model="form.giftCard"/>
        </el-form-item>
        <el-form-item label="质押30分钟：" prop="pledgeThirtyMinutes">
          <el-checkbox v-model="form.pledgeThirtyMinutes">质押30分钟</el-checkbox>
        </el-form-item>


        <el-form-item :class="form.status === '2' ? 'item' : ''" :label="form.status === '2' ? '本次进价：' : '进价：'"
                      prop="buyPrice">
          <!-- oninput="if(value<0)value=0" -->
          <!-- <el-input oninput="value=value.replace(/^0|[^0-9]/g,'')" type="number" v-model="form.buyPrice" @input="checkCartAmt" /> -->
          <el-input v-model="form.buyPrice" @input="checkCartAmt"/>
        </el-form-item>
        <!-- <el-form-item label="区域" prop="accountZone">
          <el-input v-model="form.accountZone" placeholder="请输入账号区域" />
        </el-form-item> -->


        <!-- 如果是部分充值需要增加本次充值金额和本次充值进价 -->
        <el-form-item class="item" v-if="form.status === '2'" label="本次充值金额：" prop="timeAmt">
          <el-input v-model="form.timeAmt" @input="checkCartAmt"/>
        </el-form-item>
        <!-- 如果是部分充值需要增加本次充值金额和本次充值进价 -->

        <el-form-item label="充值金额：" prop="rechargeAmt">
          <el-input v-model="form.rechargeAmt" @input="checkCartAmt" :disabled="form.status === '2'"/>
        </el-form-item>

        <el-form-item label="成本：" prop="buyAmt">
          <el-input v-model="form.buyAmt" :disabled="true"/>
        </el-form-item>

        <el-form-item label="区域：" prop="accountZone">
          <el-select v-model="form.accountZone" filterable placeholder="请选择">
            <el-option value="" label="全部"></el-option>
            <el-option
              v-for="item in zonrList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel">
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div v-if="form.status === '2'" style="color: #606266;font-weight: 700; margin: 10px;">该账号已充值的礼品卡:</div>
      <el-table v-if="form.status === '2'" v-loading="loading" :data="rechargeAlertGiftCardRecordList">
        <el-table-column label="礼品卡" align="center" prop="giftCard"/>
        <el-table-column label="来源群" align="center" prop="sourceGroup"/>
        <el-table-column label="面值" align="center" prop="faceValue"></el-table-column>
        <el-table-column label="成本" align="center" prop="buyAmt"></el-table-column>
        <el-table-column label="执行时间" align="center" prop="executionTime"/>
        <el-table-column fixed="right" label="操作" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDeleteGiftCardRechargeRecordIds(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template>
        <div slot="footer" class="dialog-footer">
          <el-button style="margin-right: 210px;" type="primary" @click="apopover('1')">完成充值</el-button>
          <el-button type="warning" @click="apopover('2')">部分充值</el-button>
          <el-button v-if="disable" type="primary"
                     @click="copyToClip(`${form.accountName}----${form.accountPwd}`)">一键复制账号
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>

    </el-dialog>
    <!-- 领取账号对话 -->
    <el-dialog title="账号领取" :visible.sync="accountOpen" :close-on-click-modal="false" :close-on-press-escape="false"
               width="600px" append-to-body>
      <el-form ref="accountForm" :model="accountForm" :rules="accountRules" label-width="90px" label-position="right"
               :inline="true">
        <el-form-item label="领取个数" prop="count">
          <!-- <el-input type="number" v-model="accountForm.count" placeholder="请输入领取个数" :disabled="false" /> -->
          <el-select v-model="accountForm.count" placeholder="请选择领取个数" :style="{ width: '80%' }" clearable>
            <el-option v-for="num in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]" :key="num"
                       :label="num" :value="num"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAccountForm">领取</el-button>
        <el-button @click="ccountCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 转交账号对话 -->
    <el-dialog title="转交账号" :visible.sync="transmitOpen" :close-on-click-modal="false"
               :close-on-press-escape="false"
               width="600px" append-to-body>
      <div>请将你已领取的账号转交给你的小伙伴</div>
      <el-form ref="transferForm" :model="transferForm" :rules="transferRules" label-width="90px" label-position="right"
               :inline="true">
        <el-form-item label="伙伴姓名" prop="userId">
          <el-select v-model="transferForm.userId" placeholder="请选择伙伴姓名" :style="{ width: '80%' }" clearable>
            <el-option v-for="itme in partneList" :key="itme.userName" :label="itme.userName" :value="itme.userId"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTransferForm">确定</el-button>
        <el-button @click="transmitCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 充值明细详情 -->
    <el-dialog title="账号详情" :visible.sync="rechargedetailVisible" :close-on-click-modal="false"
               :close-on-press-escape="false" width="850px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">账号： {{ rechargedetailForm.accountName }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">密码： {{ rechargedetailForm.accountPwd }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">区域： {{ rechargedetailForm.accountZone }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">充值状态： {{ formatRechargeStatus(rechargedetailForm.status) }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">实际充值金额： {{ rechargedetailForm.rechargeAmt }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">进价： {{ rechargedetailForm.buyPrice }}</div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">成本： {{ rechargedetailForm.buyAmt }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">创建人： {{ rechargedetailForm.createUser }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">创建时间： {{ rechargedetailForm.createTime }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">领取人： {{ rechargedetailForm.receiveUser }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">领取时间： {{ rechargedetailForm.receiveTime }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">完成人： {{ rechargedetailForm.doneUser }}</div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">完成时间： {{ rechargedetailForm.doneTime }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">作废原因： {{
              formatCancelReasonType(rechargedetailForm.cancelReasonType)
            }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">双禁金额： {{ rechargedetailForm.doubleProhibitedBalance }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="4">
        <div>作废图片:</div>
        <image-upload v-if="rechargedetailForm.cancelImgs" v-model="rechargedetailForm.cancelImgs"
                      :limit="getLimit(rechargedetailForm.cancelImgs)" :uploadImgUrl="uploadImgUrl" :isShowTip="false"/>
        <!-- <image-preview v-if="rechargedetailForm.cancelImgs" :src="rechargedetailForm.cancelImgs || ''" :width="80" :height="80" /> -->
      </el-row>


      <el-table v-loading="loading" :data="giftCardRechargeRecordList">
        <el-table-column label="礼品卡" align="center" prop="giftCard"/>
        <el-table-column label="面值" align="center" prop="faceValue"></el-table-column>
        <el-table-column label="余额" align="center" prop="balance"/>
        <el-table-column label="执行时间" align="center" prop="executionTime"/>
        <el-table-column label="操作人" align="center" prop="createBy"/>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary"
                   @click="copyToClip(`${rechargeDetailsForm.accountName}----${rechargeDetailsForm.accountPwd}`)">一键复制账号
        </el-button>
        <el-button @click="detailCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 作废弹窗 -->
    <el-dialog title="作废｜作废后无法再出售，请确认是否作废？" :visible.sync="nullifyVisible"
               :close-on-click-modal="false"
               :close-on-press-escape="false" width="600px" append-to-body>
      <el-form ref="nullifyForm" :model="nullifyForm" :rules="nullifyFormRules" label-width="100px"
               label-position="right">

        <el-form-item label="账号：" prop="accountName">
          <el-input type="text" v-model="nullifyForm.accountName" :disabled="true"/>
        </el-form-item>

        <el-form-item label="作废原因：" prop="cancelReasonType">
          <el-select v-model="nullifyForm.cancelReasonType" placeholder="请选择作废原因" :style="{ width: '100%' }"
                     clearable>
            <el-option
              v-for="itme in [{ label: '锁定', value: '1' }, { label: '双禁', value: '2' }, { label: '双重验证', value: '3' }]"
              :key="itme.value" :label="itme.label" :value="itme.value"/>
          </el-select>
        </el-form-item>

        <el-form-item label="双禁余额：" prop="doubleProhibitedBalance" v-if="nullifyForm.cancelReasonType === '2'">
          <el-input type="number" v-model="nullifyForm.doubleProhibitedBalance" :disabled="false"/>
        </el-form-item>

        <el-form-item label="作废图片:">
          <image-upload v-model="nullifyForm.cancelImgs" :limit="3" :uploadImgUrl="uploadImgUrl" :isShowTip="false"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitNullifyForm">作废</el-button>
        <el-button @click="nullifyCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  accRechargeCompleteRecharge,
  addAccRecharge,
  cancelAccRecharge,
  getAccountRechargeResidualListTwo,
  getAccRecharge,
  getDataPanel,
  getListExcludeMeRecharge,
  level2TopUpIsVoid,
  twoLevelAccountRechargeList,
  twoLevelAccountRechargeReceiveAccount,
  theSecondLevelRechargeIsDirectlyCompleted,
  transferAccountRecharge,
  updateAccRecharge, level1TopUpIsVoid, getTwoDataPanel
} from '@/api/newSystem/accRecharge'
import {getDicts} from "@/api/newSystem/dict/data";
import {getToken} from "@/utils/auth";
import Cookies from "js-cookie";
import {removeById} from "@/api/newSystem/rechargeCard";
import { checkPermi } from '@/utils/permission'
import { editRemark } from '@/api/newSystem/sellcard'

export default {
  name: "AccLevel2Recharge",
  dicts: ['recharge_status'],
  data() {
    return {
      level2Library:[],
      level1Library:[],
      myList:[],
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/commonFile/fileUpload",//,//dev-api/commonFile/fileUpload
      todayRechargeAmount: 0,
      rechargeCancelRate: 0,
      str: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      disable: false,
      // 总条数
      total: 0,
      // 账号充值表格数据
      accRechargeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 充值详情弹出层
      rechargedetailVisible: false,
      // 充值详情数据
      rechargedetailForm: {},
      //领取账号弹出
      accountOpen: false,
      // $comment时间范围
      daterangeCreateTime: [],
      //领取日期时间范围数组
      receiveTime: [],
      //完成日期时间范围数组
      doneTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        receiveTime: null,
        accountName: null,
        accountPwd: null,
        accountZone: null,
        status: null,
        buyPrice: null,
        buyAmt: null,
        rechargeAmt: null,
        createUser: null,
        updateUser: null,
        doneTime: null,
        doneUser: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sourceGroup: [{required: true, message: '请输入来源群', trigger: 'blur'}],
        giftCard: [{required: true, message: '请输入礼品卡代码', trigger: 'blur'}],
        buyPrice: [{required: true, message: '请输入进价', pattern: /^\d+(\.\d+)?$/, trigger: 'blur'}],
        rechargeAmt: [{required: true, message: '请输入充值金额', pattern: /^\d+(\.\d+)?$/, trigger: 'blur'}],
        timeAmt: [{required: true, message: '请输入本次充值金额', pattern: /^\d+(\.\d+)?$/, trigger: 'blur'}],

      },
      // 领取账号相关数据
      accountForm: {},
      accountRules: {
        count: [{required: true, message: '请选择领取个数', trigger: 'blur'}]
      },
      //转交账号相关数据
      transmitOpen: false,
      partneList: [],
      transferForm: {},
      transferRules: {
        partneName: [{required: true, message: '请输入领取个数', trigger: 'blur'}]
      },
      uploadaccFilePath: process.env.VUE_APP_BASE_API + "/system/accRecharge/importData",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      //作废弹窗相关数据
      nullifyVisible: false,
      nullifyForm: {},
      nullifyFormRules: {
        cancelReasonType: [{required: true, message: '请选择作废原因', trigger: 'blur'}],
        doubleProhibitedBalance: [{required: true, message: '请输入双禁余额', trigger: 'blur'}],
      },
      fileList: [],
      //定时刷新表格列表
      time: null,//定时器名称
      tmpRechargeAmt: null, // 记录部分充值的实际充值金额
      tmpBuyAmt: null, // 记录部分充值的成本金额
      giftCardRechargeRecordList: [],
      // 充值弹框礼品卡List
      rechargeAlertGiftCardRecordList: [],
      // 提交充值，需要删除的礼品卡id集合
      deleteGiftCardRechargeRecordIds: [],
      zonrList: []
    };
  },
  created() {
    let cook = Cookies.get("accRecharge");
    if (cook != null && cook != undefined) {
      this.queryParams = JSON.parse(cook);
    }
    let zone = this.$route.params.accountZone;
    if (zone != undefined || zone == "") {
      this.$set(this.queryParams, "accountZone", zone);
      this.$set(this.queryParams, "idType", '0');
    }
    this.into();
  },
  methods: {
    async inputRemarkFlag(e){
      this.$modal.loading()
      try {
        await editRemark({
          acid:e.acid,
          comment:e.comment
        })
      }catch (e) {
        this.$modal.closeLoading()
        return
      }
      this.$modal.closeLoading()
      e.remarkFlag = false
      this.$modal.msgSuccess('编辑备注成功')
    },
    showRemarkEdit({row}){
      console.log(row)
      row.remarkFlag = true
    },
    tableRowClassName({ row }) {
      if (row.hasBack == 'Y') {
        return "red-row";
      }
      return "";
    },
    completeTheTopUp(){

    },
    into() {
      getDicts("account_zone").then(response => {
          this.zonrList = response.data;
        }
      );

      // 获取表格列表
      this.getList();
      //设置默认时间
      // this.queryParams.receiveTime = [this.parseTime(new Date().getTime()),this.parseTime(new Date().getTime())];
      // this.queryParams.doneTime = [this.parseTime(new Date().getTime()),this.parseTime(new Date().getTime())];
      //获取我今日已充值金额和系统剩余个数
      this.getTodayR();

      //获取人员列表
      this.getListExcludeMe();
      // this.time = setInterval(()=>{
      //   setTimeout(this.getList, 0)
      // },1000 *6)
    },
    // 处理删除卡片id放到删除集合中
    handleDeleteGiftCardRechargeRecordIds(row) {
      removeById(row.id).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("删除成功");
          getAccRecharge(this.form.acid).then(response => {
            this.form = response.data;
            this.$set(this.form, 'pledgeThirtyMinutes', false);////设置质押30分钟为关闭
            this.form.updateUser = this.form.updateUser || this.$store.state.user.name;
            this.tmpRechargeAmt = response.data.rechargeAmt;
            this.tmpBuyAmt = response.data.buyAmt;
            this.title = "账号充值";
            this.rechargeAlertGiftCardRecordList = response.data.giftCardRechargeRecordList;
            this.deleteGiftCardRechargeRecordIds = []
            this.getList();
          });
        } else {
          this.$modal.msgError(response.msg);
        }
      })
    },
    async getRemainingAccounts(){
      const res = await getAccountRechargeResidualListTwo(this.$route.params.accountZone)
      console.log(res)
      this.level2Library = res.data.twoList
      this.level1Library = res.data.oneList
      this.myList = res.data.myList
    },
    yesterday() {
      // 昨天
      const date = new Date();
      const enddate = new Date(date);
      enddate.setDate(date.getDate() - 1);
      const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
      // 今天
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.$set(this.queryParams, 'receiveTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);
      this.$set(this.queryParams, 'doneTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);

      // const date = new Date();
      // const enddate = new Date(date);
      // enddate.setDate(date.getDate() - 1);
      // const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
      // this.$set(this.queryParams, 'receiveTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
      // this.$set(this.queryParams, 'doneTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
    },
    today() {
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      const end = new Date();
      end.setDate(end.getDate() + 1);
      const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
      this.$set(this.queryParams, 'receiveTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);
      this.$set(this.queryParams, 'doneTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);
    },
    //获取今日充值金额和作废率
    async getTodayR() {
      const res = await getTwoDataPanel(this.$route.params.accountZone);
      console.log(res)
      this.todayRechargeAmount = res.data.todayRechargeAmount
      this.rechargeCancelRate = res.data.rechargeCancelRate

    },
    getListExcludeMe() {
      getListExcludeMeRecharge({}).then(res => {
        this.partneList = res.rows;
      })
    },
    /** 查询账号充值列表 */
    getList() {
      this.loading = true;
      const request = {
        ...this.queryParams,
        startDoneTime: this.queryParams.doneTime ? this.queryParams.doneTime[0] : null,//开始完成时间
        endDoneTime: this.queryParams.doneTime ? this.queryParams.doneTime[1] : null,//结束完成时间
        startReceiveTime: this.queryParams.receiveTime ? this.queryParams.receiveTime[0] : null,//开始领取时间
        endReceiveTime: this.queryParams.receiveTime ? this.queryParams.receiveTime[1] : null,//结束执行时间
      }
      getDicts("sys_should_amt").then(response => {
        this.str = response.data
      });
      //获取一级二级库剩余账号
      this.getRemainingAccounts()
      twoLevelAccountRechargeList(request).then(response => {
        this.accRechargeList = response.rows.map(e=>{
          return {
            ...e,
            remarkFlag:false
          }
        })
        this.total = response.total;
        this.loading = false;
      });
    },
    /**
     * 判断是否为数字
     */
    checkIsNumber(number) {
      var numReg = /^\d+(\.\d+)?$/g;
      var numRe = new RegExp(numReg)
      if (numRe.test(number)) {
        return true;
      } else {
        return false;
      }
    },
    //新增取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    ccountCancel() {
      this.accountOpen = false;
    },
    //转交取消
    transmitCancel() {
      this.transmitOpen = false;

    },
    // 表单重置
    reset() {
      this.form = {
        acid: null,
        accountName: null,
        accountPwd: null,
        accountZone: null,
        status: null,
        buyPrice: null,
        buyAmt: null,
        rechargeAmt: null,
        createTime: null,
        createUser: null,
        updateTime: null,
        updateUser: null,
        doneTime: null,
        doneUser: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.queryParams.accountName = undefined;
      this.queryParams.status = undefined;
      this.queryParams.giftCard = undefined;
      this.queryParams.receiveTime = undefined;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.acid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.disable = false
      this.form.createUser = this.$store.state.user.name;
      this.form.updateUser = this.$store.state.user.name;
      this.open = true;
      this.title = "添加账号充值";
    },
    /**完成充值*/
    handleComplete(row) {
      this.$modal.confirm(`账号【${row.accountName}】，请确认是否直接完成？`).then(async () => {
        const res = await theSecondLevelRechargeIsDirectlyCompleted(row.acid);
        if (res.code === 200) {
          this.getList();
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const acid = row.acid || this.ids
      this.$router.push('/applereg/secondaryTopUp/'+acid)
      // getAccRecharge(acid).then(response => {
      //   this.form = response.data;
      //   this.$set(this.form, 'pledgeThirtyMinutes', false);////设置质押30分钟为关闭
      //   this.form.updateUser = this.form.updateUser || this.$store.state.user.name;
      //   this.open = true;
      //   this.disable = true;
      //   this.tmpRechargeAmt = response.data.rechargeAmt;
      //   this.tmpBuyAmt = response.data.buyAmt;
      //   this.title = "账号充值";
      //   this.rechargeAlertGiftCardRecordList = response.data.giftCardRechargeRecordList;
      //   this.deleteGiftCardRechargeRecordIds = []
      // });
    },
    // 计算成本金额:单价*充值金额
    checkCartAmtSimple() {
      if (this.checkIsNumber(this.form.buyPrice) && this.checkIsNumber(this.form.rechargeAmt)) {
        //保留两位小数，四舍五入
        this.form.buyAmt = (Number(this.form.buyPrice || 0) * Number(this.form.rechargeAmt || 0)).toFixed(2);
        // if (this.form.buyPrice > 0 && this.form.rechargeAmt > 0) {
        //
        // }
      }
    },
    /**
     * 计算成本金额
     */
    checkCartAmt() {
      if (this.form.status === '2') {//如果是部分充值
        let jisuanrechargeAmt = Number(this.tmpRechargeAmt || 0) + Number(this.form.timeAmt || 0)
        this.form.rechargeAmt = jisuanrechargeAmt;//实际充值金额要加上本次充值金额

        // let jisuanrechargeAmt = Number(this.form.rechargeAmt || 0) + Number(this.form.timeAmt || 0)
        // console.log("Number(this.form.rechargeAmt || 0)：",Number(this.form.rechargeAmt || 0))
        // console.log("Number(this.form.timeAmt || 0)：",Number(this.form.timeAmt || 0))
        // this.form.rechargeAmt = jisuanrechargeAmt;//实际充值金额要加上本次充值金额
        if (this.checkIsNumber(this.form.buyPrice) && this.checkIsNumber(this.form.rechargeAmt)) {
          if (this.form.buyPrice > 0 && this.form.rechargeAmt > 0) {
            //保留两位小数，四舍五入
            // 成本等于 = (充值金额+本次充值金额) * 本次进价
            console.log('rechargeAmt:' + this.form.rechargeAmt)
            console.log('timeAmt:' + this.form.timeAmt)
            console.log('buyPrice:' + this.form.buyPrice)

            // this.form.buyAmt = ((Number(jisuanrechargeAmt)) * Number(this.form.buyPrice)).toFixed(2);

            this.form.buyAmt = (Number(this.tmpBuyAmt) + ((Number(this.form.timeAmt)) * Number(this.form.buyPrice))).toFixed(2);
            // 成本等于 本次充值金额 *价格 + 已经充值成本
            // this.form.buyAmt = (Number(this.form.timeAmt) * Number(this.form.buyPrice) + this.tmpBuyAmt).toFixed(2);
          }
        }
        return;
      }
      if (this.checkIsNumber(this.form.buyPrice) && this.checkIsNumber(this.form.rechargeAmt)) {
        if (this.form.buyPrice > 0 && this.form.rechargeAmt > 0) {
          //保留两位小数，四舍五入
          this.form.buyAmt = (this.form.buyPrice * this.form.rechargeAmt).toFixed(2);
        }
      }
    },
    /**
     * 充值弹窗
     */
    apopover(num) {
      var amount = 1;
      this.$refs["form"].validate(valid => {
        if (valid) {
          // delete this.form.timeAmt;
          if (this.form.buyPrice <= 0 || this.form.rechargeAmt <= 0) {
            this.$modal.msgError(`充值金额和单价必须大于零`);
            return;
          }
          if (this.form.status === '2' && this.form.timeAmt <= 0) {
            this.$modal.msgError(`充值金额和单价必须大于零`);
            return;
          }
          if (this.form.buyPrice >= 7.5) {
            this.$modal.msgError(`进价必须小于7.5`);
            return;
          }
          if (num == 1) {
            for (let i = 0; i < this.str.length; i++) {
              if (this.form.rechargeAmt != this.str[i].dictValue) {
                amount = 2
              } else {
                amount = 3
                break;
              }
            }
            if (amount == 2) {
              this.$confirm('充值金额必须是 200 250 300 350 400 450 500,请确认充值金额,是否充值?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.submitForm();
                this.$message({
                  type: 'success',
                  message: '充值成功!'
                })
              }).catch(() => {
                this.$message({
                  type: 'success',
                  message: '已取消充值!'
                });
              });
              return;
            }
          }

          this.$confirm('是否充值?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (num == 1) {
              this.submitForm();
            } else if (num == 2) {
              this.bufenSubmitForm();
            }
            this.$message({
              type: 'success',
              message: '充值成功!'
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消充值'
            });
          });
        }
      })
    },
    /** 部分充值提交按钮 */
    bufenSubmitForm() {
      // this.$refs["form"].validate(valid => {
      //   if (valid) {
      //     if (this.form.buyPrice <= 0 || this.form.rechargeAmt <= 0) {
      //       this.$modal.msgError(`充值金额和单价必须大于零`);
      //       return
      //     }
      //     if (this.form.status === '2' && this.form.timeAmt <= 0) {
      //       this.$modal.msgError(`充值金额和单价必须大于零`);
      //       return
      //     }
      if (this.form.acid != null) {
        const request = {
          ...this.form,
          status: '2',
          pledgeThirtyMinutes: this.form.pledgeThirtyMinutes ? '1' : '0',
          deleteGiftCardRechargeRecordIds: this.deleteGiftCardRechargeRecordIds
        }
        updateAccRecharge(request).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
          this.getTodayR();
        });
      } else {
        addAccRecharge(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
          this.getTodayR();
        });
      }
      //   }
      // });
    },
    /**作废提交按钮事件*/
    submitNullifyForm() {
      this.$refs["nullifyForm"].validate(async valid => {
        if (valid) {
          const request = {
            ...this.nullifyForm
          }
          const newCance_res = await level2TopUpIsVoid(request);
          if (newCance_res.code === 200) {
            this.nullifyVisible = false;
            this.getList();
            this.getTodayR()
            this.$modal.msgSuccess("作废成功");
          }
        }
      });
    },
    nullifyCancel() {
      this.nullifyVisible = false;
    },
    // 一键复制账号
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    /**详情取消*/
    detailCancel() {
      this.rechargedetailVisible = false;
    },
    /** 完成充值提交按钮 */
    submitForm() {
      // this.$refs["form"].validate(valid => {
      //   if (valid) {
      //     // delete this.form.timeAmt;
      //     if (this.form.buyPrice <= 0 || this.form.rechargeAmt <= 0) {
      //       this.$modal.msgError(`充值金额和单价必须大于零`);
      //       return
      //     }
      //     if (this.form.status === '2' && this.form.timeAmt <= 0) {
      //       this.$modal.msgError(`充值金额和单价必须大于零`);
      //       return
      //     }
      if (this.form.acid != null) {
        const request = {
          ...this.form,
          status: '3',
          pledgeThirtyMinutes: this.form.pledgeThirtyMinutes ? '1' : '0',
          deleteGiftCardRechargeRecordIds: this.deleteGiftCardRechargeRecordIds
        }
        updateAccRecharge(request).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
          this.getTodayR();
        });
      } else {
        //设置状态为：完成充值
        this.form.status = 3;
        addAccRecharge(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
          this.getTodayR();
        });
      }
      //   }
      // });
    },
    // 账号领取弹窗点击
    handleClaimAccount(e) {
      let flag = checkPermi(['system:twoLevelAccountRecharge:receiveAccount'])
      if (flag){
        this.accountForm = {
          chargeStage:e.chargeStage,
          value:e.cardBalance,
          accountZone:this.queryParams.accountZone
        };
        this.accountOpen = true;
      }

    },
    // 领取账号提交
    submitAccountForm() {
      this.$refs["accountForm"].validate( async(valid) => {
        if (valid) {
          console.log(this.accountForm,this.queryParams)
          const res = await twoLevelAccountRechargeReceiveAccount(this.accountForm)
          if (res.code === 200) {
            this.getTodayR();
            this.getList();
            this.accountOpen = false;
            this.$modal.msgSuccess("领取账号成功！");

          }
        }
      })
    },
    // 转交账号弹窗
    handleTransferAccount() {
      this.transferForm = {
        userName: null
      };
      this.transmitOpen = true;
    },
    // 账号转交表单提交
    submitTransferForm() {
      this.$refs["transferForm"].validate(valid => {
        if (valid) {
          let findItme = this.partneList.find(itme => {
            return itme.userId === this.transferForm.userId
          })
          this.transferForm.userName = findItme.userName;
          console.log(this.transferForm);
          transferAccountRecharge(this.transferForm).then(res => {
            if (res.code === 200) {
              this.getList();
              this.$modal.msgSuccess("转交成功！");
              this.transmitOpen = false;
            }
          })
        }
      })
    },
    /** 作废按钮操作 */
    handleDelete(row) {
      this.nullifyVisible = true;
      this.nullifyForm = {
        acid: row.acid,
        accountName: row.accountName,
      };
      this.resetForm("nullifyForm");
      return
      const acids = row.acid || this.ids;
      this.$modal.confirm('是否确认作废账号为"' + row.accountName + '"的数据项？').then(function () {
        // return delAccRecharge(acids);
        return cancelAccRecharge(acids)
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("作废成功");
      }).catch(() => {
      });
    },
    /**详情按钮事件*/
    async handleDetail(row) {
      const detail_res = await getAccRecharge(row.acid);
      if (detail_res.code === 200) {
        this.giftCardRechargeRecordList = detail_res.data.giftCardRechargeRecordList;
        this.rechargedetailVisible = true;
        this.rechargedetailForm = detail_res.data;
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/new/system/accRecharge/export', {
        ...this.queryParams
      }, `accRecharge_${new Date().getTime()}.xlsx`)
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      if (res.code === 200) {
        this.$modal.confirm(res.msg).then(function () {
        })
        // this.$modal.msgSuccess(res.msg);
        this.getList();//重新刷新列表
        this.getTodayR();
      } else {
        this.$modal.msgError(res.msg || '导入失败');
      }
      // console.log(res.fileName+"---"+ res.fileName);
      // this.uploadList.push({ name: res.fileName, url: res.fileName });
      // if (this.uploadList.length === this.number) {
      //   this.fileList = this.fileList.concat(this.uploadList);
      //   this.uploadList = [];
      //   this.number = 0;
      //   this.$emit("input", this.listToString(this.fileList));
      //   this.$modal.msg("导入成功！")
      //   this.$modal.closeLoading();
      // }

    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("导入失败，请重试");
      this.$modal.closeLoading();
    },
    beforeUpload(file) {
      //判断上传的类型
      console.log(file);
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$notify.error({
          title: "错误",
          message: "上传文件只能为excel文件，且为xlsx,xls格式",
        });
        this.filelist = [];
        return false;
      } else {
        //上传大小限制
        const size = file.size / 1024 / 1024;
        console.log("size", size);
        if (size > 5) {
          this.$notify.warning({
            title: "警告",
            message: "大小必须小于5M",
          });
          return;
        } else {
          this.isshow = false;
        }
      }
      this.filename = file.name;
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.info(this.fileList);
    },
    // 删除文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止删除。function(file, fileList)
    //上传文件事件
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    formatRechargeStatus(status) {
      let statusName = '';
      if (status) {
        let rechargeItme = this.dict.type.recharge_status.find(itme => {
          return itme.value === status;
        })
        if (rechargeItme) {
          statusName = rechargeItme.label;
        }
      }
      return statusName
    },
    //转译作废原因
    formatCancelReasonType(type) {
      let typeName = '';
      const types = [{label: '锁定', value: '1'}, {label: '双禁', value: '2'}, {label: '双重验证', value: '3'}];
      if (type) {
        let typeItme = types.find(itme => {
          return itme.value === type;
        })
        if (typeItme) {
          typeName = typeItme.label;
        }
      }
      return typeName
    },
    getLimit(limitStr) {
      if (limitStr) return limitStr.split(',').length;
    },
    getSummaries(param) {
      const notTotals = [1, 2, 3,4, 7, 8, 9] //不需要小计的列数组
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '小计';
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += ' ';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
  },
  activated() {
    this.getList()
    this.getTodayR();
    // this.time = setInterval(()=>{
    //   setTimeout(this.getList, 0)
    // },1000 *6)
  },
  deactivated() {
    // clearInterval(this.time);
    // this.time = null;

  },
  beforeDestroy() {
    //  clearInterval(this.time);
    //  this.time = null;
  },
};
</script>

<style lang="scss" scoped>
.el-upload__tip {
  line-height: 1.2;
}

.el-row {
  margin-bottom: 25px;
  color: #606266;
  font-size: 14px;
  font-weight: 700;
}

.item .el-form-item__label {
  color: #1890ff;

}
</style>
