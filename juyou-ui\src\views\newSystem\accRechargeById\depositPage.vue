<template>
  <el-form style="max-width: 800px;margin: 20px auto" ref="form" :model="form" :rules="rules" label-width="120px" label-position="right" :inline="true">
      <div style="display:flex;justify-content: space-between">
        <el-form-item label="账号：" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入账号名称"
                    :disabled="form.status === '1' || form.status === '2'"/>
        </el-form-item>

        <el-form-item label="密码：" prop="accountPwd">
          <el-input v-model="form.accountPwd" placeholder="请输入密码"
                    :disabled="form.status === '1' || form.status === '2'"/>
        </el-form-item>
      </div>

    <div style="display:flex;justify-content: space-between">
      <el-form-item label="ID余额：" prop="accountName">
        <el-input v-model="form.accountName" placeholder="请输入账号名称"
                  :disabled="form.status === '1' || form.status === '2'"/>
      </el-form-item>

      <el-form-item label="一级充值人：" prop="accountPwd">
        <el-input v-model="form.accountPwd" placeholder="请输入密码"
                  :disabled="form.status === '1' || form.status === '2'"/>
      </el-form-item>
    </div>

    <div style="display:flex;justify-content: space-between">
      <el-form-item label="二级充值人：" prop="accountName">
        <el-input v-model="form.accountName" placeholder="请输入账号名称"
                  :disabled="form.status === '1' || form.status === '2'"/>
      </el-form-item>

      <el-form-item label="退回原因：" prop="accountPwd">
        <el-input v-model="form.accountPwd" placeholder="请输入密码"
                  :disabled="form.status === '1' || form.status === '2'"/>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>
export default {
  name: "depositPage",
  data() {
    return {
      form:{}
    }
  },
  methods:{
    checkCartAmt(){

    }
  }
}
</script>



<style scoped lang="scss">

</style>
