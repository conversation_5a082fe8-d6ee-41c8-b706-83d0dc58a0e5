<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="95px">
      <el-form-item label="账号" prop="account">
        <el-input
          v-model="queryParams.account"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="礼品卡" prop="giftCard">
        <el-input
          v-model="queryParams.giftCard"
          placeholder="请输入礼品卡"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="来源群" prop="sourceGroup">
        <el-input
          v-model="queryParams.sourceGroup"
          placeholder="请输入来源群"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="操作人" prop="operator">
        <el-input
          v-model="queryParams.operator"
          placeholder="请输入操作人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="面值" prop="faceValue">
        <el-input
          v-model="queryParams.faceValue"
          placeholder="请输入面值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="区域" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option :key="'all'" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="item in zonrList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="执行时间" prop="executionTime">
        <el-date-picker v-model="queryParams.executionTime"
                        type="datetimerange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
        <el-button type="primary" size="mini" @click="today">今天</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-upload accept=".xls,.xlsx" :action="uploadaccFilePath"
                   :multiple="false"
                   :file-list="fileList"
                   :headers="headers"
                   :on-success="handleUploadSuccess"
                   :on-error="handleUploadError"
                   :on-change="handleChange"
                   :on-remove="handleRemove">
          <el-button type="primary" icon="el-icon-upload">导入充值卡密信息</el-button>
          <div slot="tip" class="el-upload__tip">只能上传不超过 3MB 的.xls,.xlsx文件</div>
        </el-upload>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="recordList" :summary-method="getSummaries" show-summary>
      <el-table-column label="账号" align="center" prop="account">
        <template slot-scope="scope">
          {{scope.row.account?scope.row.account.substr(0,4) + '***' +
          scope.row.account.substr(scope.row.account.length-4,scope.row.account.length) :''}}
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
          scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="礼品卡" align="center" prop="giftCard"/>
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <el-table-column label="来源群" align="center" prop="sourceGroup"/>
      <el-table-column label="卡状态" align="center" prop="cardStatus"></el-table-column>
      <el-table-column label="面值" align="center" prop="faceValue"></el-table-column>
      <el-table-column label="余额" align="center" prop="balance"/>
      <el-table-column label="执行时间" align="center" prop="executionTime"/>
      <el-table-column label="操作人" align="center" prop="createBy"/>
      <el-table-column label="执行信息" align="center" width="185" prop="executionInfo"/>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"

    />
  </div>
</template>

<script>
  import {
    recordList,
  } from "@/api/system/rechargeCard";
  import {getToken} from "@/utils/auth";
  import {getDicts} from "@/api/system/dict/data";
  import Cookies from "js-cookie";

  export default {
    name: "rechargeCardRecordList",
    data() {
      return {
        uploadaccFilePath: process.env.VUE_APP_BASE_API + "/system/record/importData",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        fileList: [],
        //充值记录列表列表
        recordList: [],
        // 遮罩层
        loading: true,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
        },
        zonrList: [],
      };
    },
    created() {

      this.into();
    },
    methods: {
      into() {
        let cook = Cookies.get("rechargeCardRecordList");
        if (cook != null && cook != undefined) {
          this.queryParams = JSON.parse(cook);
        }
        getDicts("account_zone").then(response => {
          this.zonrList = response.data;
        })
        this.getList();//获取未生效状态的账号
      },
      yesterday() {
        // 昨天
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 1);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        this.$set(this.queryParams, 'executionTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);

        // const date = new Date();
        // const enddate = new Date(date);
        // enddate.setDate(date.getDate() - 1);
        // const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // this.$set(this.queryParams, 'executionTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
      },
      today() {
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        const end = new Date();
        end.setDate(end.getDate() + 1);
        const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
        this.$set(this.queryParams, 'executionTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);

        // const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        // this.$set(this.queryParams, 'executionTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
      },

      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          const request = {
            ...this.queryParams,
            startExecutionTime: this.queryParams.executionTime ? this.queryParams.executionTime[0] : null,//开始执行时间
            endExecutionTime: this.queryParams.executionTime ? this.queryParams.executionTime[1] : null,//结束执行时间
          };
          Cookies.set('rechargeCardRecordList',JSON.stringify(this.queryParams));
          const recordList_res = await recordList(request);
          if (recordList_res.code === 200) {
            this.recordList = recordList_res.rows;
            this.total = recordList_res.total;
            this.loading = false;
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: false,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
        }
        this.handleQuery();
      },
      // 上传成功回调
      handleUploadSuccess(res) {
        if (res.code === 200) {
          this.$modal.msgSuccess(res.msg || '导入成功！');
          this.getList();//重新刷新列表
        } else {
          this.$modal.msgError(res.msg || '导入失败');
        }
      },
      // 上传失败
      handleUploadError() {
        this.$modal.msgError("导入失败，请重试");
        this.$modal.closeLoading();
      },
      handleChange(file, fileList) {
        this.fileList = fileList;
      },
      // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
      handleRemove(file, fileList) {
        this.fileList = fileList;
        console.info(this.fileList);
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3, 4, 5, 8, 10] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' ';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
