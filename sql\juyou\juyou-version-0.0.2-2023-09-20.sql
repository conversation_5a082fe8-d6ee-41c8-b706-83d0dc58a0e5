-- 账号充值添加 作废原因 列
alter table two_account_recharge add column `cancel_reason_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作废原因类型: 1 锁定 2 双禁 3 双重验证' after recharge_amt;

-- 账号充值添加 双禁余额 列
alter table two_account_recharge add column `double_prohibited_balance` double DEFAULT NULL COMMENT'双禁余额' after cancel_reason_type;

-- 账号充值添加 作废图片 列
alter table two_account_recharge add column `cancel_imgs` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作废图片' after double_prohibited_balance;

-- 账号充值添加 作废时间 列
alter table two_account_recharge add column `cancel_date` datetime DEFAULT NULL  COMMENT'作废时间' after cancel_imgs;

-- 账号出售添加 作废时间 列
alter table two_account_sell add column `cancel_date` datetime DEFAULT NULL  COMMENT'作废时间' after remark;

-- 账号出售添加 作废原因 列
alter table two_account_sell add column `cancel_reason` varchar(520) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作废原因' after cancel_date;

-- 账号出售添加 作废图片 列
alter table two_account_sell add column `cancel_imgs` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作废图片' after cancel_reason;


-- 账号充值 作废时间老数据处理: 把状态为作废的数据updateTime时间更新到作废时间
update two_account_recharge ar
set ar.cancel_date = ar.update_time
WHERE ar.`status` = 4;

-- 账号出售 作废时间老数据处理: 把状态为在作废的数据updateTime时间跟新到作废时间
update two_account_sell ase
set ase.cancel_date = ase.update_time
where ase.`status` = 4;


-- 礼品卡充值记录添加 是否质押30分钟 列 0 否 1 是
alter table two_gift_card_recharge_record add column `pledge_thirty_minutes` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否质押30分钟: 0 否 1 是' after execution_info;

-- 礼品卡充值记录添加 开始质押时间
alter table two_gift_card_recharge_record add column `pledge_start_date` datetime DEFAULT NULL COMMENT '质押开始时间' after pledge_thirty_minutes;

-- 礼品卡充值记录添加 结束质押时间
alter table two_gift_card_recharge_record add column `pledge_end_date` datetime DEFAULT NULL COMMENT '质押结束时间' after pledge_start_date;


-- 权限新增数据
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2054, '完成充值', 2004, 12, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:accRecharge:completeRecharge', '#', 'admin', '2023-09-20 17:25:04', '', NULL, '');
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2055, '作废统计列表', 2045, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:cancelList', '#', 'admin', '2023-09-21 14:28:54', '', NULL, '');

