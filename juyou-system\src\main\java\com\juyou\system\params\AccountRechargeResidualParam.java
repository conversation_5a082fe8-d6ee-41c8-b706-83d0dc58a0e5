package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号充值-剩余账号-param
 */
@Data
@ApiModel("AccountRechargeResidualParam")
public class AccountRechargeResidualParam implements Serializable {

    private static final long serialVersionUID = 5925741444621972622L;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("登录用户id:后端用")
    private Long loginUserId;

    @ApiModelProperty("id2-一级库-等待分钟:后端用")
    private Integer onePendingMinutes;

    @ApiModelProperty("id2-二级库-等待分钟:后端用")
    private Integer twoPendingMinutes;

}
