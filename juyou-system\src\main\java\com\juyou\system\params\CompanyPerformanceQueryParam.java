package com.juyou.system.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class CompanyPerformanceQueryParam {

    //部门
    @ApiModelProperty("部门")
    private String deptName;
    //员工
    @ApiModelProperty("员工")
    private String userName;
    //员工ID
    @ApiModelProperty("员工ID")
    private Long  userId;
    //部门ID
    @ApiModelProperty("部门ID")
    private Long deptId;
    //开始工作日期
    @ApiModelProperty("开始工作日期")
    private Date startTime;
    //结束工作日期
    @ApiModelProperty("结束工作日期")
    private Date endTime;

    @ApiModelProperty("ID用户")
    private String idUser;
}
