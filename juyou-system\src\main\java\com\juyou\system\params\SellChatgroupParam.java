package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 出售群统计
 */
@Data
@ApiModel("SellChatgroupParam")
public class SellChatgroupParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -4467135348173709562L;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellTime;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellTime;
}
