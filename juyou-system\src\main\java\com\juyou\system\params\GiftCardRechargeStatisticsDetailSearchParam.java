package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 礼品卡充值来源群统计详情列表-search-param
 */
@Data
@ApiModel("GiftCardRechargeStatisticsDetailSearchParam")
public class GiftCardRechargeStatisticsDetailSearchParam implements Serializable {

    private static final long serialVersionUID = 6794652470752844585L;

    @ApiModelProperty("充值来源群")
    private String sourceGroup;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("充值完成开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startRechargeCompleteTime;

    @ApiModelProperty("充值完成结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endRechargeCompleteTime;

}
