package com.juyou.system.service;

import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.params.AccountRechargeEditParam;
import com.juyou.system.params.AccountRechargeParam;
import com.juyou.system.params.GiftCardRechargeRecordPageParam;
import com.juyou.system.params.GiftCardRechargeStatisticsDetailSearchParam;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 礼品卡充值记录Service接口
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
public interface IGiftCardRechargeRecordService {


    /**
     * 礼品卡充值来源群统计详情列表
     *
     * @param param
     * @return
     */
    List<GiftCardRechargeRecord> getGiftCardRechargeStatisticsDetailList(GiftCardRechargeStatisticsDetailSearchParam param);


    /**
     * 获取账号的总成本
     * @param account
     * @return
     */
    BigDecimal getBuyAmt(String account);

    /**
     * 获取账号总面值
     * @param account
     * @return
     */
    BigDecimal getFaceValue(String account);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int batchInsert(List<AccountRechargeParam.RechargeGiftCardRechargeRecordListVo> list, AccountRecharge recharge,
                    String createBy);

    /**
     * 添加礼品卡充值记录,param-账号编辑参数对象
     *
     * @param param
     * @return
     */
    int insert(AccountRechargeEditParam param, String currentUsername);

    /**
     * 统计质押30分钟
     *
     * @param account
     * @return
     */
    int countPledgeThirtyMinutes(String account);

    /**
     * 查询礼品卡充值记录
     *
     * @param id 礼品卡充值记录主键
     * @return 礼品卡充值记录
     */
    public GiftCardRechargeRecord selectGiftCardRechargeRecordById(Long id);


    /**
     * 查询礼品卡充值记录分页列表
     *
     * @param param
     * @return
     */
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordPage(GiftCardRechargeRecordPageParam param);

    /**
     * 查询礼品卡充值记录列表
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 礼品卡充值记录集合
     */
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordList(GiftCardRechargeRecord giftCardRechargeRecord);

    /**
     * 新增礼品卡充值记录
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    public int insertGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord);

    /**
     * 修改礼品卡充值记录
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    public int updateGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord);


    /**
     * 批量删除
     * @param accountName
     * @return
     */
    int deleteByAccountName(String accountName);

    /**
     * 批量删除礼品卡充值记录
     *
     * @param ids 需要删除的礼品卡充值记录主键集合
     * @return 结果
     */
    public int deleteGiftCardRechargeRecordByIds(Long[] ids);

    /**
     * 删除礼品卡充值记录信息
     *
     * @param id 礼品卡充值记录主键
     * @return 结果
     */
    public int deleteGiftCardRechargeRecordById(Long id);
}
