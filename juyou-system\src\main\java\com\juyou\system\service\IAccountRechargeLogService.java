package com.juyou.system.service;

import java.util.List;
import com.juyou.system.domain.AccountRechargeLog;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
public interface IAccountRechargeLogService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public AccountRechargeLog selectAccountRechargeLogById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<AccountRechargeLog> selectAccountRechargeLogList(AccountRechargeLog accountRechargeLog);

    /**
     * 新增【请填写功能名称】
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 结果
     */
    public int insertAccountRechargeLog(AccountRechargeLog accountRechargeLog);

    /**
     * 修改【请填写功能名称】
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 结果
     */
    public int updateAccountRechargeLog(AccountRechargeLog accountRechargeLog);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteAccountRechargeLogByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAccountRechargeLogById(Long id);
}
