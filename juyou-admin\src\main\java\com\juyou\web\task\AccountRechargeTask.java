package com.juyou.web.task;

import cn.hutool.core.date.DateUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.service.IAccountRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 账号充值定时任务
 */
@Slf4j
@Component("accountRechargeTask")
public class AccountRechargeTask {


    @Autowired
    private IAccountRechargeService accountRechargeService;

    public void run() {
        log.info("start----------更新账号充值待领取分钟数：运行时间:{}", DateUtil.formatDateTime(new Date()));
        // 一级充值完成后，可以立即出售；等3天，自动放二级库；
        boolean b = this.accountRechargeService.pendingMinutesPutTwoChargeStage();
        log.info("end----------更新账号充值待领取分钟数：运行时间:{}, 执行结果:{}", DateUtil.formatDateTime(new Date()), b);
    }

}
