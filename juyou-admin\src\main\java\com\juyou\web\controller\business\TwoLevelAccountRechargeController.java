package com.juyou.web.controller.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.AccountRechargeDataPanelVo;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.domain.vo.AccountRechargeResidualVo;
import com.juyou.system.domain.vo.AccountSellResidualAccountVoList;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.params.*;
import com.juyou.system.service.IAccountRechargePlusService;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import com.juyou.system.service.ISysConfigService;
import com.juyou.system.utils.JuYouBusinessUtil;
import com.juyou.system.utils.LockUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 一级账号充值-controller
 */
@Api(value = "二级账号充值", tags = "二级账号充值")
@RestController
@RequestMapping("/manager/twoLevelAccountRecharge")
public class TwoLevelAccountRechargeController extends BaseController {



    @Autowired
    private IAccountRechargeService accountRechargeService;

    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @Autowired
    private IAccountRechargePlusService accountRechargePlusService;

    @Autowired
    private ISysConfigService sysConfigService;

    @ApiOperation("编辑备注")
    @PostMapping("/editRemark")
    public ResultData<Boolean> editRemark(@RequestBody AccountRechargeEditRemarkParam param){
        // check
        if(ObjUtil.isNull(param.getAcid())){
            throw new ServiceException("acid不能为空!");
        }

        Boolean b = this.accountRechargeService.editRemark(param);

        return ResultUtil.success(b);
    }

    @ApiOperation("领取账号")
    @Log(title = "领取账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:receiveAccount')")
    @PostMapping("/receiveAccount")
    public AjaxResult receiveAccount(@RequestBody AccountRechargeReceiveParam param) {

        // check
        if (ObjUtil.isNull(param.getCount())) {
            throw new ServiceException("数量不能为空!");
        }

        if (ObjUtil.isNull(param.getValue())) {
            throw new ServiceException("面值不能为空!");
        }

        if (StrUtil.isBlank(param.getAccountZone())) {
            throw new ServiceException("区域不能为空!");
        }

        if (StrUtil.isBlank(param.getChargeStage())) {
            throw new ServiceException("阶段不能为空!");
        }

        param.setLoginUserId(getUserId());
        param.setLoginUserName(getUsername());
        param.setDeptId(getDeptId());

        String onwPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(onwPendingMinutes)){
            throw new ServiceException("id2-账号充值-一级库等待分钟没有配置!");
        }
        param.setOnePendingMinutes(Integer.valueOf(onwPendingMinutes));

        String twoPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_TWO_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(twoPendingMinutes)){
            throw new ServiceException("id2-账号充值-二级库等待分钟没有配置!");
        }
        param.setTwoPendingMinutes(Integer.valueOf(twoPendingMinutes));

        ReentrantLock lock = LockUtil.getLock();
        try {
            int i = 0;
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                i = this.accountRechargeService.receiveAccountTwo(param);
                if (i == 0) {
                    throw new ServiceException("请稍后重试，没有待分配的账号!");
                }
                return toAjax(i);
            }
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } catch (InterruptedException e) {
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation("剩余账号列表")
    @Log(title = "剩余账号列表", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:getAccountRechargeResidualList')")
    @GetMapping("getAccountRechargeResidualList")
    public ResultData<AccountRechargeResidualVo> getAccountRechargeResidualList(AccountRechargeResidualParam param) {
        // check
        if (StrUtil.isBlank(param.getAccountZone())) {
            throw new ServiceException("区域不能为空!");
        }
        param.setLoginUserId(getUserId());

        AccountRechargeResidualVo vo = new AccountRechargeResidualVo();

        // 一级账号列表
        List<AccountSellResidualAccountVoList> oneList = this.accountRechargeService.findOneLevelResidualAccountList(param);
        vo.setOneList(oneList);

        String onwPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(onwPendingMinutes)){
            throw new ServiceException("id2-账号充值-一级库等待分钟没有配置!");
        }
        param.setOnePendingMinutes(Integer.valueOf(onwPendingMinutes));

        String twoPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_TWO_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(twoPendingMinutes)){
            throw new ServiceException("id2-账号充值-二级库等待分钟没有配置!");
        }
        param.setTwoPendingMinutes(Integer.valueOf(twoPendingMinutes));

        // 二级账号列表
        List<AccountSellResidualAccountVoList> twoList = this.accountRechargeService.findTwoLevelResidualAccountList(param);
        vo.setTwoList(twoList);

        // 我的账号
        List<AccountSellResidualAccountVoList> myList = this.accountRechargeService.findMyResidualAccountList(param);
        vo.setMyList(myList);

        return ResultUtil.success(vo);
    }

    @ApiOperation("充值")
    @Log(title = "充值", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:recharge')")
    @PostMapping("/recharge")
    public ResultData<Integer> recharge(@RequestBody AccountRechargeParam param) {
        // check
        if (ObjUtil.isNull(param.getAcid())) {
            throw new ServiceException("账号充值id不能为空!");
        }
        if (ObjUtil.isNull(param.getRechargeAmt())) {
            throw new ServiceException("ID余额不能为空!");
        }
        if (CollUtil.isEmpty(param.getCardList())) {
            throw new ServiceException("卡密列表不能为空!");
        }
        if(ObjUtil.isNull(param.getStatus())){
            throw new ServiceException("状态不能为空!");
        }
        if(CollUtil.isEmpty(param.getCardList())){
            throw new ServiceException("卡列表不能为空!");
        }

        param.setCurrentUserId(getUserId().intValue());
        param.setCurrentUsername(getUsername());

        int i = this.accountRechargeService.rechargeTwo(param);

        return ResultUtil.success(i);
    }


    @ApiOperation("列表")
    @Log(title = "列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountRecharge> list(AccountRechargePageParam param) {
        // check
        if (StrUtil.isBlank(param.getAccountZone())) {
            throw new ServiceException("区域不能为空!");
        }

        param.setReceiveUser(super.getUsername());
        List<AccountRecharge> list = this.accountRechargeService.selectAccountRechargePage(param);

        TableDataInfo<AccountRecharge> dataTable = getDataTable(list);
        if (CollUtil.isNotEmpty(dataTable.getRows())) {
            for (AccountRecharge item : dataTable.getRows()) {
                item.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(item.getBuyAmt(), item.getRechargeAmt()));
            }
        }
        return dataTable;
    }

    @ApiOperation("数据面板")
    @Log(title = "数据面板", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:getDataPanel')")
    @GetMapping("/getDataPanel")
    public ResultData<AccountRechargeDataPanelVo> getDataPanel(AccountRechargeDataPanelParam param) {
        // check
        if (StrUtil.isBlank(param.getAccountZone())) {
            throw new ServiceException("区域不能为空!");
        }

        param.setLoginUserId(getUserId());
        AccountRechargeDataPanelVo vo = this.accountRechargeService.getDataPanel(param);

        return ResultUtil.success(vo);
    }

    @ApiOperation("账号充值详情")
    @Log(title = "获取账号充值详细信息", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:detail')")
    @GetMapping(value = "/{acid}")
    public ResultData<AccountRechargeDetailVo> getInfo(@PathVariable("acid") Integer acid) {
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空");
        }
        AccountRechargeDetailVo detail = this.accountRechargeService.getDetail(acid);
        return ResultUtil.success(detail);
    }


    @ApiOperation("完成充值")
    @Log(title = "账号充值-完成充值", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:completeRecharge')")
    @PostMapping("/completeRecharge/{acid}")
    public ResultData<Integer> completeRecharge(@PathVariable("acid") Integer acid) {
        // check
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空!");
        }
        Date now = new Date();
        AccountRecharge recharge = this.accountRechargeService.selectAccountRechargeByAcid(acid);

        int count = this.iGiftCardRechargeRecordService.countPledgeThirtyMinutes(recharge.getAccountName());

        if (count > 0) {
            throw new ServiceException("礼品卡代码质押时间未超过30分钟。");
        }

        recharge.setStatus(AccountRechargeEnum.STATUS_3.getCode());
        recharge.setUpdateTime(now);
        recharge.setDoneTime(now);
        recharge.setPendingStartDate(now);
        recharge.setUpdateBy(super.getUsername());
        recharge.setDoneUser(super.getUsername());
        int i = this.accountRechargeService.updateAccountRecharge(recharge);
        return ResultUtil.success(i);
    }


    @ApiOperation("作废")
    @Log(title = "作废", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:twoLevelAccountRecharge:cancel')")
    @PostMapping("/cancel")
    public ResultData<Integer> cancel(@RequestBody AccountRechargeCancelParam param) {
        // check
        if (ObjUtil.isNull(param.getAcid())) {
            throw new ServiceException("acid,唯一标识不能为空!");
        }
        if (StrUtil.isBlank(param.getCancelReasonType())) {
            throw new ServiceException("作废原因不能为空!");
        }
        param.setUpdateBy(super.getUsername());

        int cancel = accountRechargeService.cancel(param);

        return ResultUtil.success(cancel);
    }

}
