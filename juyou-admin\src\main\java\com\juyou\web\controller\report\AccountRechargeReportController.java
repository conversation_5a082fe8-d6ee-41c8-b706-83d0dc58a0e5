package com.juyou.web.controller.report;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.params.AccountRechargeBatchWriteOffParam;
import com.juyou.system.params.AccountRechargePageParam;
import com.juyou.system.params.AccountSellBatchWriteOffParam;
import com.juyou.system.params.AccountSellPendingSaleSearchParam;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import com.juyou.system.utils.JuYouBusinessUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 充值明细报表-controller
 */
@Api(value = "充值明细报表", tags = "充值明细报表")
@RestController
@RequestMapping("/system/accountRechargeReport")
public class AccountRechargeReportController extends BaseController {

    @Autowired
    private IAccountRechargeService iAccountRechargeService;
    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @ApiOperation("查询充值明细报表")
    @Log(title = "充值明细报表-查询充值明细报表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountRechargeReport:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountRecharge> list(AccountRechargePageParam param) {
        startPage();
        List<AccountRecharge> list = this.iAccountRechargeService.selectAccountRechargeReportPage(param);
        TableDataInfo<AccountRecharge> dataTable = getDataTable(list);
        if (CollUtil.isNotEmpty(list)) {
            for (AccountRecharge item : dataTable.getRows()) {
                item.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(item.getBuyAmt(), item.getRechargeAmt()));
            }
        }

        return dataTable;
    }

    @ApiOperation("导出账号充值列表")
    @Log(title = "充值明细报表-导出账号充值列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountRechargeReport:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountRechargePageParam accountRecharge) {
        List<AccountRecharge> list = iAccountRechargeService.selectAccountRechargeReportPage(accountRecharge);
        if (CollUtil.isNotEmpty(accountRecharge.getIds())) {
            List<Integer> ids = accountRecharge.getIds();
            list = new ArrayList<>();
            for (Integer id : ids) {
                AccountRecharge item = this.iAccountRechargeService.selectAccountRechargeByAcid(id);
                list.add(item);
            }
        }
        ExcelUtil<AccountRecharge> util = new ExcelUtil<AccountRecharge>(AccountRecharge.class);
        util.exportExcel(response, list, "充值明细报表");
    }

    @ApiOperation("获取详情")
    @Log(title = "充值明细报表-获取详情", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountRechargeReport:detail')")
    @PostMapping("/getDetail/{acid}")
    public ResultData<AccountRechargeDetailVo> getDetail(@PathVariable Integer acid) {
        if (ObjUtil.isNull(acid)) {
            throw new ServiceException("acid不能为空");
        }
        AccountRecharge accountRecharge = this.iAccountRechargeService.selectAccountRechargeByAcid(acid);
        GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
        rechargeRecord.setAccount(accountRecharge.getAccountName());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(rechargeRecord);

        AccountRechargeDetailVo vo = new AccountRechargeDetailVo();
        BeanUtil.copyProperties(accountRecharge, vo);
        vo.setGiftCardRechargeRecordList(list);

        // 充值明细、出售明细详情：平均进价：完成充值时，核算平均进价；成本/充值金额=平均进价，两位小数
        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getRechargeAmt()));

        return ResultUtil.success(vo);
    }

    @ApiOperation("批量核对")
    @Log(title = "账号销售-已出售账号-批量核对", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:accountRechargeReport:batchWriteOff')")
    @PostMapping("/batchWriteOff")
    public ResultData<Integer> batchWriteOff(@RequestBody AccountRechargeBatchWriteOffParam param) {
        // check
        if (!CollUtil.isNotEmpty(param.getAcIdList())) {
            throw new ServiceException("请选择后操作!");
        }
        Integer i = this.iAccountRechargeService.batchWriteOff(param);
        return ResultUtil.success("ok", i);
    }
}
