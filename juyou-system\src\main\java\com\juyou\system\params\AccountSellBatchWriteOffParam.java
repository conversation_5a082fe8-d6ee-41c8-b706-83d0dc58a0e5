package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号出售核销-parma
 */
@Data
@ApiModel("AccountSellBatchWriteOffParam")
public class AccountSellBatchWriteOffParam implements Serializable {

    private static final long serialVersionUID = -5025314332925098784L;

    @ApiModelProperty("recharge_id集合")
    private List<Integer> rechargeIdList;

}
