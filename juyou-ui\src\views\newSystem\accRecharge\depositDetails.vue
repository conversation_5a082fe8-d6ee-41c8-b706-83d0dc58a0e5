<template>
  <div style="max-width: 1000px;margin: 20px auto">
    <el-radio-group style="margin-bottom: 20px" v-model="selectType">
      <el-radio-button label="0">充值</el-radio-button>
      <el-radio-button label="2">操作日志</el-radio-button>
    </el-radio-group>
    <div v-if="selectType === '0'">
      <el-form ref="form" :model="form" label-width="120px" label-position="right" :inline="true">
        <div style="display:flex;justify-content: space-between">
          <el-form-item label="账号：" prop="accountName">
            <el-input v-model="form.accountName" placeholder="请输入账号名称"
                      disabled/>
          </el-form-item>

          <el-form-item label="密码：" prop="accountPwd">
            <el-input v-model="form.accountPwd" placeholder="请输入密码"
                      disabled/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="ID余额：" prop="rechargeAmt">
            <el-input v-model="form.rechargeAmt" disabled placeholder="请输入ID余额"/>
          </el-form-item>

          <el-form-item label="一级充值人：" prop="primaryCharger">
            <el-input disabled v-model="form.primaryCharger" placeholder="请输入一级充值人"/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="二级充值人：" prop="secondaryCharger">
            <el-input disabled v-model="form.secondaryCharger" placeholder="请输入二级充值人"/>
          </el-form-item>


          <el-form-item v-if="form.idUser" label="ID归属业务员：" prop="idUser">
            <el-input disabled v-model="form.idUser" placeholder="请输入ID归属业务员"/>
          </el-form-item>
        </div>
        <div style="display:flex;justify-content: space-between">
          <el-form-item v-if="form.backReason" label="退回原因：" prop="backReason">
            <el-input disabled v-model="form.backReason" placeholder="请输入退回原因"/>
          </el-form-item>
        </div>
        <div style="display:flex;justify-content: space-between">
          <el-form-item v-if="form.cancelReasonType" label="作废原因：" prop="cancelReasonType">
            <el-select disabled v-model="form.cancelReasonType" filterable>
              <el-option
                v-for="item in [{ label: '锁定', value: '1' }, { label: '双禁', value: '2' }, { label: '双重验证', value: '3' }]"
                :key="item.label"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.cancelImgs" label="作废附件：" prop="cancelImgs">
            <image-upload disabled v-if="form.cancelImgs" v-model="form.cancelImgs"
                           :isShowTip="false"/>
          </el-form-item>
        </div>
        <el-table
          :data="tableCode"
          style="width: 100%">
          <el-table-column
            prop="giftCard"
            label="卡密"
            width="180">
          </el-table-column>
          <el-table-column
            prop="faceValue"
            label="面值"
            width="180">
          </el-table-column>
          <el-table-column
            prop="buyPrice"
            label="汇率(价格)">
          </el-table-column>
          <el-table-column
            prop="sourceGroup"
            label="来源群">
          </el-table-column>
          <el-table-column
            prop="pledgeThirtyMinutes"
            label="是否质押30分钟">
            <template slot-scope="{row}">
              {{row.pledgeThirtyMinutes === '0'?'否':'是'}}
            </template>
          </el-table-column>
        </el-table>

        <el-form-item style="margin-top: 20px;float: right" label="" prop="accountPwd">
          <el-button @click="copyToClip(`${form.accountName}----${form.accountPwd}`)">一键复制账号</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-else-if="selectType === '2'">
      <operation-logs></operation-logs>
    </div>
  </div>

</template>
<script>
import {
  oneLevelAccountRechargeDetail,
  topUpYourAccount,
  twoLevelAccountRechargeDetail
} from '@/api/newSystem/accRecharge'
import ca from 'element-ui/src/locale/lang/ca'
import OperationLogs from '@/views/newSystem/accRecharge/operationLogs.vue'

export default {
  name: "depositDetails",
  components: { OperationLogs },
  data() {
    return {
      selectType:'0',
      form:{
        accountName:'',
        accountPwd:'',
        rechargeAmt:'',
        secondaryCharger:'',
        primaryCharger:'',
        idUser:'',
        backReason:'',
      },
      cardCode:'',
      tableCode:[],
    }
  },
  watch:{
  },
  async mounted() {
    if (this.$route.params.acid){
      let res
      if (this.$route.query.chargeStage === '1'){
         res = await oneLevelAccountRechargeDetail(this.$route.params.acid)
      }else {
         res = await twoLevelAccountRechargeDetail(this.$route.params.acid)
      }

      this.form.accountName = res.data.accountName
      this.form.accountPwd = res.data.accountPwd
      this.form.rechargeAmt = res.data.rechargeAmt || 0
      this.form.secondaryCharger = res.data.secondaryCharger
      this.form.primaryCharger = res.data.primaryCharger
      this.form.idUser = res.data.idUser
      this.form.cancelImgs = res.data.cancelImgs
      this.form.cancelReasonType = res.data.cancelReasonType
      this.form.backReason = res.data.backReason
      this.form.acid = this.$route.params.acid
      this.tableCode = res.data.giftCardRechargeRecordList
    }

  },
  methods:{
    /**一键复制账号和密码*/
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    checkCartAmt(){

    }
  }
}
</script>



<style scoped lang="scss">
 ::v-deep .el-upload--picture-card{
display: none;
}
 ::v-deep .el-upload-list__item{
   width: 60px;
   height: 60px;
 }
 ::v-deep .el-upload-list__item-delete{
   display: none !important;
 }
</style>
