package com.juyou.web.controller.tool;

import com.juyou.web.controller.dao.CellImageInfo;
import com.juyou.web.controller.dao.ExcelVo;
import com.juyou.web.controller.dao.HyperlinkInfo;
import com.juyou.web.controller.dao.Relationship;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
public class ExcelUtils {
    //得到所有 对应的图片id
    public static List<HyperlinkInfo> parseHyperlinks(String file) {
        File xmlFile = new File(file);
        List<HyperlinkInfo> hyperlinks = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();

            Document doc = builder.parse(xmlFile);

            NodeList nodeList = doc.getElementsByTagName("hyperlink");
            for (int i = 0; i < nodeList.getLength(); i++) {
                Element element = (Element) nodeList.item(i);
                String ref = element.getAttribute("ref");
                String rid = element.getAttribute("r:id");
                String display = element.getAttribute("display");
                HyperlinkInfo info = new HyperlinkInfo(ref, rid, display);
                hyperlinks.add(info);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hyperlinks;
    }

    //对应的文件路径
    public static List<Relationship> parseRelationshipsFromFile(String file) {
        File xmlFile = new File(file);
        List<Relationship> relationships = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(xmlFile);

            NodeList nodeList = doc.getElementsByTagName("Relationship");
            int count = nodeList.getLength();
            for (int i = 0; i < count; i++) {
                Element element = (Element) nodeList.item(i);
                String id = element.getAttribute("Id");
                String type = element.getAttribute("Type");
                String target = element.getAttribute("Target");
                Relationship info = new Relationship(id, type, target);
                relationships.add(info);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return relationships;
    }

    //图片映射ID
    public static List<CellImageInfo> getCellImageInfos(String filePath) {
        List<CellImageInfo> cellImageInfoList = new ArrayList<>();
        try {
            File inputFile = new File(filePath); // 替换为你的 XML 文件路径
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            dbFactory.setNamespaceAware(true); // 设置为命名空间感知
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(inputFile);
            NodeList cellImageNodes = doc.getElementsByTagName("etc:cellImage");
            for (int i = 0; i < cellImageNodes.getLength(); i++) {
                Node cellImageNode = cellImageNodes.item(i);
                if (cellImageNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element cellImageElement = (Element) cellImageNode;
                    Element picElement = (Element) cellImageElement.getElementsByTagName("xdr:pic").item(0);
                    // 获取 xdr:cNvPr 元素的属性
                    Element cNvPrElement = (Element) picElement.getElementsByTagName("xdr:cNvPr").item(0);
                    String id = cNvPrElement.getAttribute("id");
                    String name = cNvPrElement.getAttribute("name");
                    String descr = cNvPrElement.getAttribute("descr");
                    // 获取 a:blip 元素的 r:embed 属性
                    Element blipElement = (Element) picElement.getElementsByTagName("a:blip").item(0);
                    String embedId = blipElement.getAttributeNS("http://schemas.openxmlformats.org/officeDocument/2006/relationships", "embed");

                    // 创建 CellImageInfo 对象并添加到列表中
                    CellImageInfo cellImageInfo = new CellImageInfo(id, name, descr, embedId);
                    cellImageInfoList.add(cellImageInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cellImageInfoList;
    }

    //解压文件
    public static void unzip2(String zipFilePath, String destDirectory) throws IOException {
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdir();
        }
        ZipInputStream zipIn = null;
        try {
            zipIn = new ZipInputStream(new FileInputStream(zipFilePath));
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        ZipEntry entry = zipIn.getNextEntry();
        // 遍历ZIP文件中的每个条目
        while (entry != null) {
            String filePath = destDirectory + File.separator + entry.getName();
            if (!entry.isDirectory()) {
                // 如果是文件，提取它
                extractFile(zipIn, filePath);
            } else {
                // 如果是目录，创建它
                File dir = new File(filePath);
                dir.mkdir();
            }
            zipIn.closeEntry();
            entry = zipIn.getNextEntry();
        }
        zipIn.close();

    }

    private static void extractFile(ZipInputStream zipIn, String filePath) {
        BufferedOutputStream bos = null;
        try {
            bos = new BufferedOutputStream(new FileOutputStream(filePath));
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        byte[] bytesIn = new byte[4096];
        int read = 0;
        while (true) {
            try {
                if (!((read = zipIn.read(bytesIn)) != -1)) break;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            try {
                bos.write(bytesIn, 0, read);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        try {
            bos.close();

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static void deleteDirectoryWithContents(Path directoryPath) throws IOException {
        Files.walk(directoryPath)
                .sorted(Comparator.reverseOrder())
                .forEach(path -> {
                    try {
                        Files.delete(path);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
    }

    public static void unzip(String zipFilePath, String destDir) throws IOException {
        byte[] buffer = new byte[1024];

        // 创建输出文件夹
        File folder = new File(destDir);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        // 获取zip文件输入流
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            // 从zip文件中提取条目并解压
            ZipEntry zipEntry = zis.getNextEntry();
            while (zipEntry != null) {
                String fileName = destDir + File.separator + zipEntry.getName();
                File newFile = new File(fileName);

                // 创建所有父文件夹
                if (zipEntry.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    new File(newFile.getParent()).mkdirs();

                    // 写入文件内容
                    try (FileOutputStream fos = new FileOutputStream(newFile)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                zipEntry = zis.getNextEntry();
            }

        }
    }

    public static String findID(String id) {
        int start = id.indexOf("\"");
        if (start >= 0) {
            int end = id.indexOf("\"", start + 1);
            if (end >= 0) {
                String extractedData = id.substring(start + 1, end);
                return extractedData;
            }
        }
        return null;
    }

    /*
     * Parameters:
     *  - file:       The file to be uploaded.
     *  - savePath:   The path where the file will be saved.
     *  - List<ExcelVo>: Returns a list containing data for each row of the table.
     */
    public static List<ExcelVo> dispose(MultipartFile file, String savePath,String time) {

        Path path = Paths.get(savePath + "/" + file.getOriginalFilename());
        try {
            Files.createDirectories(path.getParent()); // 检测路径目录是否存在并创建
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            Files.write(path, file.getBytes()); // 保存文件
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        int dotIndex = file.getOriginalFilename().lastIndexOf(".");
        String fileNameWithoutExtension = file.getOriginalFilename().substring(0, dotIndex);
        String zipFileName = savePath + "/" + fileNameWithoutExtension + ".zip";
        File flag = new File(zipFileName);
        if (!flag.exists()) {
            try {
                Files.copy(path, Paths.get(zipFileName));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        try {
            unzip(zipFileName, savePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String xmlString = savePath + "/xl/_rels/cellimages.xml.rels";
        String config = savePath + "/xl/cellimages.xml";
        List<Relationship> relationships = parseRelationshipsFromFile(xmlString);
        List<CellImageInfo> hyperlinks = getCellImageInfos(config);
        List<ExcelVo> excelVos = new ArrayList<>();
        try (FileInputStream excelFile = new FileInputStream(path.toString());
             Workbook workbook = WorkbookFactory.create(excelFile)) {
            Sheet sheet = workbook.getSheetAt(0);

            for (Row row : sheet) {
                ExcelVo excelVo = new ExcelVo();
                for (Cell cell : row) {
                    if (cell.getRowIndex() >= 1) {
                        if (cell != null) {
                            cell.setCellType(CellType.STRING);
                            if (cell.getColumnIndex() == 0) {
                                excelVo.setAccount(cell.getStringCellValue());
                            }
                            if (cell.getColumnIndex() == 1) {
                                excelVo.setPassword(cell.getStringCellValue());
                            }
                            if (cell.getColumnIndex() == 3) {
                                excelVo.setWalletArea(cell.getStringCellValue());
                            }
                            if (cell.getColumnIndex() == 2) {
                                String id = findID(cell.getStringCellValue());
                                if (id != null) {
                                    for (CellImageInfo img : hyperlinks) {
                                        if (id.equals(img.getName())) {
                                            for (Relationship relationship : relationships) {
                                                if (relationship.getId().equals(img.getEmbedId())) {
                                                    excelVo.setPicturePath(time + "/xl/" + relationship.getTarget());
                                                    excelVos.add(excelVo);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return excelVos;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}



