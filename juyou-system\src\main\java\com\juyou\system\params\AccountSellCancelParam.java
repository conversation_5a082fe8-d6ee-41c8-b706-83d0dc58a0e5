package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号出售作废Param
 */
@Data
@ApiModel("AccountSellCancelParam-账号出售作废Param")
public class AccountSellCancelParam implements Serializable {

    private static final long serialVersionUID = 6447736462135952632L;

    @ApiModelProperty("rechargeId:唯一标识")
    private Integer rechargeId;

    @ApiModelProperty("作废原因")
    private String cancelReason;

    @ApiModelProperty("作废图片")
    private String cancelImgs;

    @ApiModelProperty("更新人-前端不用")
    private String updateBy;

}
