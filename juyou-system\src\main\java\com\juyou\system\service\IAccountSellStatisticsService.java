package com.juyou.system.service;

import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.AccountStatisticsCancelVoList;
import com.juyou.system.domain.vo.GiftCardRechargeStatisticsVo;
import com.juyou.system.params.*;

import java.util.List;

/**
 * 账号销售统计service
 *
 * <AUTHOR>
 */
public interface IAccountSellStatisticsService {


    /**
     * 礼品卡充值来源群统计
     * @param param
     * @return
     */
    List<GiftCardRechargeStatisticsVo> getGiftCardRechargeStatisticsList(GiftCardRechargeStatisticsSearchParam param);

    /**
     * 销售账号作废金额明细列表
     * @param param
     * @return
     */
    List<AccountSell> getSellCancelAmtDetailList(SellCancelAmtDetailParam param);

    /**
     * 充值账号作废金额明细列表
     * @param param
     * @return
     */
    List<AccountRecharge> getRechargeCancelAmtDetailList(RechargeCancelAmtDetailSearchParam param);

    /**
     * 出售群统计明细列表
     *
     * @param param
     * @return
     */
    List<AccountSell> getSellGroupDetailList(SellGroupDetailSearchParam param);

    /**
     * 查询作废列表
     *
     * @param param
     * @return
     */
    List<AccountStatisticsCancelVoList> getCancelList(AccountStatisticsCancelParam param);

}
