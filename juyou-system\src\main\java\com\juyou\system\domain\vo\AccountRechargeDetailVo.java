package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号充值报表-详情Vo
 */
@ApiModel("AccountRechargeReportDetailVo-账号充值报表-详情Vo")
@Data
public class AccountRechargeDetailVo extends AccountRecharge implements Serializable {

    private static final long serialVersionUID = 5947104402777169446L;

    @ApiModelProperty("充值礼品卡列表")
    private List<GiftCardRechargeRecord> giftCardRechargeRecordList;

    @ApiModelProperty("一级充值人姓名")
    private String primaryChargerName;

    @ApiModelProperty("二级充值人姓名")
    private String secondaryChargerName;

}
