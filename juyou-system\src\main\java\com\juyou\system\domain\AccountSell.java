package com.juyou.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * sellcard对象 two_account_sell
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("AccountSell")
@TableName("two_account_sell")
public class AccountSell extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId
    @ApiModelProperty("充值ID")
    private Integer rechargeId;

    @ApiModelProperty("录入卡ID")
    @Excel(name = "录入卡ID")
    private Integer acid;

    @ApiModelProperty("账号名称")
    @Excel(name = "账号名称")
    private String accountName;

    @ApiModelProperty("密码")
    @Excel(name = "密码")
    private String accountPwd;

    @ApiModelProperty("账号区域")
    @Excel(name = "账号区域")
    private String accountZone;

    @ApiModelProperty("钱包区域")
    @Excel(name = "钱包区域")
    private String walletArea;

    @ApiModelProperty("idType")
    @Excel(name = "idType")
    private String idType;

    @ApiModelProperty("备用码")
    @Excel(name = "备用码",cellType = Excel.ColumnType.IMAGE)
    private String spareCode;

    @ApiModelProperty("售卡状态：1 未生效 2 待出售 3 已出售 4 作废")
    @Excel(name = "售卡状态：1 未生效 2 待出售 3 已出售 4 作废")
    private String status;

    @ApiModelProperty("核销状态：1 未核对 2 已核对")
    @Excel(name = "核销状态：1 未核对 2 已核对")
    private String writeOffStatus;

    @ApiModelProperty("完成时间分钟数(账号在未生效持续的时间,单位分钟)")
    private Double completedTimeMinutesNum;

    @ApiModelProperty("已充值-卡余额(实际充值金额)")
    @Excel(name = "卡余额")
    private Double cardBalance;

    @ApiModelProperty("收卡单价")
    @Excel(name = "收卡单价")
    private Double buyPrice;

    @ApiModelProperty("卡面值(收卡金额-成本)")
    @Excel(name = "卡面值(收卡金额-成本)")
    private Double buyAmt;

    @ApiModelProperty("应充值金额")
    @Excel(name = "应充值金额")
    private Double shouldAmt;

    @ApiModelProperty("出售单价")
    @Excel(name = "出售单价")
    private Double sellPrice;

    @ApiModelProperty("出售金额")
    @Excel(name = "出售金额")
    private Double sellAmt;

    @ApiModelProperty("出售群")
    @Excel(name = "出售群")
    private String sellChatgroupName;

    @ApiModelProperty("售卖人-客户")
    @Excel(name = "售卖人-客户")
    private String custName;

    @ApiModelProperty("出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出售时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sellTime;

    @ApiModelProperty("作废时间")
    @Excel(name = "作废时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelDate;

    @ApiModelProperty("作废原因")
    @Excel(name = "作废原因")
    private String cancelReason;

    @ApiModelProperty("作废图片")
    private String cancelImgs;

    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出售时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date doneTime;

    @ApiModelProperty("完成人")
    @Excel(name = "完成人")
    private String doneUser;

    @ApiModelProperty("领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    @ApiModelProperty("领取人")
    @Excel(name = "领取人")
    private String receiveUser;

    @ApiModelProperty("领取人id")
    private Long receiveUserId;

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty("置顶状态")
    private String pinnedStatus;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("是否使用")
    private String isUse;

    @ApiModelProperty("使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date useTime;
    @ApiModelProperty("使用时间")
    @TableField(exist = false)
    private  String chargeStage ;

    @ApiModelProperty("是否修改  Y：N")
    @TableField(exist = false)
    private  String hasEdit ;


}
