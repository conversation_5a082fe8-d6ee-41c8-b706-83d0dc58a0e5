package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 账号充值-param
 */
@Data
@ApiModel("AccountRechargeReceiveParam")
public class AccountRechargeReceiveParam implements Serializable {

    @ApiModelProperty("领取数量")
    private Integer count;

    @ApiModelProperty("领取面值")
    private BigDecimal value;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("阶段: 1 一级充值 2 二级充值 3 我的剩余")
    private String chargeStage;

    /* 后端传值 */
    @ApiModelProperty("登录用户id")
    private Long loginUserId;

    @ApiModelProperty("登录用户姓名")
    private String loginUserName;

    @ApiModelProperty("部门")
    private Long deptId;

    @ApiModelProperty("id2-账号充值-一级等待分钟:后端用")
    private Integer onePendingMinutes;

    @ApiModelProperty("id2-账号充值-二级等待分钟:后端用")
    private Integer twoPendingMinutes;
}
