<template>
  <div style="padding: 10px 20px">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="公司" name="first">
        <el-main>
          <div style="display: flex">
            <el-card style="flex: 3" class="box-card" >
              <div slot="header" class="clearfix">
                <span>剩余充值总额</span>
              </div>
              <div v-if="form">
                <el-button v-for="(v,i) in form.cardMoney" :key="i">{{`${v.accountZone} ${v.rechargeAmt}`}}</el-button>
              </div>
            </el-card>
            <el-card style="flex: 1;margin-left: 20px" class="box-card" >
              <div slot="header" class="clearfix">
                <span>剩余成本总额</span>
              </div>
              <div v-if="form" class="sum-amt">
                ¥ {{form.surplusMoney || 0}}
              </div>
            </el-card>
          </div>
          <el-card style="margin-top: 20px" class="box-card" >
            <div slot="header" class="clearfix">
              <span>作废率</span>
            </div>
            <div style="display: flex">
              <div v-if="form" style="margin: 10px 20px">
                <div class="item-title">充值作废率</div>
                <div class="item-value">{{form.rechargeCancelRate ? form.rechargeCancelRate.toFixed(2): 0}}%</div>
              </div>
              <div v-if="form" style="margin: 10px 20px">
                <div class="item-title">出售作废率</div>
                <div class="item-value">{{form.sellCancelRate ? form.sellCancelRate.toFixed(2) : 0}}%</div>
              </div>
              <div v-if="form" style="margin: 10px 20px">
                <div class="item-title">退回率</div>
                <div class="item-value">{{form.bounceRate ? form.bounceRate.toFixed(2) : 0}}%</div>
              </div>
            </div>

          </el-card>
        </el-main>
      </el-tab-pane>
      <el-tab-pane label="部门" name="second">
        <departmentPage></departmentPage>
      </el-tab-pane>
    </el-tabs>

  </div>


</template>
<script>

import { companyPerformanceStatement } from '@/api/newSystem/performanceStatement'
import DepartmentPage from '@/views/newSystem/performanceStatement/departmentPage.vue'

export default {
  name: 'performanceStatement',
  components: { DepartmentPage },
  data(){
    return {
      activeName: 'first',
      form:{

      }
    }
  },
  created() {
    companyPerformanceStatement().then(res=>{
      this.form = res
    })
  },
  methods:{
    handleClick(){
      console.log(this.activeName)
    },
  }
}
</script>

<style scoped lang="scss">
.item-title{
  font-size: 16px;
  color: #9292a4;
}
.item-value{
  margin-top: 4px;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}
.sum-amt{
  margin-top: 10px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
}
</style>
