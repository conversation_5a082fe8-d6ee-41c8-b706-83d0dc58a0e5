<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.TdOrderMapper">
    
    <resultMap type="TdOrder" id="TdOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderTime"    column="order_time"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTdOrderVo">
        select id, order_no, order_time, dept_id, create_by, create_time, update_by, update_time, remark from td_order o
    </sql>

    <select id="selectTdOrderList" parameterType="TdOrder" resultMap="TdOrderResult">
        <include refid="selectTdOrderVo"/>
        <where>
            1=1
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    
    <select id="selectTdOrderById" parameterType="Long" resultMap="TdOrderResult">
        <include refid="selectTdOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTdOrder" parameterType="TdOrder" useGeneratedKeys="true" keyProperty="id">
        insert into td_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTdOrder" parameterType="TdOrder">
        update td_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTdOrderById" parameterType="Long">
        delete from td_order where id = #{id}
    </delete>

    <delete id="deleteTdOrderByIds" parameterType="String">
        delete from td_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>