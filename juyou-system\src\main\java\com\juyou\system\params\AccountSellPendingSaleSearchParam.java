package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号销售-待出售账号查询param
 */
@Data
@ApiModel("AccountSellPendingSaleSearchParam-账号销售-待出售账号查询param")
public class AccountSellPendingSaleSearchParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 954582174891036523L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("idType")
    private String idType;


    @ApiModelProperty("核销状态：1 未核对 2 已核对")
    private String writeOffStatus;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellTime;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellTime;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("礼品卡")
    private String giftCard;
}
