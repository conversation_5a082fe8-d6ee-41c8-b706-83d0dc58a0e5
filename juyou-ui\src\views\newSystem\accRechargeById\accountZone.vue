<template>
  <div class="app-container">
    <div>
<!--      <h2><strong>请选择账户充值国家/地区</strong></h2>-->
<!--      <el-table :data="table" border style="width: 100%">-->
<!--        <el-table-column prop="dictLabel" label="国家/地区" align="center">-->
<!--          <template slot-scope="scope">-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--      </el-table>-->
      <el-button style="width: 150px;height: 50px;font-size: 18px;margin: 20px" type="primary" v-for="item in table" @click="gotoAccount(item.dictLabel)">{{ item.dictValue?'('+item.dictValue+')'+item.dictLabel:item.dictLabel }}</el-button>
    </div>
  </div>
</template>

<script>
import {listAccRechargeZone} from "@/api/newSystem/accRecharge";
import Link from "@/layout/components/Sidebar/Link";

export default {
  name: "accountZone",
  components: {Link},
  data() {
    return {
      table: [],
      zoneList: [],
    }
  },
  created() {
    this.into();
  },
  methods: {
    into() {
      let obj = {
        idType: '1'
      }
      listAccRechargeZone(obj).then(res => {
        this.table = res.rows;
      })
    },
    gotoAccount(accountZone) {
      this.$router.push({path: '/applereg/accRechargeById/'+accountZone})
    }
  }
}
</script>

<style scoped>

</style>
