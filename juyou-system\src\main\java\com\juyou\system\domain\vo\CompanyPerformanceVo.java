package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@Data
public class CompanyPerformanceVo {
    //卡里的钱
    @ApiModelProperty(value = "剩余充值总额")
    List<AccountZoneAmtVo> cardMoney;
    //剩余的钱
    @ApiModelProperty(value = "剩余成本总额")
    BigDecimal surplusMoney;
    //充值作废率
    @ApiModelProperty(value = "充值作废率")
    BigDecimal rechargeCancelRate;
    //出售作废率
    @ApiModelProperty(value = "出售作废率")
    BigDecimal sellCancelRate;

    @ApiModelProperty(value = "退回率")
    BigDecimal bounceRate;

}
