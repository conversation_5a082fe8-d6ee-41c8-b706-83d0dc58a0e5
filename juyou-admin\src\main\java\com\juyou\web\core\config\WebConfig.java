package com.juyou.web.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WebConfig-配置文件上传
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${file.upload.path}")
    private String fileUploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // /files/**为resources下的路径
        // /files/** 映射到本地的路径 file:D:/work/temp/
        // 文件上传到file:D:/work/temp/路径后，可根据ip:port/files/文件名称
        // 例如 http://127.0.0.1:8080/files/logo.png 来进行访问
        registry.addResourceHandler("/showFile/**")
                .addResourceLocations("file:" + fileUploadPath);
    }

}
