package com.juyou.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.domain.entity.SysDept;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.service.ISysDeptService;
import com.juyou.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.enums.BusinessType;
import com.juyou.system.domain.AccountRechargeLog;
import com.juyou.system.service.IAccountRechargeLogService;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(value = "操作日志", tags = "操作日志")
@RestController
@RequestMapping("/system/log")
@Slf4j
public class AccountRechargeLogController extends BaseController
{
    @Autowired
    private IAccountRechargeLogService accountRechargeLogService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysUserService sysUserService;

//    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/listUser")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = sysUserService.getUserList(user);
        return getDataTable(list);
    }


//    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/listDept")
    public AjaxResult list(SysDept dept)
    {
        List<SysDept> depts = sysDeptService.getDeptList(dept);
        return AjaxResult.success(depts);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation("列表")
    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountRechargeLog> list(AccountRechargeLog accountRechargeLog)
    {
        startPage();
//        accountRechargeLog.setCreateUser(getUsername());
        List<AccountRechargeLog> list = accountRechargeLogService.selectAccountRechargeLogList(accountRechargeLog);
        return getDataTable(list);
    }
    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation("导出")
    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountRechargeLog accountRechargeLog)
    {
        List<AccountRechargeLog> list = accountRechargeLogService.selectAccountRechargeLogList(accountRechargeLog);
        ExcelUtil<AccountRechargeLog> util = new ExcelUtil<AccountRechargeLog>(AccountRechargeLog.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation("查询")
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(accountRechargeLogService.selectAccountRechargeLogById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation("新增")
    @PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountRechargeLog accountRechargeLog)
    {
        return toAjax(accountRechargeLogService.insertAccountRechargeLog(accountRechargeLog));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation("修改")
    @PreAuthorize("@ss.hasPermi('system:log:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountRechargeLog accountRechargeLog)
    {
        return toAjax(accountRechargeLogService.updateAccountRechargeLog(accountRechargeLog));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation("删除")
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(accountRechargeLogService.deleteAccountRechargeLogByIds(ids));
    }
}
