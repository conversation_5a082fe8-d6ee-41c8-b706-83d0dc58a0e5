<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.SysRoleUserMapper">

	<resultMap type="SysRoleUser" id="SysRoleUserResult">
		<result property="roleId"     column="role_id"      />
		<result property="userId"     column="user_id"      />
	</resultMap>

	<delete id="deleteRoleUserByRoleId" parameterType="Long">
		delete from sys_user_role where role_id=#{roleId}
	</delete>

	<delete id="deleteRoleUser" parameterType="Long">
 		delete from sys_user_role where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach> 
 	</delete>
	
	<insert id="batchRoleUser">
		insert into sys_user_role(role_id, user_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.userId})
		</foreach>
	</insert>

    <select id="selectUserListByRoleId" resultType="Long">
        select a.user_id from sys_user_role a
        where role_id = #{roleId}
    </select>
	
</mapper> 