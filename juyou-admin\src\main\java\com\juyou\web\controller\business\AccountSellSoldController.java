package com.juyou.web.controller.business;

import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.params.AccountSellSoldListSearchParam;
import com.juyou.system.service.IAccountSellService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 已出售账号-Controller
 */
@Api(value = "已出售账号列表", tags = "已出售账号列表")
@RestController
@RequestMapping("/sellcard/sold")
public class AccountSellSoldController extends BaseController {

    @Autowired
    private IAccountSellService iAccountSellService;

    @ApiOperation("已出售账号列表")
    @Log(title = "已出售账号列表-已出售账号列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:soldList')")
    @GetMapping("/getAccountSellSoldListList")
    public TableDataInfo<AccountSell> getAccountSellSoldList(AccountSellSoldListSearchParam param) {
        startPage();
        List<AccountSell> list = this.iAccountSellService.getAccountSellSoldList(param);
        return getDataTable(list);
    }

    @ApiOperation("修改出售群")
    @Log(title = "已出售账号列表-修改出售群", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:editSellChatgroupName')")
    @PostMapping("/editSellChatgroupName")
    public ResultData<Integer> editSellChatgroupName() {

        return ResultUtil.success("ok");
    }


}
