package com.juyou.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.SysDeptSessionMapper;
import com.juyou.system.domain.SysDeptSession;
import com.juyou.system.service.ISysDeptSessionService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class SysDeptSessionServiceImpl implements ISysDeptSessionService
{
    @Autowired
    private SysDeptSessionMapper sysDeptSessionMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SysDeptSession selectSysDeptSessionById(Long id)
    {
        return sysDeptSessionMapper.selectSysDeptSessionById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SysDeptSession> selectSysDeptSessionList(SysDeptSession sysDeptSession)
    {
        return sysDeptSessionMapper.selectSysDeptSessionList(sysDeptSession);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSysDeptSession(SysDeptSession sysDeptSession)
    {
        return sysDeptSessionMapper.insertSysDeptSession(sysDeptSession);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSysDeptSession(SysDeptSession sysDeptSession)
    {
        return sysDeptSessionMapper.updateSysDeptSession(sysDeptSession);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptSessionByIds(Long[] ids)
    {
        return sysDeptSessionMapper.deleteSysDeptSessionByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptSessionById(Long id)
    {
        return sysDeptSessionMapper.deleteSysDeptSessionById(id);
    }
}
