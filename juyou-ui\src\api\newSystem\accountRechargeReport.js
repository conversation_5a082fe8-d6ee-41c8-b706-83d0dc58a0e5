import request from '@/utils/request'

// 新增sellcard
export function accountRechargeReportList(data) {
  return request({
    url: '/new/system/accountRechargeReport/list',
    method: 'get',
    params: data
  })
}



// 出售作废
export function accountRechargeReportGetDetail(rechargeId) {
  return request({
    url: '/new/system/accountRechargeReport/getDetail/' + rechargeId,
    method: 'post',
    data:{}
  })
}

export function batchWriteOff(data = {}){
  return request({
    url: '/new/system/accountRechargeReport/batchWriteOff',
    method: 'post',
    data: data
  })
}
// 903 账号列表
export function getSellAnchorList(params){
  return request({
    url: '/new/system/sellAnchor/list',
    method: 'get',
    params
  })
}
// 903 账号批量使用
export function sellAnchorBatch(data){
  return request({
    url: '/new/system/sellAnchor/batch',
    method: 'put',
    data
  })
}
// 903 账号恢复
export function sellAnchorRecovery(id){
  return request({
    url:  `/new/system/sellAnchor/recovery/${id}`,
    method: 'put',
  })
}
