package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountSell;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号出售统计明细-vo
 */
@Data
@ApiModel("AccountSellStatisticsDetailVo")
public class AccountSellStatisticsDetailVo extends AccountSell implements Serializable {

    private static final long serialVersionUID = 6616784357673782959L;

    @ApiModelProperty("充值人")
    private String rechargeUsername;

    @ApiModelProperty("出售人")
    private String sellUsername;

}
