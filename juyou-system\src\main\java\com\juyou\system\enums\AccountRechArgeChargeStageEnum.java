package com.juyou.system.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Objects;


@Getter
public enum AccountRechArgeChargeStageEnum {
    LEVEL_1("1", "一级充值"),
    LEVEL_2("2", "二级充值")
    ;

    /**
     * 状态
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    AccountRechArgeChargeStageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccountRechArgeChargeStageEnum getEnum(String code) {
        for (AccountRechArgeChargeStageEnum value : AccountRechArgeChargeStageEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }




}
