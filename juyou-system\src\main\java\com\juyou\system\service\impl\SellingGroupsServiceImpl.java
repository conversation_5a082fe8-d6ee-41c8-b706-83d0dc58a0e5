package com.juyou.system.service.impl;

import java.util.List;
import com.juyou.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.SellingGroupsMapper;
import com.juyou.system.domain.SellingGroups;
import com.juyou.system.service.ISellingGroupsService;

/**
 * 出售群信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Service
public class SellingGroupsServiceImpl implements ISellingGroupsService
{
    @Autowired
    private SellingGroupsMapper sellingGroupsMapper;

    /**
     * 查询出售群信息
     *
     * @param id 出售群信息主键
     * @return 出售群信息
     */
    @Override
    public SellingGroups selectSellingGroupsById(Long id)
    {
        return sellingGroupsMapper.selectSellingGroupsById(id);
    }

    /**
     * 查询出售群信息列表
     *
     * @param sellingGroups 出售群信息
     * @return 出售群信息
     */
    @Override
    public List<SellingGroups> selectSellingGroupsList(SellingGroups sellingGroups)
    {
        return sellingGroupsMapper.selectSellingGroupsList(sellingGroups);
    }

    /**
     * 新增出售群信息
     *
     * @param sellingGroups 出售群信息
     * @return 结果
     */
    @Override
    public int insertSellingGroups(SellingGroups sellingGroups)
    {
        return sellingGroupsMapper.insertSellingGroups(sellingGroups);
    }

    /**
     * 修改出售群信息
     *
     * @param sellingGroups 出售群信息
     * @return 结果
     */
    @Override
    public int updateSellingGroups(SellingGroups sellingGroups)
    {
        sellingGroups.setUpdateTime(DateUtils.getNowDate());
        return sellingGroupsMapper.updateSellingGroups(sellingGroups);
    }

    /**
     * 批量删除出售群信息
     *
     * @param ids 需要删除的出售群信息主键
     * @return 结果
     */
    @Override
    public int deleteSellingGroupsByIds(Long[] ids)
    {
        return sellingGroupsMapper.deleteSellingGroupsByIds(ids);
    }

    /**
     * 删除出售群信息信息
     *
     * @param id 出售群信息主键
     * @return 结果
     */
    @Override
    public int deleteSellingGroupsById(Long id)
    {
        return sellingGroupsMapper.deleteSellingGroupsById(id);
    }
}
