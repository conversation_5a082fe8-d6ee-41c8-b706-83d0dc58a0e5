package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 【请填写功能名称】对象 two_refund_records
 * 
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class TwoRefundRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 退回金额统计表 */
    private Long id;

    /** 退回的ID */
    @Excel(name = "退回的ID")
    @ApiModelProperty(value = "退回的ID")
    private Long acid;

    /** 部门 */
    @Excel(name = "部门")
    @ApiModelProperty(value = "部门")
    private Long deptId;

    /** 领取人id */
    @Excel(name = "领取人id")
    @ApiModelProperty(value = "领取人id")
    private Long receiveUserId;

    /** 领取人名称 */
    @Excel(name = "领取人名称")
    @ApiModelProperty(value = "退回人")
    private String receiveUser;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty(value = "退回金额")
    private Double buyAmt;

    /** 退回原因 */
    @Excel(name = "退回原因")
    @ApiModelProperty(value = "退回原因")
    private String backReason;

    /** 退回原因 */
    @Excel(name = "充值阶段")
    @ApiModelProperty(value = "充值阶段")
    private String chargeStage;


}
