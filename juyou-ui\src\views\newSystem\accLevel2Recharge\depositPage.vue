<template>
  <div style="max-width: 1000px;margin: 20px auto">
    <el-radio-group style="margin-bottom: 20px" v-model="selectType">
      <el-radio-button label="0">充值</el-radio-button>
      <el-radio-button label="1">已充值礼品卡</el-radio-button>
      <el-radio-button label="2">操作日志</el-radio-button>
    </el-radio-group>
    <div v-if="selectType === '0'">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="right" :inline="true">
        <div style="display:flex;justify-content: space-between">
          <el-form-item label="账号：" prop="accountName">
            <el-input v-model="form.accountName" placeholder="请输入账号名称"
                      disabled/>
          </el-form-item>

          <el-form-item label="密码：" prop="accountPwd">
            <el-input v-model="form.accountPwd" placeholder="请输入密码"
                      disabled/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="ID余额：" prop="rechargeAmt">
            <el-input v-model="form.rechargeAmt" disabled placeholder="请输入ID余额"/>
          </el-form-item>

          <el-form-item label="一级充值人：" prop="primaryCharger">
            <el-input disabled v-model="form.primaryChargerName" placeholder=""/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="二级充值人：" prop="secondaryCharger">
            <el-input disabled v-model="form.secondaryChargerName" placeholder=""/>
          </el-form-item>

          <el-form-item label="ID归属业务员：" prop="idUser">
            <el-input v-model="form.idUser" placeholder="请输入ID归属业务员"/>
          </el-form-item>
        </div>
        <div style="display:flex;justify-content: space-between">
          <el-form-item v-if="form.backReason" label="退回原因：" prop="backReason">
            <el-input disabled v-model="form.backReason" placeholder="请输入退回原因"/>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="卡密代码：" prop="accountPwd">
            <el-input @change="inputCode" style="width: 500px" type="textarea" rows="5" v-model="cardCode" placeholder="请输入卡密代码"/>
            <div style="font-size: 12px;color: red">格式：卡密 面值*汇率 #群号</div>
          </el-form-item>
        </div>
        <el-table
          :data="tableCode"
          style="width: 100%">
          <el-table-column
            prop="giftCard"
            label="卡密"
            width="180">
            <template slot-scope="{row}">
              <el-input v-model="row.giftCard"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="faceValue"
            label="面值"
            width="180">
            <template slot-scope="{row}">
              <el-input v-model="row.faceValue"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="buyPrice"
            label="汇率(价格)">
            <template slot-scope="{row}">
              <el-input v-model="row.buyPrice"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="sourceGroup"
            label="来源群">
            <template slot-scope="{row}">
              <el-select v-model="row.sourceGroup" filterable placeholder="请选择来源群">
                <el-option
                  v-for="item in sourceGroupList"
                  :key="item.groupNumber"
                  :label="item.groupNumber"
                  :value="item.groupNumber">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="pledgeThirtyMinutes"
            label="是否质押30分钟">
            <template slot-scope="{row}">
              <el-checkbox true-label="1" false-label="0" v-model="row.pledgeThirtyMinutes"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <div>
          <el-form-item style="margin-top: 20px;display: flex;justify-content: flex-end" label="" prop="accountPwd">
            <el-button style="margin-right: 100px" type="primary" @click="addItem">添加一行</el-button>
          </el-form-item>
        </div>
        <el-form-item style="margin-top: 20px;float: right" label="" prop="accountPwd">
          <el-button style="margin-right: 100px" type="primary" @click="recharge('3')">完成充值</el-button>
          <el-button type="warning" @click="recharge('2')">部分充值</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="copyToClip(`${form.accountName}----${form.accountPwd}`)">一键复制账号</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-else-if="selectType === '1'">
      <el-form ref="form" :model="form" label-width="120px" label-position="right" :inline="true">
        <div style="display:flex;justify-content: space-between">
          <el-form-item label="账号：" prop="accountName">
            <el-input v-model="form.accountName" placeholder="请输入账号名称"
                      disabled/>
          </el-form-item>

          <el-form-item label="密码：" prop="accountPwd">
            <el-input v-model="form.accountPwd" placeholder="请输入密码"
                      disabled/>
          </el-form-item>

        </div>
      </el-form>
      <el-card shadow="never" header="该账号已充值的礼品卡">
        <div slot="header" class="clearfix">
          <span style="font-size: 20px;font-weight: 600">该账号已充值的礼品卡</span>

        </div>
        <el-table
          :data="cardList"
          style="width: 100%">
          <el-table-column
            prop="giftCard"
            label="礼品卡"
            width="180">
          </el-table-column>
          <el-table-column
            prop="faceValue"
            label="面值"
            width="180">
          </el-table-column>
          <el-table-column
            prop="buyPrice"
            label="价格">
          </el-table-column>
          <el-table-column
            prop="sourceGroup"
            label="来源群">
          </el-table-column>
          <el-table-column
            prop="updateBy"
            label="充值人">
          </el-table-column>
          <el-table-column
            prop="updateTime"
            label="充值时间">
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <div v-else-if="selectType === '2'">
      <operation-logs></operation-logs>
    </div>
  </div>

</template>
<script>
import {
  oneLevelAccountRechargeDetail,
  rechargeTheSecondaryAccount,
  twoLevelAccountRechargeDetail
} from '@/api/newSystem/accRecharge'
import ca from 'element-ui/src/locale/lang/ca'
import OperationLogs from '@/views/newSystem/accRecharge/operationLogs.vue'
import { getSourceGroupsList } from '@/api/newSystem/notEffective'

export default {
  name: "secondaryTopUp",
  components: { OperationLogs },
  data() {
    return {
      sourceGroupList:[],
      selectType:'0',
      form:{
        accountName:'',
        accountPwd:'',
        rechargeAmt:'',
        idUser:'',
        secondaryCharger:'',
        primaryCharger:'',
        acid:'',
      },
      cardCode:'',
      tableCode:[],
      cardList:[],
      rules: {
        idUser: [
          { required: true, message: '请输入ID归属业务员', trigger: 'blur' },
        ],
      }
    }
  },
  watch:{
    tableCode: {
      handler: function(val, oldVal){
        if (val.length){
          this.cardCode = val.map(e=>{
            return `${e.giftCard} ${e.faceValue}*${e.buyPrice} #${e.sourceGroup}`
          }).join('\n')
          let sum  = val.reduce((a,b)=>a+ +b.faceValue,+this.form.orignRechargeAmt)
          if (isNaN(sum)){
            this.$modal.msgError('面值必须为数字')
            return
          }
          this.form.rechargeAmt = sum
        }
      },
      deep:true,
      immediate: true
    },
  },
  async created() {
    if (this.$route.params.acid){
      const res = await twoLevelAccountRechargeDetail(this.$route.params.acid)
      this.form.accountName = res.data.accountName
      this.form.accountPwd = res.data.accountPwd
      this.form.rechargeAmt = res.data.rechargeAmt || 0
      this.form.orignRechargeAmt = res.data.rechargeAmt || 0
      this.form.secondaryChargerName = res.data.secondaryChargerName
      this.form.primaryChargerName = res.data.primaryChargerName
      this.form.acid = this.$route.params.acid
      this.form.backReason = res.data.backReason
      this.form.idUser = res.data.idUser
      this.cardList = res.data.giftCardRechargeRecordList
    }
    const res = await getSourceGroupsList();
    this.sourceGroupList = res.rows

  },
  methods:{
    addItem(){
      this.tableCode.push({
        giftCard:'',
        faceValue:0,
        buyPrice:0,
        sourceGroup:'',
      })
    },
    resetForm(){
      this.cardCode = ''
      this.tableCode = []
    },
    /**一键复制账号和密码*/
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    recharge(e){
      this.$refs["form"].validate(async valid => {
        if (valid) {
          let flag = null
          if (this.tableCode.length){
            this.tableCode.forEach(e=>{
              if (isNaN(e.faceValue)){
                flag = '面值必须为数字'
                return
              }
              if (isNaN(e.buyPrice)){
                flag = '汇率必须为数字'
                return
              }
              if (!e.sourceGroup){
                flag = '来源群不能为空'
                return
              }
              e.pledgeThirtyMinutes = e.pledgeThirtyMinutes === '1'? '1':'0'

            })
          }else {
            flag = '请输入正确的卡密代码'
          }

          if (flag){
            this.$modal.msgError(flag)
            return
          }
          this.tableCode.forEach(e=>{
            e.pledgeThirtyMinutes = e.pledgeThirtyMinutes === '1'? '1':'0'
          })
          let form = {
            ...this.form,
            cardList:this.tableCode,
            status:e
          }
          const res = await rechargeTheSecondaryAccount(form)
          this.$modal.msgSuccess('充值成功')
          this.$router.go(-1)
          this.$tab.closePage()
        }
      })

    },
    inputCode(e){
      let inputArr = e.split('\n').filter(e=>e)
      try {
        let arr = inputArr.map(item=>{
          //匹配卡密和面值
          const regex = /^[^*]*/; // 匹配从开始到第一个*之前的所有字符
          let codeAndValue = item.match(regex)[0];
          let codeAndValueArray =  codeAndValue.split(' ')
          //匹配汇率
          const regex1 = /\*(\S+) #/; // 匹配 * 和 # 之间的任何字符
          let rate = item.match(regex1)[0];
          rate = rate.replace(new RegExp(' #', 'g'), '')
          rate = rate.replace(/\*/g, '')
          //匹配群号
          const regex2 = /#(.*)/; // 匹配 # 之后的任何字符
          let groupNum = item.match(regex2)[0]
          groupNum = groupNum.replace(new RegExp('#', 'g'), '')
          return {
            giftCard:codeAndValueArray[0],
            faceValue:codeAndValueArray[1],
            buyPrice:rate,
            sourceGroup:groupNum,
          }
        })
        this.tableCode = arr
      }catch (e){
        this.$modal.msgError('卡密代码格式错误')
        return
      }
      this.tableCode.forEach(e=>{
        let index = this.sourceGroupList.findIndex(index=>index.groupNumber == e.sourceGroup)
        console.log(index)
        if (index===-1){
          e.sourceGroup = ''
        }
      })
    },
    checkCartAmt(){

    }
  }
}
</script>



<style scoped lang="scss">

</style>
