package com.juyou.system.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 账号充值status枚举
 * 卡状态：0 未领取 1 待充值  2 部分充值 3 完成充值 4 作废
 */
@Getter
public enum AccountRechargeEnum {
    STATUS_0("0", "未领取"),
    STATUS_1("1", "待充值"),
    STATUS_2("2", "部分充值"),
    STATUS_3("3", "完成充值"),
    STATUS_4("4", "作废"),
    ;

    /**
     * 状态
     */
    private String code;

    /**
     * 描述
     */
    private String desc;


    AccountRechargeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccountRechargeEnum getEnum(String code) {
        for (AccountRechargeEnum value : AccountRechargeEnum.values()) {
            if (StrUtil.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }




}
