<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.AccountRechargeMapper">

    <resultMap type="AccountRecharge" id="AccountRechargeResult">
        <result property="acid" column="acid"/>
        <result property="accountName" column="account_name"/>
        <result property="accountPwd" column="account_pwd"/>
        <result property="accountZone" column="account_zone"/>
        <result property="walletArea" column="wallet_area"/>
        <result property="idType" column="id_type"/>
        <result property="spareCode" column="spare_code"/>
        <result property="status" column="status"/>
        <result property="buyPrice" column="buy_price"/>
        <result property="buyAmt" column="buy_amt"/>
        <result property="shouldAmt" column="should_amt"/>
        <result property="rechargeAmt" column="recharge_amt"/>
        <result property="cancelReasonType" column="cancel_reason_type"/>
        <result property="doubleProhibitedBalance" column="double_prohibited_balance"/>
        <result property="cancelImgs" column="cancel_imgs"/>
        <result property="cancelDate" column="cancel_date"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="doneTime" column="done_time"/>
        <result property="doneUser" column="done_user"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="receiveUser" column="receive_user"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="writeOffStatus" column="write_off_status"/>
        <result property="chargeStage" column="charge_stage"/>
        <result property="primaryCharger" column="primary_charger"/>
        <result property="secondaryCharger" column="secondary_charger"/>
        <result property="backReason" column="back_reason"/>
        <result property="idUser" column="id_user"/>
        <result property="hasEdit" column="has_edit"/>
        <result property="hasBack" column="has_back"/>
        <result property="comment" column="comment"/>
        <result property="pendingStartDate" column="pending_start_date"/>
    </resultMap>

    <sql id="selectAccountRechargeVo">
        select ar.acid,
               ar.account_name,
               ar.account_pwd,
               ar.account_zone,
               ar.wallet_area,
               ar.id_type,
               ar.spare_code,
               ar.STATUS,
               ar.buy_price,
               ar.buy_amt,
               ar.should_amt,
               ar.recharge_amt,
               ar.cancel_reason_type,
               ar.double_prohibited_balance,
               ar.cancel_imgs,
               ar.cancel_date,
               ar.create_time,
               ar.create_user,
               ar.update_time,
               ar.update_user,
               ar.done_time,
               ar.done_user,
               ar.receive_time,
               ar.receive_user,
               ar.receive_user_id,
               ar.dept_id,
               ar.write_off_status,
               ar.charge_stage,
               ar.primary_charger,
               ar.secondary_charger,
               ar.back_reason,
               ar.id_user,
               ar.has_edit,
               ar.has_back,
               ar.comment,
               ar.pending_start_date
        from two_account_recharge ar
    </sql>

    <sql id="selectAccountRechargeListVo">
        select ar.acid                      as acid,
               ar.account_name              as accountName,
               ar.account_pwd               as accountPwd,
               ar.account_zone              as accountZone,
               ar.wallet_area               as walletArea,
               ar.STATUS                    as status,
               ar.buy_price                 as buyPrice,
               ar.buy_amt                   as buyAmt,
               ar.should_amt                as shouldAmt,
               ar.recharge_amt              as rechargeAmt,
               ar.cancel_reason_type        as cancelReasonType,
               ar.double_prohibited_balance as doubleProhibitedBalance,
               ar.cancel_imgs               as cancelImgs,
               ar.cancel_date               as cancelDate,
               ar.create_time               as createTime,
               ar.create_user               as createUser,
               ar.update_time               as updateTime,
               ar.update_user               as updateUser,
               ar.done_time                 as doneTime,
               ar.done_user                 as doneUser,
               ar.receive_time              as receiveTime,
               ar.receive_user              as receiveUser,
               ar.receive_user_id           as receiveUserId,
               ar.dept_id                   as deptId
        from two_account_recharge ar
    </sql>

    <select id="selectAccountRechargeByAccountName" parameterType="String" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        where
        account_name = #{accountName}
    </select>


    <select id="selectAccountRechargeList" parameterType="AccountRecharge" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        <where>
            <if test="accountName != null  and accountName != ''">and account_name like concat('%', #{accountName},
                '%')
            </if>
            <if test="accountPwd != null  and accountPwd != ''">and account_pwd = #{accountPwd}</if>
            <if test="accountZone != null  and accountZone != ''">and account_zone = #{accountZone}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="status == null  or status == ''">and status in(1,2) and ( create_user is null or create_user =
                #{createUser} )
            </if>
            <if test="buyPrice != null ">and buy_price = #{buyPrice}</if>
            <if test="buyAmt != null ">and buy_amt = #{buyAmt}</if>
            <if test="rechargeAmt != null ">and recharge_amt = #{rechargeAmt}</if>
            <!--<if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>-->
            <if test="updateUser != null  and updateUser != ''">and update_user = #{updateUser}</if>
            <if test="doneTime != null ">and done_time = #{doneTime}</if>
            <if test="doneUser != null  and doneUser != ''">and done_user = #{doneUser}</if>
        </where>
    </select>

    <select id="selectAccountRechargeByAcid" parameterType="Integer" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        where acid = #{acid}
    </select>

    <select id="getOne" parameterType="java.lang.String" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        <where>
            and account_name = #{accountName}
        </where>
    </select>

    <select id="selectTopByStatus" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        where `status` = #{status}
        and ar.account_zone = #{accountZone}
        and ar.id_type = #{idType}
        LIMIT 0,#{count}
    </select>

    <select id="selectTopByStatusByWalletArea" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        where `status` = #{status}
        and ar.account_zone = #{accountZone}
        and ar.id_type = #{idType}
        and ar.wallet_area = #{walletArea}
        LIMIT 0,#{count}
    </select>
    <select id="selectAccountRechargeReportPage"
            parameterType="com.juyou.system.params.AccountRechargePageParam" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        LEFT JOIN sys_user u ON u.user_id = ar.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <where>
            <if test=" param.accountName != null and param.accountName != '' ">and ar.account_name LIKE
                CONCAT('%',#{param.accountName},'%')
            </if>
            <if test=" param.status != null and param.status != '' ">and ar.`status` = #{param.status}</if>
            <if test=" param.accountZone != null and param.accountZone != '' ">and ar.account_zone like
                CONCAT('%',#{param.accountZone},'%')
            </if>
            <if test=" param.idType != null and param.idType != '' ">and ar.`id_type` = #{param.idType}</if>
            <if test=" param.startReceiveTime != null and param.endReceiveTime != null ">and ar.receive_time BETWEEN
                #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test=" param.startDoneTime != null and param.endDoneTime != null ">and ar.done_time BETWEEN
                #{param.startDoneTime} and #{param.endDoneTime}
            </if>
            <if test=" param.operator != null and param.operator != ''">and ar.receive_user LIKE
                CONCAT('%',#{param.operator},'%')
            </if>
            <if test=" param.writeOffStatus != null and param.writeOffStatus != '' ">and ar.write_off_status =
                #{param.writeOffStatus}
            </if>
        </where>
        ORDER BY
        (
        case ar.`status`
        WHEN 0 THEN 0
        WHEN 1 THEN 1
        WHEN 2 THEN 2
        WHEN 0 THEN 3
        WHEN 3 THEN 4
        WHEN 4 THEN 5
        ELSE 0
        END
        ) ASC,ar.update_time DESC
    </select>

    <select id="selectAccountRechargePage"
            parameterType="com.juyou.system.params.AccountRechargePageParam" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        LEFT JOIN sys_user u ON u.user_id = ar.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <if test="param.giftCard != null and param.giftCard != ''">
            inner join ( select gcrr.account FROM two_gift_card_recharge_record gcrr WHERE gcrr.gift_card LIKE
            CONCAT('%',#{param.giftCard},'%') GROUP BY gcrr.account ) as gcrr on gcrr.account = ar.account_name
        </if>
        <where>
            ${param.params.dataScope}
            <if test=" param.accountName != null and param.accountName != '' ">and ar.account_name LIKE
                CONCAT('%',#{param.accountName},'%')
            </if>
            <if test=" param.status == null or param.status == ''">and ar.`status` != 0 and ar.`status` in (1,2)</if>
            <if test=" param.status != null and param.status != '' ">and ar.`status` = #{param.status}</if>
            <if test=" param.accountZone != null and param.accountZone != '' ">and ar.account_zone =
                #{param.accountZone}
            </if>
            <!--            <if test=" param.idType != null and param.idType != '' "> and ar.id_type = #{param.idType} </if>-->
            <if test=" param.startReceiveTime != null and param.endReceiveTime != null ">and ar.receive_time BETWEEN
                #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test=" param.startDoneTime != null and param.endDoneTime != null ">and ar.done_time BETWEEN
                #{param.startDoneTime} and #{param.endDoneTime}
            </if>
        </where>
        ORDER BY
        (
        case ar.`status`
        WHEN 1 THEN 1
        WHEN 2 THEN 2
        WHEN 0 THEN 3
        WHEN 3 THEN 4
        WHEN 4 THEN 5
        ELSE 0
        END
        ) ASC,ar.update_time DESC
    </select>

    <select id="selectByReceiveUserAndStatus" resultMap="AccountRechargeResult">
        <include refid="selectAccountRechargeVo"/>
        <where>
            ar.receive_user_id = #{receiveUserId}
            and ar.`status` in
            <foreach item="item" collection="statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getTodayRechargeAmount" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(sum( a.face_value ),0)
        FROM
        two_gift_card_recharge_record a
        LEFT JOIN two_account_recharge ar ON ar.account_name = a.account
        LEFT JOIN sys_user su ON su.user_name = a.create_by
        WHERE
        su.user_id = #{userId}
        AND ar.STATUS IN ( 2, 3 )
        AND ar.account_zone =#{accountZone}
        <if test="idType != null and idType != ''">
            and ar.id_type = #{idType}
        </if>
        and a.execution_time BETWEEN #{startDate} and #{endDate}

        <!--        SELECT sum(face_value)-->
        <!--        FROM two_gift_card_recharge_record gcrr-->
        <!--        LEFT JOIN two_account_recharge ar on ar.account_name= gcrr.account-->
        <!--        where-->
        <!--            1=1-->
        <!--            and ar.receive_user_id = #{userId}-->
        <!--            and ar.account_zone = #{accountZone}-->
        <!--            and ar.status in (2,3)-->
        <!--            <if test="idType != null and idType != ''">-->
        <!--                and ar.id_type = #{idType}-->
        <!--            </if>-->
        <!--            and ar.done_time BETWEEN #{startDate} and #{endDate}-->

    </select>

    <select id="countByReceiveUserIdAndStatusList" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM `two_account_recharge` ar
        where ar.receive_user_id = #{receiveUserId}
        and ar.`status` in
        <foreach item="item" collection="statusList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="countByStatus" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT COUNT(*)
        FROM two_account_recharge ar
        where ar.`status` = #{status}
          and ar.account_zone = #{accountZone}
          and ar.id_type = #{idType}
    </select>
    <select id="findPerformanceList" resultType="com.juyou.system.domain.vo.PerformanceVoList"
            parameterType="com.juyou.system.params.PerformanceSearchParam">
        select
        MAX(su.nick_name) as nickName,
        ar.done_user account,
        DATE(ar.done_time) as workDate,
        COUNT(1) as rechargeCount,
        SUM(ar.recharge_amt) as rechargeAmt
        FROM two_account_recharge ar
        LEFT JOIN sys_user su on su.user_name = ar.done_user
        WHERE ar.done_user IS NOT NULL
        <if test="param.userName != null and param.userName != ''">
            AND (
            su.user_name LIKE CONCAT('%',#{param.userName},'%')
            OR
            su.nick_name LIKE CONCAT('%',#{param.userName},'%')
            )
        </if>
        <if test="param.startDate != null and param.endDate != null">
            AND DATE(ar.done_time) BETWEEN #{param.startDate} AND #{param.endDate}
        </if>
        GROUP BY ar.done_user, DATE(ar.done_time)
        ORDER BY DATE(ar.done_time) DESC
    </select>


    <insert id="insertAccountRecharge" parameterType="AccountRecharge" useGeneratedKeys="true" keyProperty="acid">
        insert into two_account_recharge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountName != null">account_name,</if>
            <if test="accountPwd != null">account_pwd,</if>
            <if test="accountZone != null">account_zone,</if>
            <if test="walletArea != null">wallet_area,</if>
            <if test="idType != null">id_type,</if>
            <if test="spareCode != null">spare_code,</if>
            <if test="status != null">status,</if>
            <if test="buyPrice != null">buy_price,</if>
            <if test="buyAmt != null">buy_amt,</if>
            <if test="shouldAmt != null">should_amt,</if>
            <if test="rechargeAmt != null">recharge_amt,</if>
            <if test="cancelReasonType != null">cancel_reason_type,</if>
            <if test="doubleProhibitedBalance != null">double_prohibited_balance,</if>
            <if test="cancelImgs != null">cancel_imgs,</if>
            <if test="cancelDate != null">cancel_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="doneTime != null">done_time,</if>
            <if test="doneUser != null">done_user,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="receiveUser != null">receive_user,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountName != null">#{accountName},</if>
            <if test="accountPwd != null">#{accountPwd},</if>
            <if test="accountZone != null">#{accountZone},</if>
            <if test="walletArea != null">#{walletArea},</if>
            <if test="idType != null">#{idType},</if>
            <if test="spareCode != null">#{spareCode},</if>
            <if test="status != null">#{status},</if>
            <if test="buyPrice != null">#{buyPrice},</if>
            <if test="buyAmt != null">#{buyAmt},</if>
            <if test="shouldAmt != null">#{shouldAmt},</if>
            <if test="rechargeAmt != null">#{rechargeAmt},</if>
            <if test="cancelReasonType != null">#{cancelReasonType},</if>
            <if test="doubleProhibitedBalance != null">#{doubleProhibitedBalance},</if>
            <if test="cancelImgs != null">#{cancelImgs},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="doneTime != null">#{doneTime},</if>
            <if test="doneUser != null">#{doneUser},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="receiveUser != null">#{receiveUser},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>


    <update id="updateAccountRecharge" parameterType="AccountRecharge">
        update two_account_recharge
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="accountPwd != null">account_pwd = #{accountPwd},</if>
            <if test="accountZone != null">account_zone = #{accountZone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="writeOffStatus != null">write_off_status = #{writeOffStatus},</if>
            <if test="buyPrice != null">buy_price = #{buyPrice},</if>
            <if test="buyAmt != null">buy_amt = #{buyAmt},</if>
            <if test="shouldAmt != null">should_amt = #{shouldAmt},</if>
            <if test="rechargeAmt != null">recharge_amt = #{rechargeAmt},</if>
            <if test="cancelReasonType != null">cancel_reason_type = #{cancelReasonType},</if>
            <if test="doubleProhibitedBalance != null">double_prohibited_balance = #{doubleProhibitedBalance},</if>
            <if test="cancelImgs != null">cancel_imgs = #{cancelImgs},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="doneTime != null">done_time = #{doneTime},</if>
            <if test="doneUser != null">done_user = #{doneUser},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="receiveUser != null">receive_user = #{receiveUser},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="idUser != null">id_user = #{idUser},</if>
            <if test="primaryCharger != null">primary_charger = #{primaryCharger},</if>
            <if test="secondaryCharger != null">secondary_charger = #{secondaryCharger},</if>
            <if test="pendingStartDate != null">pending_start_date = #{pendingStartDate},</if>
        </trim>
        where acid = #{acid}
    </update>
    <update id="updateAccountRechargeStatus">
        update two_account_recharge
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
        </trim>
        where acid = #{acid}
    </update>
    <update id="batchUpdateAccountRechargeStatus">
        update two_account_recharge
        <trim prefix="SET" suffixOverrides=",">
            status = #{status},
            receive_user = #{receiveUser},
            receive_time = #{receiveTime},
            receive_user_id = #{receiveUserId},
            primary_charger = #{receiveUserId},
            dept_id = #{deptId},
        </trim>
        where acid in
        <foreach item="item" collection="acids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdateAccountRechargeStatusTwo">
        update two_account_recharge
        <trim prefix="SET" suffixOverrides=",">
            status = #{status},
            receive_user = #{receiveUser},
            receive_time = #{receiveTime},
            receive_user_id = #{receiveUserId},
            secondary_charger = #{receiveUserId},
            dept_id = #{deptId},
            charge_stage = 2,
        </trim>
        where acid in
        <foreach item="item" collection="acids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdateReceiveUserByIds">
        update two_account_recharge
        set receive_user = #{receiveUser},receive_user_id = #{receiveUserId}
        where acid in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteAccountRechargeByAcid" parameterType="Integer">
        delete
        from two_account_recharge
        where acid = #{acid}
    </delete>

    <delete id="deleteAccountRechargeByAcids" parameterType="String">
        delete from two_account_recharge where acid in
        <foreach item="acid" collection="array" open="(" separator="," close=")">
            #{acid}
        </foreach>
    </delete>

    <select id="selectAccountRechargeVoList" resultType="accountRechargeVo">
        SELECT u.account_zone as country, count(u.acid) as quantity
        FROM `two_account_recharge` u
                 left join sys_dept d on u.dept_id = d.dept_id
        WHERE u.`status` in (1, 2)
          and u.id_type = #{param.idType}
--         ${param.params.dataScope}
        GROUP BY u.account_zone
    </select>

    <select id="findOneLevelResidualAccountList"
            resultType="com.juyou.system.domain.vo.AccountSellResidualAccountVoList">
        SELECT
        IFNULL(ar.recharge_amt,0) as cardBalance,
        COUNT(1) as count,
        '1' as chargeStage
        FROM two_account_recharge ar
        WHERE
        1 = 1
        AND ar.`status` = '0'
        AND ar.charge_stage = 1
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        GROUP BY cardBalance
        ORDER BY cardBalance ASC
    </select>

    <select id="findTwoLevelResidualAccountList"
            resultType="com.juyou.system.domain.vo.AccountSellResidualAccountVoList">
        SELECT
        IFNULL(ar.recharge_amt,0) as cardBalance,
        COUNT(1) as count,
        '2' as chargeStage
        FROM two_account_recharge ar
        WHERE
        1 = 1
        AND ar.`status` = '0'
        AND ar.charge_stage = 2
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        AND ar.receive_user_id is NULL
        AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) >= #{onePendingMinutes}
        GROUP BY cardBalance
        ORDER BY cardBalance ASC
    </select>
    <select id="findMyResidualAccountList"
            resultType="com.juyou.system.domain.vo.AccountSellResidualAccountVoList">
        SELECT
        IFNULL(ar.recharge_amt,0) as cardBalance,
        COUNT(1) as count,
        '3' as chargeStage
        FROM two_account_recharge ar
        LEFT JOIN two_account_sell s on s.acid = ar.acid
        WHERE
        1 = 1
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        <if test="loginUserId != null">
            AND ar.receive_user_id = #{loginUserId}
        </if>
        AND ar.`status` = '3'
        AND ar.charge_stage = 2
        AND s.receive_user_id is NULL
        AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) >= #{twoPendingMinutes}
        GROUP BY cardBalance
        ORDER BY cardBalance ASC
    </select>
    <select id="getMyRechargeCancelRate" resultType="java.math.BigDecimal">
        SELECT (SELECT IFNULL(SUM(IF(ar.`status` = '4', IFNULL(ar.recharge_amt, 0), 0)),0)
                FROM two_account_recharge ar
                WHERE 1 = 1
                  AND ar.receive_user_id = #{userId}
                  AND ar.cancel_date BETWEEN #{startDate} and #{endDate})
                   /
               (SELECT   SUM(IFNULL(ar.recharge_amt, 1))
                FROM two_account_recharge ar
                WHERE 1 = 1
                  AND ar.receive_user_id = #{userId}
                  AND (ar.cancel_date BETWEEN #{startDate} and #{endDate}
                    OR ar.done_time BETWEEN #{startDate} and #{endDate})) * 100
    </select>

    <select id="selectOneLevelAccount" resultType="com.juyou.system.domain.AccountRecharge">
        SELECT *
        FROM two_account_recharge ar
        WHERE
        1 = 1
        AND ar.`status` = '0'
        AND ar.charge_stage = 1
        AND ar.account_zone = #{accountZone}
        <choose>
            <when test="amt != null and amt != 0">
                AND ar.recharge_amt = #{amt}
            </when>
            <otherwise>
                AND ( ar.recharge_amt is null or ar.recharge_amt = 0 )
            </otherwise>
        </choose>
        ORDER BY ar.create_time asc
        LIMIT 0,#{count}
    </select>

    <select id="selectTwoLevelAccount" resultType="com.juyou.system.domain.AccountRecharge">
        SELECT *
        FROM two_account_recharge ar
        WHERE 1 = 1
          AND ar.`status` = '0'
          AND ar.charge_stage = 2
          AND ar.account_zone = #{accountZone}
          AND ar.receive_user_id is NULL
          AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date, NOW()) >= #{pendingMinutes}
          AND (ar.recharge_amt = #{amt} or ar.recharge_amt is null)
          ORDER BY ar.create_time asc
          LIMIT 0,#{count}
    </select>

    <select id="selectMyLevelAccount" resultType="com.juyou.system.domain.AccountRecharge">
        SELECT ar.*
        FROM two_account_recharge ar
                 LEFT JOIN two_account_sell s on s.acid = ar.acid
        WHERE 1 = 1
          AND ar.account_zone = #{accountZone}
          AND ar.receive_user_id = #{userId}
          AND ar.`status` = '3'
          AND ar.charge_stage = 2
          AND s.receive_user_id is NULL
          AND ar.recharge_amt = #{amt}
          AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date, NOW()) >= #{pendingMinutes} LIMIT 0,#{count}
    </select>

    <select id="chargingInquiryList" resultType="com.juyou.system.domain.vo.ChargingInquiryListVo">
        SELECT
        (#{accountSellPendingMinutes}-TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW())) as surplusWaitDuration,
        ar.*,
        u1.user_name as primaryChargerName,
        u2.user_name as secondaryChargerName
        FROM
        two_account_recharge ar
        LEFT JOIN two_account_sell ase ON ase.acid = ar.acid
        LEFT JOIN sys_user u1 on u1.user_id = ar.primary_charger
        LEFT JOIN sys_user u2 on u2.user_id = ar.secondary_charger
        WHERE
        1 = 1
        <if test="accountName != null and accountName != ''">
            AND ar.account_name LIKE CONCAT('%',#{accountName},'%')
        </if>
        <if test="status != null and status != ''">
            AND ar.`status` = #{status}
        </if>
        <if test="chargeStage != null and chargeStage != ''">
            AND ar.charge_stage = #{chargeStage}
        </if>
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        <if test="doneTimeStart != null and doneTimeEnd != null">
            AND ar.done_time BETWEEN #{doneTimeStart} AND #{doneTimeEnd}
        </if>
        <if test="writeOffStatus != null and writeOffStatus != ''">
            AND ar.write_off_status = #{writeOffStatus}
        </if>
        <if test="primaryCharger != null">
            AND ar.primary_charger = #{primaryCharger}
        </if>
        <if test="secondaryCharger != null">
            AND ar.secondary_charger = #{secondaryCharger}
        </if>
        <if test="idUser != null and idUser != ''">
            AND ar.id_user LIKE CONCAT('%',#{idUser},'%')
        </if>
        <if test="surplusWaitDurationStart != null and surplusWaitDurationEnd != null">
            AND (#{accountSellPendingMinutes}-IFNULL(ase.completed_time_minutes_num,0)) BETWEEN
            #{surplusWaitDurationStart} AND #{surplusWaitDurationEnd}
        </if>
    </select>
    <select id="getlevel1UserList" resultType="com.juyou.system.domain.vo.KeyValueVo">
        SELECT su.user_id as value,
               su.nick_name as label
        FROM sys_user su
            LEFT JOIN sys_user_role sr
        on sr.user_id= su.user_id
        WHERE
            sr.role_id=105

    </select>
    <select id="getlevel2UserList" resultType="com.juyou.system.domain.vo.KeyValueVo">

        SELECT su.user_id as value,
               su.nick_name as label
        FROM sys_user su
            LEFT JOIN sys_user_role sr
        on sr.user_id= su.user_id
        WHERE
            sr.role_id=106

    </select>
    <select id="pendingMinutesPutTwoChargeStageList" resultType="com.juyou.system.domain.AccountRecharge">
        SELECT ar.*
        FROM two_account_recharge ar
                 LEFT JOIN two_account_sell ase on ase.account_name = ar.account_name
        WHERE 1 = 1
          AND ar.charge_stage = 1
          AND ar.`status` = 3
          AND ase.receive_user_id is NULL
          AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date, NOW()) >= #{pendingMinutes}
    </select>

    <select id="notEffectiveList" resultType="com.juyou.system.domain.vo.AccountRechargeNotEffectiveListVo">
        SELECT
        (#{onePendingMinutes}-TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW())) as surplusMinutesNum,
        ar.*
        FROM two_account_recharge ar
        LEFT JOIN two_account_sell s on s.acid = ar.acid
        WHERE
        1 = 1
        AND ar.receive_user_id is NOT NULL
        AND ar.`status` = '3'
        AND ar.charge_stage = 1
        AND s.receive_user_id is NULL
        <if test="accountName != null and accountName != ''">
            AND ar.account_name LIKE CONCAT('%',#{accountName},'%')
        </if>
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        <if test="chargeStage != null and chargeStage != ''">
            AND ar.charge_stage = #{chargeStage}
        </if>
        <if test=" idBalances != null and idBalances.size() > 0 ">
            and ar.recharge_amt in
            <foreach collection="idBalances" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="remainingTime != null">
            AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{remainingTime}
        </if>
        AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{onePendingMinutes}

        UNION ALL

        SELECT
        (#{onePendingMinutes}-TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW())) as surplusMinutesNum,
        ar.*
        FROM two_account_recharge ar
        WHERE
        1 = 1
        AND ar.`status` = '0'
        AND ar.charge_stage = 2
        AND ar.receive_user_id is NULL
        <if test="accountName != null and accountName != ''">
            AND ar.account_name LIKE CONCAT('%',#{accountName},'%')
        </if>
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        <if test="chargeStage != null and chargeStage != ''">
            AND ar.charge_stage = #{chargeStage}
        </if>
        <if test=" idBalances != null and idBalances.size() > 0 ">
            and ar.recharge_amt in
            <foreach collection="idBalances" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="remainingTime != null">
            AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{remainingTime}
        </if>
        AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{onePendingMinutes}

        UNION ALL

        SELECT
        (#{twoPendingMinutes}-TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW())) as surplusMinutesNum,
        ar.*
        FROM two_account_recharge ar
        LEFT JOIN two_account_sell s on s.acid = ar.acid
        WHERE
        1 = 1
        AND ar.receive_user_id is NOT NULL
        AND ar.`status` = '3'
        AND ar.charge_stage = 2
        AND s.receive_user_id is NULL
        <if test="accountName != null and accountName != ''">
            AND ar.account_name LIKE CONCAT('%',#{accountName},'%')
        </if>
        <if test="accountZone != null and accountZone != ''">
            AND ar.account_zone = #{accountZone}
        </if>
        <if test="chargeStage != null and chargeStage != ''">
            AND ar.charge_stage = #{chargeStage}
        </if>
        <if test=" idBalances != null and idBalances.size() > 0 ">
            and ar.recharge_amt in
            <foreach collection="idBalances" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="remainingTime != null">
            AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{remainingTime}
        </if>
        AND TIMESTAMPDIFF(MINUTE,ar.pending_start_date , NOW()) &lt; #{twoPendingMinutes}

    </select>


    <update id="updatePutAccountRecharge" parameterType="AccountRecharge">
        update two_account_recharge
        <trim prefix="SET" suffixOverrides=",">
            receive_user = #{accountRecharge.receiveUser},
            receive_user_id =#{accountRecharge.receiveUserId },
            status = #{accountRecharge.status},
            charge_stage = #{accountRecharge.chargeStage},
            back_reason = #{accountRecharge.backReason},
            has_back = #{accountRecharge.hasBack}
        </trim>
        where acid = #{accountRecharge.acid}
    </update>
    <!--    <select id="selectAccountRechargeCompleteList"  parameterType="java.lang.Integer"  resultMap="AccountRechargeResult">-->
    <!--        SELECT * FROM `two_account_recharge` u-->
    <!--        WHERE u.`status` =3 and u.id_type = ${idType}-->
    <!--    </select>-->


</mapper>
