package com.juyou.system.mapper;

import com.juyou.system.domain.UserGoogleAuthenticator;

import java.util.List;

/**
 * 用户谷歌验证器关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-07
 */
public interface UserGoogleAuthenticatorMapper 
{
    /**
     * 查询用户谷歌验证器关联
     * 
     * @param id 用户谷歌验证器关联主键
     * @return 用户谷歌验证器关联
     */
    public UserGoogleAuthenticator selectUserGoogleAuthenticatorById(Long id);

    /**
     * 查询用户谷歌验证器关联列表
     * 
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 用户谷歌验证器关联集合
     */
    public List<UserGoogleAuthenticator> selectUserGoogleAuthenticatorList(UserGoogleAuthenticator userGoogleAuthenticator);

    /**
     * 新增用户谷歌验证器关联
     * 
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 结果
     */
    public int insertUserGoogleAuthenticator(UserGoogleAuthenticator userGoogleAuthenticator);

    /**
     * 修改用户谷歌验证器关联
     * 
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 结果
     */
    public int updateUserGoogleAuthenticator(UserGoogleAuthenticator userGoogleAuthenticator);

    /**
     * 删除用户谷歌验证器关联
     * 
     * @param id 用户谷歌验证器关联主键
     * @return 结果
     */
    public int deleteUserGoogleAuthenticatorById(Long id);

    /**
     * 批量删除用户谷歌验证器关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserGoogleAuthenticatorByIds(Long[] ids);
}
