import request from '@/utils/request'

// 来源群列表
export function sourceGroupsList(params) {
  return request({
    url: '/new/system/substitutionSettings/sourceGroupsList',
    method: 'get',
    params
  })
}
// 来源群列表
export function sellingGroupsList(params) {
  return request({
    url: '/new/system/substitutionSettings/sellingGroupsList',
    method: 'get',
    params
  })
}
// 修改时间
export function editConfigMinutes(data) {
  return request({
    url: `/new/system/substitutionSettings/editConfigMinutes/${data.oneTime}/${data.twoTime}`,
    method: 'put',
  })
}
// 新增来源群
export function insertSourceGroups(groupName) {
  return request({
    url: `/new/system/substitutionSettings/insertSourceGroups/${groupName}`,
    method: 'post',
  })
}
// 新增来源群
export function insertSellingGroups(groupName) {
  return request({
    url: `/new/system/substitutionSettings/insertSellingGroups/${groupName}`,
    method: 'post',
  })
}
// 获取代充设置详情
export function getTheDetailsOfTheTopUpSettings() {
  return request({
    url: `/new/system/substitutionSettings/config`,
    method: 'get',
  })
}
// 获取代充设置详情
export function editConfigDate(date) {
  return request({
    url: `/new/system/substitutionSettings/editConfigDate/${date}`,
    method: 'put',
  })
}
// 保存日切时间
export function editConfigNumbers(date) {
  return request({
    url: `/new/system/substitutionSettings/editConfigNumbers/${date.oneNumber}/${date.twoNumber}/${date.sellNumber}`,
    method: 'put',
  })
}
// 修改来源群信息
export function editSourceGroups(data) {
  return request({
    url: `/new/system/substitutionSettings/editSourceGroups`,
    method: 'put',
    data
  })
}
// 修改出售群信息
export function editSellingGroups(data) {
  return request({
    url: `/new/system/substitutionSettings/editSellingGroups`,
    method: 'put',
    data
  })
}
