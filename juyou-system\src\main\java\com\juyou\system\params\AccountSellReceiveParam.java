package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号出售领取Param
 */
@ApiModel("AccountSellReceiveParam-账号出售领取Param")
@Data
public class AccountSellReceiveParam implements Serializable {


    private static final long serialVersionUID = 6127681460229434545L;

    @ApiModelProperty("领取的数量")
    private int count;

    @ApiModelProperty("领取面值集合(账号销售的实际充值金额)")
    private List<Double> cardBalanceList;

    @ApiModelProperty("库等级")
    private String chargeStage;

    @ApiModelProperty("领取的钱包区域")
    private String walletArea;


}
