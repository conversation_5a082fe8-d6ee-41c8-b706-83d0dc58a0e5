package com.juyou.system.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号出售跟新-param
 */
@Data
@ApiModel("AccountSellUpdateParam-账号出售跟新param")
public class AccountSellUpdateParam implements Serializable {

    private static final long serialVersionUID = 622109519206672041L;

    @ApiModelProperty("rechargeId")
    private Integer rechargeId;

    @ApiModelProperty("账号区域")
    private String accountZone;

    @ApiModelProperty("出售单价")
    private Double sellPrice;

    @ApiModelProperty("出售金额")
    private Double sellAmt;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("售卖人-客户")
    private String custName;
}
