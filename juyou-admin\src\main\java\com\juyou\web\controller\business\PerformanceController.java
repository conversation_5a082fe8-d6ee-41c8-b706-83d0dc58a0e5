package com.juyou.web.controller.business;

import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.CompanyPerformanceQueryParam;
import com.juyou.system.params.PerformanceSearchParam;
import com.juyou.system.service.IPerformanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 业绩表-Controller
 */
@Api(value = "业绩表", tags = "业绩表")
@RestController
@RequestMapping("/performance")
public class PerformanceController extends BaseController {

    @Autowired
    private IPerformanceService iPerformanceService;

    @ApiOperation("ID部业绩表列表")
    @Log(title = "业绩表-ID部业绩表列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:performance:list')")
    @GetMapping("/list")
    public TableDataInfo<PerformanceVoList> list(PerformanceSearchParam param) {
        List<PerformanceVoList> list = this.iPerformanceService.list(param);
        return getDataTable(list);
    }

    @ApiOperation("业绩表-公司业绩")
    @Log(title = "业绩表-公司业绩", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:performance:list')")
    @GetMapping("/getPerformanceData")
    public CompanyPerformanceVo getPerformanceData() {

        return iPerformanceService.getPerformanceData();
    }

    @ApiOperation("业绩表-员工树")
    @Log(title = "业绩表-员工树", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:department:tree')")
    @GetMapping("/getDepartmentList")
    public  List<DepartmentTreeVo>getDepartmentList() {
        return iPerformanceService.getDepartmentList();
    }

    @ApiOperation("业绩表-详细业绩")
    @Log(title = "业绩表-详细业绩", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:department:query')")
    @GetMapping("/query")
    public PerformanceReportIsDetailedVo getPerformanceReportIsDetailed (CompanyPerformanceQueryParam param ) {

        return iPerformanceService.getPerformanceReportIsDetailed(param);
    }

}
