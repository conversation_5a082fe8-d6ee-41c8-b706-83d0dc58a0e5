package com.juyou.web.controller.config;

import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.domain.SellingGroups;
import com.juyou.system.domain.SourceGroups;
import com.juyou.system.service.ISellingGroupsService;
import com.juyou.system.service.ISourceGroupsService;
import com.juyou.system.service.ISysConfigService;
import com.juyou.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "代充配置", tags = "代充配置")
@RestController
@RequestMapping("/system/substitutionSettings")
@Slf4j
public class SubstitutionSettingsController extends BaseController {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ISellingGroupsService sellingGroupsService;

    @Autowired
    private ISourceGroupsService sourceGroupsService;

    /**
     * 查询出售群信息列表
     */
    @ApiOperation("查询代充设置信息")
//    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:list')")
    @GetMapping("/config")
    public ResultData<Map<String, Object>> config()
    {
        Map<String, Object> map = new HashMap<>();
        map.put("oneMinutes",sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES));
        map.put("twoMinutes",sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES_TWO));
        map.put("oneNumber",sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_ECHARGE_ONE));
        map.put("twoNumber",sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_ECHARGE_TWO));
        map.put("sellNumber",sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL));
        map.put("date",sysConfigService.selectConfigByKey(ConfigConstant.DIURNAL_TANGENCY_POINT));
        return ResultUtil.success(map);
    }

    @ApiOperation("等待天数设置")
    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:edit')")
    @Log(title = "等待天数设置", businessType = BusinessType.UPDATE)
    @PutMapping("/editConfigMinutes/{one}/{two}")
    public ResultData<Integer> editConfigMinutes(@PathVariable("one") String one,@PathVariable("two") String two)
    {
        return ResultUtil.success(sysConfigService.updateConfigMinutes(one, two));
    }

    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:edit')")
    @Log(title = "日切点设置", businessType = BusinessType.UPDATE)
    @PutMapping("/editConfigDate/{date}")
    public ResultData<Integer> editConfigDate(@PathVariable("date") String date)
    {
        return ResultUtil.success(sysConfigService.updateConfigDate(date));
    }

    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:edit')")
    @Log(title = "未完成ID个数设置", businessType = BusinessType.UPDATE)
    @PutMapping("/editConfigNumbers/{one}/{two}/{sell}")
    public ResultData<Integer> editConfigNumbers(@PathVariable("one") String one,@PathVariable("two") String two,@PathVariable("sell") String sell)
    {
        return ResultUtil.success(sysConfigService.updateConfigNumbers(one, two, sell));
    }

    /**
     * 查询出售群信息列表
     */
    @ApiOperation("查询出售群信息列表")
//    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:list')")
    @GetMapping("/sellingGroupsList")
    public TableDataInfo<SellingGroups> sellingGroupsList(SellingGroups sellingGroups)
    {
        startPage();
        List<SellingGroups> list = sellingGroupsService.selectSellingGroupsList(sellingGroups);
       return getDataTable(list);
    }

    /**
     * 新增出售群信息
     */
    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:add')")
    @Log(title = "出售群信息", businessType = BusinessType.INSERT)
    @PostMapping("/insertSellingGroups/{groupName}")
    public ResultData<Integer> insertSellingGroups(@PathVariable("groupName") String groupName)
    {
        try {
            SellingGroups sellingGroups = new SellingGroups();
            sellingGroups.setGroupNumber(groupName);
            sellingGroups.setCreateBy(getUsername());
            return ResultUtil.success(sellingGroupsService.insertSellingGroups(sellingGroups));
        } catch (Exception e) {
            log.error("新增出售群信息失败", e);
            return ResultUtil.error("新增出售群信息已存在");
        }
    }

    /**
     * 修改出售群信息
     */
    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:edit')")
    @Log(title = "出售群信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editSellingGroups")
    public ResultData<Integer> editSellingGroups(@RequestBody SellingGroups sellingGroups)
    {
        try {
            // 调用服务层方法更新数据
            int affectedRows = sellingGroupsService.updateSellingGroups(sellingGroups);

            // 根据受影响行数判断操作是否成功
            if (affectedRows > 0) {
                // 如果至少有一行被更新，则认为操作成功
                // 这里可以根据需要添加更具体的成功消息
                return ResultUtil.success(affectedRows);
            } else {
                // 如果没有行被更新，可能是没有找到对应的记录，也可能是更新条件没有匹配到任何行
                // 返回错误消息
                return ResultUtil.error("未找到对应的销售群信息或未发生任何更改");
            }
        } catch (Exception e) {
            // 记录详细的错误信息到日志
            log.error("更新销售群信息时发生异常", e);
            // 返回通用的错误信息给客户端
            return ResultUtil.error("更新销售群信息失败，群号已存在");
        }
    }

    /**
     * 查询来源群信息列表
     */
    @ApiOperation("查询来源群信息列表")
//    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:list')")
    @GetMapping("/sourceGroupsList")
    public TableDataInfo<SourceGroups> sourceGroupsList(SourceGroups sourceGroups)
    {    startPage();
        List<SourceGroups> list = sourceGroupsService.selectSourceGroupsList(sourceGroups);
//        return ResultUtil.success(list);
        return getDataTable(list);
    }



    /**
     * 新增来源群信息
     */
    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:add')")
    @Log(title = "来源群信息", businessType = BusinessType.INSERT)
    @PostMapping("/insertSourceGroups/{groupName}")
    public ResultData<Integer> insertSourceGroups(@PathVariable("groupName") String groupName)
    {
        try {
            SourceGroups sourceGroups = new SourceGroups();
            sourceGroups.setGroupNumber(groupName);
            sourceGroups.setCreateBy(getUsername());
            return ResultUtil.success(sourceGroupsService.insertSourceGroups(sourceGroups));
        } catch (Exception e) {
            log.error("新增来源群信息失败",e);
            return ResultUtil.error("新增来源群信息已存在");
        }
    }

    /**
     * 修改来源群信息
     */
    @PreAuthorize("@ss.hasPermi('system:substitutionSettings:edit')")
    @Log(title = "来源群信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editSourceGroups")
    public ResultData<Integer> editSourceGroups(@RequestBody SourceGroups sourceGroups)

    {   try {
        // 调用服务层方法更新数据
        int affectedRows = sourceGroupsService.updateSourceGroups(sourceGroups);

        // 根据受影响行数判断操作是否成功
        if (affectedRows > 0) {
            // 如果至少有一行被更新，则认为操作成功
            return ResultUtil.success(affectedRows);
        } else {
            // 如果没有行被更新，可能是没有找到对应的记录，也可能是更新条件没有匹配到任何行
            // 这里可以根据实际需求调整错误信息
            return ResultUtil.error("未找到对应的来源群信息或未发生任何更改");
        }
    } catch (Exception e) {
        // 记录详细的错误信息到日志
        log.error("修改来源群信息时发生异常", e);
        // 返回通用的错误信息给客户端
        return ResultUtil.error("修改来源群信息失败，群号已存在");
    }
    }
}
