package com.juyou.web.controller.business;

import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.PerformanceVoList;
import com.juyou.system.domain.vo.RecordOfChargingCardsVo;
import com.juyou.system.params.AccountSellSoldListSearchParam;
import com.juyou.system.params.PerformanceSearchParam;
import com.juyou.system.params.RecordOfChargingCardsParam;
import com.juyou.system.service.IPerformanceService;
import com.juyou.system.service.IRecordOfChargingCardsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api(value = "代充卡记录", tags = "代充卡记录查询")
@RestController
@RequestMapping("/recordOfChargingCards")
public class RecordOfChargingCardsController extends BaseController {

    @Autowired
    private IRecordOfChargingCardsService  recordOfChargingCardsService;

    @ApiOperation("苹果代充/代充卡记录列表")
    @Log(title = "苹果代充/代充卡记录列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:recordOfChargingCards:list')")
    @GetMapping("/getRecordOfChargingCardsList")
    public TableDataInfo<RecordOfChargingCardsVo> getRecordOfChargingCardsList(RecordOfChargingCardsParam param) {
        startPage();
        List<RecordOfChargingCardsVo> list = recordOfChargingCardsService.getRecordOfChargingCards(param);
        return getDataTable(list);
    }
}

