package com.juyou.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡充值记录对象 two_gift_card_recharge_record
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
@Data
@ApiModel("GiftCardRechargeRecord")
@TableName("two_gift_card_recharge_record")
public class GiftCardRechargeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    @ApiModelProperty("账号")
    @Excel(name = "账号")
    private String account;

    @ApiModelProperty("账号密码")
    @Excel(name = "账号密码")
    private String accountPwd;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("礼品卡")
    @Excel(name = "礼品卡")
    private String giftCard;

    @ApiModelProperty("卡片状态")
    @Excel(name = "卡片状态")
    private String cardStatus;

    @ApiModelProperty("执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date executionTime;

    @ApiModelProperty("面值")
    @Excel(name = "面值")
    private BigDecimal faceValue;

    @ApiModelProperty("余额")
    @Excel(name = "余额")
    private BigDecimal balance;

    @ApiModelProperty("进价(充值账号的单价)")
    @Excel(name = "进价(充值账号的单价)")
    private BigDecimal buyPrice;

    @ApiModelProperty("成本")
    @Excel(name = "成本")
    private BigDecimal buyAmt;

    @ApiModelProperty("执行信息")
    @Excel(name = "执行信息")
    private String executionInfo;

    @Excel(name = "来源群")
    @ApiModelProperty("来源群")
    private String sourceGroup;

    @Excel(name = "是否质押30分钟: 0 否 1 是")
    @ApiModelProperty("是否质押30分钟: 0 否 1 是")
    private String pledgeThirtyMinutes;

    @ApiModelProperty("质押开始时间")
    @Excel(name = "质押开始时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pledgeStartDate;

    @ApiModelProperty("质押结束时间")
    @Excel(name = "质押结束时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pledgeEndDate;


}
