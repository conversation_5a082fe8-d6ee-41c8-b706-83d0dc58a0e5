<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.QuotationMapper">

    <resultMap type="Quotation" id="QuotationResult">
        <result property="id"    column="id"    />
        <result property="groupNumber"    column="group_number"    />
        <result property="cardType"    column="card_type"    />
        <result property="country"    column="country"    />
        <result property="minFaceValue"    column="min_face_value"    />
        <result property="maxFaceValue"    column="max_face_value"    />
        <result property="price"    column="price"    />
        <result property="remark"    column="remark"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuotationVo">
        select id, group_number, card_type, country, min_face_value, max_face_value, price, remark, update_time,open from quotation
    </sql>

    <select id="selectQuotationList" parameterType="Quotation" resultMap="QuotationResult">
        <include refid="selectQuotationVo"/>
        <where>
            <if test="groupNumber != null  and groupNumber != ''"> and group_number = #{groupNumber}</if>
             <if test="groupNumbers != null and groupNumbers != ''">
                 and group_number = #{groupNumbers}
             </if>
            <if test="cardType != null  and cardType != ''"> and card_type = #{cardType}</if>
            <if test="country != null  and country != ''"> and country like concat(#{country}, '%')</if>
            <if test="minFaceValue != null "> and min_face_value &gt;= #{minFaceValue} and IF(max_face_value = -1, 99999999, max_face_value) &lt;= #{maxFaceValue}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="open != null  and open != ''"> and open = #{open}</if>
        </where>
        order by group_number,country,price desc
    </select>

    <select id="selectQuotationGroup" parameterType="Quotation" resultMap="QuotationResult">
            select group_number from quotation group by group_number
    </select>

    <select id="selectQuotationById" parameterType="Long" resultMap="QuotationResult">
        <include refid="selectQuotationVo"/>
        where id = #{id}
    </select>

    <insert id="insertQuotation" parameterType="Quotation" useGeneratedKeys="true" keyProperty="id">
        insert into quotation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupNumber != null">group_number,</if>
            <if test="cardType != null">card_type,</if>
            <if test="country != null">country,</if>
            <if test="minFaceValue != null">min_face_value,</if>
            <if test="maxFaceValue != null">max_face_value,</if>
            <if test="price != null">price,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupNumber != null">#{groupNumber},</if>
            <if test="cardType != null">#{cardType},</if>
            <if test="country != null">#{country},</if>
            <if test="minFaceValue != null">#{minFaceValue},</if>
            <if test="maxFaceValue != null">#{maxFaceValue},</if>
            <if test="price != null">#{price},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateQuotation" parameterType="Quotation">
        update quotation
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupNumber != null">group_number = #{groupNumber},</if>
            <if test="cardType != null">card_type = #{cardType},</if>
            <if test="country != null">country = #{country},</if>
            <if test="minFaceValue != null">min_face_value = #{minFaceValue},</if>
            <if test="maxFaceValue != null">max_face_value = #{maxFaceValue},</if>
            <if test="price != null">price = #{price},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="open != null">open = #{open},</if>
            update_time = SYSDATE(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuotationById" parameterType="Long">
        delete from quotation where id = #{id}
    </delete>

    <delete id="deleteQuotationByIds" parameterType="String">
        delete from quotation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
