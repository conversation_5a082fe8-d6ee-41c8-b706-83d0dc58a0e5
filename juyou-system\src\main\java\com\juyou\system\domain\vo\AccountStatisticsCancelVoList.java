package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 账号统计作废-VoList
 */
@Data
@ApiModel("AccountStatisticsCancelVoList-账号统计作废-VoList")
public class AccountStatisticsCancelVoList implements Serializable {

    private static final long serialVersionUID = -7124490533491946848L;

    @ApiModelProperty("作废类型")
    private String cancelType;

    @ApiModelProperty("作废数量")
    private Integer cancelTotal;

    // 实际充值金额
    @ApiModelProperty("作废金额")
    private BigDecimal cancelAmt;

    // 成本
    @ApiModelProperty("亏损总额(CNY)")
    private BigDecimal lossAmt;


}
