
-- 新增权限菜单
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2062, '账号销售统计-出售账号作废金额明细列表', 2045, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:sellCancelAmtDetailList', '#', 'admin', '2023-10-08 09:50:40', '', NULL, '');
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2063, '充值账号作废金额明细列表', 2045, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:rechargeCancelAmtDetailList', '#', 'admin', '2023-10-08 09:51:02', '', NULL, '');
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2064, '出售群统计明细列表', 2045, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:sellGroupDetailList', '#', 'admin', '2023-10-08 09:51:28', '', NULL, '');
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2065, '礼品卡充值来源群统计详情列表', 2045, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:giftCardRechargeStatisticsDetail', '#', 'admin', '2023-10-08 14:45:12', '', NULL, '');
INSERT INTO `juyou_tool`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2066, '礼品卡充值来源群统计', 2045, 9, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:sellStatistics:giftCardRechargeStatistics', '#', 'admin', '2023-10-08 14:45:28', '', NULL, '');

-- 礼品卡添加 进价(充值账号的单价),成本
ALTER TABLE `two_gift_card_recharge_record` ADD COLUMN `buy_price` decimal(10,2) DEFAULT NULL COMMENT '进价(充值账号的单价)' AFTER `balance`;
ALTER TABLE `two_gift_card_recharge_record` ADD COLUMN `buy_amt` decimal(10,2) DEFAULT NULL COMMENT '成本' AFTER `buy_price`;

-- 礼品卡进价(充值账号的单价) 取值: 取账号充值的收卡单价赋值,注意这个账号充值的单价是最后的单价
update two_gift_card_recharge_record g
    left join two_account_recharge ar on ar.account_name = g.account
    set g.buy_price = ar.buy_price
WHERE g.buy_price is NULL;

-- 礼品卡成本 : 礼品卡面值*礼品卡进价
update two_gift_card_recharge_record g
set g.buy_amt = g.face_value * g.buy_price
WHERE g.buy_amt is NULL;
