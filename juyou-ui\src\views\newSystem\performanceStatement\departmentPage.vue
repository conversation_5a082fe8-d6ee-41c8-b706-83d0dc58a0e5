<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="inputName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">
          <el-form-item label="工作日期：">
            <el-date-picker
              v-model="dateRange"
              style="width: 400px"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="ID归属业务员：" prop="userName">
            <el-input
              v-model="queryParams.idUser"
              placeholder="请输入ID归属业务员"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="20">
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">充值作废率</div>
              <div class="item-value">{{form.rechargeCancelRate?form.rechargeCancelRate.toFixed(2): 0}}%</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">出售作废率</div>
              <div class="item-value">{{form.sellCancelRate?form.sellCancelRate.toFixed(2): 0}}%</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">退回率</div>
              <div class="item-value">{{form.bounceRate?form.bounceRate.toFixed(2): 0}}%</div>
            </div>
          </el-col>
          <el-col :span="6">

          </el-col>
          <el-col :span="6">

          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">充值数量</div>
              <div class="item-value">{{form.rechargeCount?form.rechargeCount: 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">充值成本总额</div>
              <div class="item-value">{{form.rechargeCostTotal?form.rechargeCostTotal.toFixed(2): 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">充值作废数量</div>
              <div class="item-value">{{form.rechargeCancelCount?form.rechargeCancelCount: 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">充值作废成本总额</div>
              <div class="item-value">{{form.rechargeCancelCostTotal?form.rechargeCancelCostTotal.toFixed(2): 0}}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">出售数量</div>
              <div class="item-value">{{form.sellCount?form.sellCount: 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">出售金额总额</div>
              <div class="item-value">{{form.sellCostTotal?form.sellCostTotal.toFixed(2): 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">出售作废数量</div>
              <div class="item-value">{{form.sellCancelCount?form.sellCancelCount: 0}}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div v-if="form" style="margin: 10px 20px">
              <div class="item-title">出售作废金额总额</div>
              <div class="item-value">{{form.sellCancelCostTotal?form.sellCancelCostTotal.toFixed(2): 0}}</div>
            </div>
          </el-col>
        </el-row>
        <el-table v-loading="loading" :data="dataList">
          <el-table-column label="区域" align="center" prop="accountZone" />
          <el-table-column label="充值总ID余额" align="center" prop="rechargeTotal" />
          <el-table-column label="充值作废总ID余额" align="center" prop="rechargeCancelTotal" />
          <el-table-column label="出售总ID余额" align="center" prop="sellTotal" />
          <el-table-column label="出售作废总ID余额" align="center" prop="sellCancelTotal" width="120" />
        </el-table>

<!--        <pagination-->
<!--          v-show="total>0"-->
<!--          :total="total"-->
<!--          :page.sync="queryParams.pageNum"-->
<!--          :limit.sync="queryParams.pageSize"-->
<!--          @pagination="getList"-->
<!--        />-->
      </el-col>
    </el-row>

  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus ,generateGoogleSecret} from "@/api/system/user";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { detailedPerformance, getDepartmentList } from '@/api/newSystem/performanceStatement'
import el from 'element-ui/src/locale/lang/el'
import { getTheDetailsOfTheTopUpSettings } from '@/api/newSystem/substitutionSettings'

export default {
  name: "departmentPage",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      form:{},
      dateRange:[],
      // 树选项
      deptOptions: undefined,
      inputName:'',
      dataList:[],
      loading:false,
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
        idUser: undefined

      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    inputName(val) {
      this.$refs.tree.filter(val);
    }
  },
  async created() {
    this.getConfigKey("sys.user.initPassword").then(response => {
      this.initPassword = response.msg;
    });
    await this.today()
    this.getList();
    this.getTreeselect();

  },
  methods: {
    async today() {
      const res = await getTheDetailsOfTheTopUpSettings()
      let date = res.data.date
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      const end = new Date();
      end.setDate(end.getDate() + 1);
      const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
      this.dateRange = [`${today} ${date || '00:00:00'}`, `${endStr} ${date || '00:00:00'}`]

      // const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      // this.$set(this.queryParams, 'receiveTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
      // this.$set(this.queryParams, 'doneTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      if (this.dateRange && this.dateRange.length){
        this.queryParams.endTime = this.dateRange[1]
        this.queryParams.startTime = this.dateRange[0]
      }else{
        this.queryParams.endTime = ''
        this.queryParams.startTime = ''
      }
      detailedPerformance(this.queryParams).then(response => {

          this.dataList = response.performanceDetailedVoList;
          this.form = response;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      getDepartmentList().then(response => {
        console.log(response)
        response.forEach(e=>{
          if (e.children && e.children.length){
            e.children.forEach(ee=>{
              ee.label = ee.userName
            })
          }
        })
        this.deptOptions = response;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log(data)
      if (data.userId){
        this.queryParams.userId = data.userId
        this.queryParams.deptId = ''
      }else{
        this.queryParams.userId = ''
        this.queryParams.deptId = data.deptId
      }
      // this.queryParams.deptId = data.id;
      this.handleQuery();
    },

  }
};
</script>
<style scoped lang="scss">
::v-deep .is-current{
  background-color: #40a3ff !important;
  color: #FFFFFF !important;
}
//::v-deep .el-tree-node__content:hover{
//  background-color: #00afff !important;
//}
::v-deep .el-tree-node:focus{
  .el-tree-node__content{
    background-color: #40a3ff !important;
    color: #FFFFFF !important;
  }
}
.item-title{
  text-align: center;
  font-size: 16px;
  color: #9292a4;
}
.item-value{
  margin-bottom: 20px;
  margin-top: 4px;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}
.sum-amt{
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
