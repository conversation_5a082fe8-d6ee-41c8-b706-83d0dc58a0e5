package com.juyou.framework.web.service;

import cn.hutool.core.util.ObjUtil;
import com.juyou.common.constant.Constants;
import com.juyou.common.constant.UserConstants;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.core.domain.model.LoginUser;
import com.juyou.common.core.redis.RedisCache;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.exception.user.CaptchaException;
import com.juyou.common.exception.user.CaptchaExpireException;
import com.juyou.common.exception.user.UserPasswordNotMatchException;
import com.juyou.common.utils.DateUtils;
import com.juyou.common.utils.MessageUtils;
import com.juyou.common.utils.ServletUtils;
import com.juyou.common.utils.StringUtils;
import com.juyou.common.utils.ip.IpUtils;
import com.juyou.framework.manager.AsyncManager;
import com.juyou.framework.manager.factory.AsyncFactory;
import com.juyou.system.domain.SysDeptSession;
import com.juyou.system.domain.UserGoogleAuthenticator;
import com.juyou.system.service.ISysConfigService;
import com.juyou.system.service.ISysDeptSessionService;
import com.juyou.system.service.ISysUserService;
import com.juyou.system.service.IUserGoogleAuthenticatorService;
import com.juyou.system.utils.GoogleAuthenticator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IUserGoogleAuthenticatorService iUserGoogleAuthenticatorService;

    @Autowired
    private ISysDeptSessionService sysDeptSessionService;


    /**
     * 谷歌验证器登陆
     *
     * @param username
     * @param password
     * @param code
     * @return
     */
    public String googleLogin(String username, String password, String code) {
        SysUser sysUser = this.userService.selectUserByUserName(username);
        if(ObjUtil.isNull(sysUser)){
            throw new ServiceException("账号不存在!");
        }

        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }

        // Google校验器
        if(!sysUser.isAdmin()){
            // 验证码开关
            boolean captchaOnOff = configService.selectCaptchaOnOff();
            if (captchaOnOff) {
                UserGoogleAuthenticator userGoogleAuthenticator = this.iUserGoogleAuthenticatorService.getByUsername(username);
                if(ObjUtil.isNull(userGoogleAuthenticator)){
                    throw new ServiceException("该账号没有绑定谷歌验证器,请去绑定!");
                }
                boolean isTrue = GoogleAuthenticator.check_code(userGoogleAuthenticator.getGoogleSecret(), Long.parseLong(code), System.currentTimeMillis());
                if(!isTrue){
                    throw new ServiceException("验证码不正确!");
                }
            }
        }
        Long session = Long.valueOf(0);
        SysDeptSession deptSession = new SysDeptSession();
        deptSession.setDeptId(sysUser.getDeptId());
        List<SysDeptSession> list = sysDeptSessionService.selectSysDeptSessionList(deptSession);
        if (list.size() > 0) {
            redisCache.setCacheObject(UserConstants.token_expire_time_dept_id + sysUser.getDeptId().toString(), list.get(0).getSession());
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        boolean captchaOnOff = configService.selectCaptchaOnOff();
        // 验证码开关
        if (captchaOnOff) {
            validateCaptcha(username, code, uuid);
        }
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
}
