package com.juyou.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.system.domain.AccountSell;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("sellAnchorVo")
public class SellAnchorVo extends AccountSell {

    private static final long serialVersionUID = 6518901787464483647L;

    @ApiModelProperty("是否使用")
    private String isUse;

    @ApiModelProperty("使用开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date useStartTime;

    @ApiModelProperty("使用结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date useEndTime;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellTime;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellTime;

}
