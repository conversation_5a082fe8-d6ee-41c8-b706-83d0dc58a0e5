package com.juyou.system.service.impl;

import java.util.List;
import com.juyou.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.AccountRechargeLogMapper;
import com.juyou.system.domain.AccountRechargeLog;
import com.juyou.system.service.IAccountRechargeLogService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
@Service
public class AccountRechargeLogServiceImpl implements IAccountRechargeLogService
{
    @Autowired
    private AccountRechargeLogMapper accountRechargeLogMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public AccountRechargeLog selectAccountRechargeLogById(Long id)
    {
        return accountRechargeLogMapper.selectAccountRechargeLogById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<AccountRechargeLog> selectAccountRechargeLogList(AccountRechargeLog accountRechargeLog)
    {
        return accountRechargeLogMapper.selectAccountRechargeLogList(accountRechargeLog);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertAccountRechargeLog(AccountRechargeLog accountRechargeLog)
    {
        accountRechargeLog.setCreateTime(DateUtils.getNowDate());
        return accountRechargeLogMapper.insertAccountRechargeLog(accountRechargeLog);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param accountRechargeLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateAccountRechargeLog(AccountRechargeLog accountRechargeLog)
    {
        return accountRechargeLogMapper.updateAccountRechargeLog(accountRechargeLog);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAccountRechargeLogByIds(Long[] ids)
    {
        return accountRechargeLogMapper.deleteAccountRechargeLogByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAccountRechargeLogById(Long id)
    {
        return accountRechargeLogMapper.deleteAccountRechargeLogById(id);
    }
}
