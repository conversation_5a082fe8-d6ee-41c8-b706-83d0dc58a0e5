package com.juyou;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@MapperScan("com.juyou.system.**.*mapper")
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class JuYouApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(JuYouApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  JuYouTools 启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}

