package com.juyou.system.service;

import com.juyou.system.domain.AccountSell;
import com.juyou.system.log.RechargeLog;
import com.juyou.system.service.impl.AccountSellServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class RechargeLogTest {
    @Autowired
    IAccountSellService accountSellsService ;
    @Test
    public void test() {
        RechargeLog e = new RechargeLog();

        AccountSell oldACS = accountSellsService.selectAccountSellByRechargeId(1);
        if (oldACS == null)
        { AccountSell newACS = new AccountSell();
        }

//        e.insertSell(oldACS, newACS, "1", "1", 1L);
    }
}
