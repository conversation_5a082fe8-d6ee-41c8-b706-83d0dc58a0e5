package com.juyou.system.service;

import java.util.List;
import com.juyou.system.domain.SysDeptSession;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface ISysDeptSessionService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysDeptSession selectSysDeptSessionById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysDeptSession> selectSysDeptSessionList(SysDeptSession sysDeptSession);

    /**
     * 新增【请填写功能名称】
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 结果
     */
    public int insertSysDeptSession(SysDeptSession sysDeptSession);

    /**
     * 修改【请填写功能名称】
     *
     * @param sysDeptSession 【请填写功能名称】
     * @return 结果
     */
    public int updateSysDeptSession(SysDeptSession sysDeptSession);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSysDeptSessionByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysDeptSessionById(Long id);
}
