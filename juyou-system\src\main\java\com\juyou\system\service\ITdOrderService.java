package com.juyou.system.service;

import com.juyou.system.domain.TdOrder;

import java.util.List;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2023-06-17
 */
public interface ITdOrderService 
{
    /**
     * 查询订单
     * 
     * @param id 订单ID
     * @return 订单
     */
    public TdOrder selectTdOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param tdOrder 订单
     * @return 订单集合
     */
    public List<TdOrder> selectTdOrderList(TdOrder tdOrder);

    /**
     * 新增订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    public int insertTdOrder(TdOrder tdOrder);

    /**
     * 修改订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    public int updateTdOrder(TdOrder tdOrder);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    public int deleteTdOrderByIds(Long[] ids);

    /**
     * 删除订单信息
     * 
     * @param id 订单ID
     * @return 结果
     */
    public int deleteTdOrderById(Long id);
}
