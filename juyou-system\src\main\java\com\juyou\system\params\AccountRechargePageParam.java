package com.juyou.system.params;

import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 账号充值分页参数
 */
@Data
public class AccountRechargePageParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 6452435746754042995L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("充值状态")
    private String status;

    @ApiModelProperty("核销状态：1 未核对 2 已核对")
    @Excel(name = "核销状态：1 未核对 2 已核对")
    private String writeOffStatus;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("idType")
    private String idType;

    @ApiModelProperty("领取开始时间")
    private Date startReceiveTime;

    @ApiModelProperty("领取结束时间")
    private Date endReceiveTime;

    @ApiModelProperty("完成开始时间")
    private Date startDoneTime;

    @ApiModelProperty("完成结束时间")
    private Date endDoneTime;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("ids导出选中数据时使用")
    private List<Integer> ids;

    @ApiModelProperty("礼品卡代码")
    private String giftCard;

    /**
     * 领取人--前端不传后端穿
     */
    private String receiveUser;




}
