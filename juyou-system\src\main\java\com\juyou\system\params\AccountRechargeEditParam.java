package com.juyou.system.params;

import com.juyou.system.domain.AccountRecharge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账号充值编辑Param
 */
@ApiModel("AccountRechargeEditParam-账号充值编辑Param")
@Data
public class AccountRechargeEditParam extends AccountRecharge implements Serializable {

    private static final long serialVersionUID = 294160922432219189L;


    @ApiModelProperty("礼品卡代码")
    private String giftCard;

    @ApiModelProperty("来源群")
    private String sourceGroup;

    @ApiModelProperty("是否质押30分钟 列 0 否 1 是")
    private String pledgeThirtyMinutes;

    @ApiModelProperty("本次充值金额")
    private BigDecimal timeAmt;

    @ApiModelProperty("需要删除的礼品卡充值记录id集合")
    private List<Long> deleteGiftCardRechargeRecordIds;



}
