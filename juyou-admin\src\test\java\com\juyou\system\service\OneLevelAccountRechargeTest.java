package com.juyou.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.AccountRechargeDataPanelVo;
import com.juyou.system.domain.vo.AccountRechargeDetailVo;
import com.juyou.system.domain.vo.AccountRechargeResidualVo;
import com.juyou.system.domain.vo.AccountSellResidualAccountVoList;
import com.juyou.system.params.AccountRechargeDataPanelParam;
import com.juyou.system.params.AccountRechargePageParam;
import com.juyou.system.params.AccountRechargeResidualParam;
import com.juyou.system.utils.JuYouBusinessUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * 一级充值
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class OneLevelAccountRechargeTest {

    @Autowired
    private IAccountRechargeService accountRechargeService;
    @Autowired
    private IGiftCardRechargeRecordService giftCardRechargeRecordService;


    @Test
    public void getInfo() {
        Integer acid = 1974;

        AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(acid);
        GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
        rechargeRecord.setAccount(accountRecharge.getAccountName());
        List<GiftCardRechargeRecord> list = this.giftCardRechargeRecordService.selectGiftCardRechargeRecordList(rechargeRecord);

        AccountRechargeDetailVo vo = new AccountRechargeDetailVo();
        BeanUtil.copyProperties(accountRecharge, vo);
        vo.setGiftCardRechargeRecordList(list);

        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getRechargeAmt()));

        log.info("vo:{}", JSONUtil.toJsonPrettyStr(vo));
    }

    @Test
    public void getDataPanel(){
        AccountRechargeDataPanelParam param = new AccountRechargeDataPanelParam();
        param.setAccountZone("美国");
        param.setLoginUserId(Long.valueOf(106));

        AccountRechargeDataPanelVo vo = this.accountRechargeService.getDataPanel(param);

        log.info("vo:{}", JSONUtil.toJsonPrettyStr(vo));
    }

    @Test
    public void list(){
        AccountRechargePageParam param = new AccountRechargePageParam();
        param.setAccountZone("美国");

        List<AccountRecharge> list = this.accountRechargeService.selectAccountRechargePage(param);

        log.info("listVo:{}", JSONUtil.toJsonPrettyStr(list));
    }

    @Test
    public void getAccountRechargeResidualList(){
        AccountRechargeResidualParam param = new AccountRechargeResidualParam();
        param.setLoginUserId(106L);
        param.setAccountZone("美国");

        AccountRechargeResidualVo vo = new AccountRechargeResidualVo();
        // 一级账号列表
        List<AccountSellResidualAccountVoList> oneList = this.accountRechargeService.findOneLevelResidualAccountList(param);
        vo.setOneList(oneList);

        // 二级账号列表
        List<AccountSellResidualAccountVoList> twoList = this.accountRechargeService.findTwoLevelResidualAccountList(param);
        vo.setTwoList(twoList);

        // 我的账号
        List<AccountSellResidualAccountVoList> myList = this.accountRechargeService.findMyResidualAccountList(param);
        vo.setMyList(myList);

        log.info("vo:{}", JSONUtil.toJsonPrettyStr(vo));
    }

}
