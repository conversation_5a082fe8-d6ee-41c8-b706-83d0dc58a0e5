package com.juyou.system.mapper;





import com.juyou.system.domain.vo.TowUpdateHistory;

import java.util.List;


/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface TowUpdateHistoryMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TowUpdateHistory selectTowUpdateHistoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TowUpdateHistory> selectTowUpdateHistoryList(TowUpdateHistory towUpdateHistory);

    /**
     * 新增【请填写功能名称】
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 结果
     */
    public int insertTowUpdateHistory(TowUpdateHistory towUpdateHistory);

    /**
     * 修改【请填写功能名称】
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 结果
     */
    public int updateTowUpdateHistory(TowUpdateHistory towUpdateHistory);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTowUpdateHistoryById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTowUpdateHistoryByIds(Long[] ids);

    /**
     * 获取最新的消息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public TowUpdateHistory selectTowUpdateHistoryNew();
}
