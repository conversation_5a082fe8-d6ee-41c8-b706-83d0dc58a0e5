package com.juyou.common.core.domain;


import com.juyou.common.constant.HttpStatus;

import java.util.List;

/**
 * 结果Util
 */
public class ResultUtil {


    public static  <T> ResultData<T> success() {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.SUCCESS);
        data.setMsg("操作成功");
        return data;
    }

    public static <T> ResultData<List<T>> success(List<T> list){
        ResultData<List<T>> data = new ResultData<>();
        data.setCode(HttpStatus.SUCCESS);
        data.setMsg("操作成功");
        data.setData(list);
        return data;
    }

    public static <T> ResultData<T> success(T obj) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.SUCCESS);
        data.setMsg("操作成功");
        data.setData(obj);
        return data;
    }

    public static <T> ResultData<T> success(String msg) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.SUCCESS);
        data.setMsg(msg);
        return data;
    }

    public static <T> ResultData<T> success(String msg, T obj) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.SUCCESS);
        data.setMsg(msg);
        data.setData(obj);
        return data;
    }

    public static <T> ResultData<T> error(String msg) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.ERROR);
        data.setMsg(msg);
        return data;
    }

    public static <T> ResultData<T> error(String msg, T obj) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(500);
        data.setMsg(msg);
        data.setData(obj);
        return data;
    }

    public static <T> ResultData<T> error(int code, String msg) {
        ResultData<T> data = new ResultData<T>();
        data.setCode(HttpStatus.ERROR);
        data.setMsg(msg);
        return data;
    }



}
