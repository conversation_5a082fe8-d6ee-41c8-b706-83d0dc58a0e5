package com.juyou.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账号充值Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface AccountRechargeMapper extends BaseMapper<AccountRecharge> {


    /**
     * 账号充值未生效列表
     * @param param
     * @return
     */
    List<AccountRechargeNotEffectiveListVo> notEffectiveList(AccountRechargeNotEffectiveParam param);

    /**
     * 等待到达时间放入二级库的列表
     * @param pendingMinutes
     * @return
     */
    List<AccountRecharge> pendingMinutesPutTwoChargeStageList(@Param("pendingMinutes") Integer pendingMinutes);

    /**
     * 代充查询
     * @param param
     * @return
     */
    List<ChargingInquiryListVo> chargingInquiryList(ChargingInquirySearchParam param);

    /**
     * 查询一级可领取账号
     * @param count
     * @param accountZone
     * @return
     */
    List<AccountRecharge> selectOneLevelAccount(@Param("count") Integer count,@Param("accountZone") String accountZone,
                                                @Param("amt") BigDecimal amt);

    /**
     * 查询二级可领取的账号
     * @param count
     * @param accountZone
     * @return
     */
    List<AccountRecharge> selectTwoLevelAccount(@Param("count") Integer count, @Param("accountZone") String accountZone,
                                                @Param("amt") BigDecimal amt, @Param("pendingMinutes") Integer pendingMinutes);

    /**
     * 查询我的剩余可领取账号
     * @param count
     * @param accountZone
     * @return
     */
    List<AccountRecharge> selectMyLevelAccount(@Param("count") Integer count,@Param("userId") Long userId,
                                               @Param("accountZone") String accountZone, @Param("amt") BigDecimal amt,
                                               @Param("pendingMinutes") Integer pendingMinutes);

    /**
     * 查询我的剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findMyResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 查询二级账号剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findTwoLevelResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 查询一级账号剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findOneLevelResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 查询业绩分页
     *
     * @param param
     * @return
     */
    List<PerformanceVoList> findPerformanceList(@Param("param") PerformanceSearchParam param);

    /**
     * 根据状态统计
     *
     * @param status
     * @return
     */
    public int countByStatus(@Param("status") String status,@Param("accountZone") String accountZone,@Param("idType") String idType);

    /**
     * 统计数量根据领取人和状态列表
     *
     * @param receiveUserId
     * @param statusList
     * @return
     */
    public int countByReceiveUserIdAndStatusList(@Param("receiveUserId") Long receiveUserId, @Param("statusList") List<String> statusList);


    /**
     * 我的充值作废率
     * @param userId
     * @return
     */
    BigDecimal getMyRechargeCancelRate(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 我今日已充值金额
     *
     * @param userId
     * @return
     */
//    select
//    (
//                (
//                select
//                        IFNULL(SUM(ar.recharge_amt),0)
//    from two_account_recharge ar
//    where ar.receive_user_id = #{userId}
//    and ar.account_zone = #{accountZone}
//                      <if test="idType != null and idType != ''">
//    and ar.id_type = #{idType}
//                      </if>
//    and ar.done_time BETWEEN #{startDate} and #{endDate}
//                )
//                        +
//                        (select
//    IFNULL(SUM(ar.recharge_amt),0)
//    from two_account_recharge ar
//    where ar.receive_user_id = #{userId}
//    and ar.account_zone = #{accountZone}
//                <if test="idType != null and idType != ''">
//    and ar.id_type = #{idType}
//                </if>
//    and ar.`status` = 2
//            )
//            ) as amt


    public BigDecimal getTodayRechargeAmount(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate,@Param("accountZone")String accountZone,@Param("idType")String idType);

    /**
     * 批量修改领取人
     *
     * @param ids
     * @return
     */
    public int batchUpdateReceiveUserByIds(@Param("ids") List<Integer> ids, @Param("receiveUser") String receiveUser,
                                           @Param("receiveUserId") Long receiveUserId);

    /**
     * 查询列表根据领取人和状态列表
     *
     * @param receiveUserId
     * @param statusList
     * @return
     */
    public List<AccountRecharge> selectByReceiveUserAndStatus(@Param("receiveUserId") Long receiveUserId, @Param("statusList") List<String> statusList);



    /**
     * 查询指定数量条数根据状态
     *
     * @param status
     * @param count
     * @return
     */
    public List<AccountRecharge> selectTopByStatus(@Param("status") String status, @Param("count")
    int count,@Param("accountZone") String accountZone,@Param("idType") String idType);



    public List<AccountRecharge> selectTopByStatusByWalletArea(@Param("status") String status, @Param("count")
    int count,@Param("accountZone") String accountZone,@Param("idType") String idType,String  walletArea);

    /**
     * 查询账号充值根据账号
     *
     * @param accountName
     * @return
     */
    public AccountRecharge getOne(String accountName);

    /**
     * 查询账号充值
     *
     * @param acid 账号充值主键
     * @return 账号充值
     */
    public AccountRecharge selectAccountRechargeByAcid(Integer acid);


    /**
     * 查询充值明细报表-分页用
     * <p>
     * --             (ar.`status` in (1,2) ${param.params.dataScope}
     * --             or
     * --             ar.`status` in (0,3,4) )
     *
     * @param param
     * @return
     */
    public List<AccountRecharge> selectAccountRechargeReportPage(@Param("param") AccountRechargePageParam param);

    /**
     * 查询账号充值列表-分页用
     *
     * @param param
     * @return
     */
    public List<AccountRecharge> selectAccountRechargePage(@Param("param") AccountRechargePageParam param);

    /**
     * 查询账号充值列表
     *
     * @param accountRecharge 账号充值
     * @return 账号充值集合
     */
    public List<AccountRecharge> selectAccountRechargeList(AccountRecharge accountRecharge);

    public AccountRecharge selectAccountRechargeByAccountName(@Param("accountName")String accountName);

    /**
     * 新增账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    public int insertAccountRecharge(AccountRecharge accountRecharge);

    /**
     * 修改账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    public int updateAccountRecharge(AccountRecharge accountRecharge);

    /**
     * 删除账号充值
     *
     * @param acid 账号充值主键
     * @return 结果
     */
    public int deleteAccountRechargeByAcid(Integer acid);

    /**
     * 批量删除账号充值
     *
     * @param acids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAccountRechargeByAcids(Integer[] acids);


    /**
     * 修改账号充值状态
     *
     * @param acid
     * @param status
     * @return
     */
    public int updateAccountRechargeStatus(@Param("acid") Integer acid, @Param("status") String status);

    /**
     * 批量修改账号充值状态
     *
     * @param acids
     * @param status
     * @return
     */
    public int batchUpdateAccountRechargeStatus(@Param("acids") List<Integer> acids, @Param("status") String status,
                                                @Param("receiveUser") String receiveUser, @Param("receiveTime") Date receiveTime,
                                                @Param("receiveUserId") Long receiveUserId, @Param("deptId") Long deptId);

    public int batchUpdateAccountRechargeStatusTwo(@Param("acids") List<Integer> acids, @Param("status") String status,
                                                @Param("receiveUser") String receiveUser, @Param("receiveTime") Date receiveTime,
                                                @Param("receiveUserId") Long receiveUserId, @Param("deptId") Long deptId);


    public List<AccountRechargeVo> selectAccountRechargeVoList(@Param("param") AccountRecharge param);


    public int  updatePutAccountRecharge(@Param("accountRecharge") AccountRecharge accountRecharge);


    /**
     * 获取一级库用户列表
     *
     * @return
     */
    public List<KeyValueVo> getlevel1UserList();

    /**
     * 获取二级库用户列表
     *
     * @return
     */
    public List<KeyValueVo> getlevel2UserList();
}
