<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.SourceGroupsMapper">

    <resultMap type="SourceGroups" id="SourceGroupsResult">
        <result property="id"    column="id"    />
        <result property="groupNumber"    column="group_number"    />
        <result property="status"    column="STATUS"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectSourceGroupsVo">
        select id, group_number, STATUS, create_by, update_by, created_time, update_time, is_deleted from source_groups
    </sql>

    <select id="selectSourceGroupsList" parameterType="SourceGroups" resultMap="SourceGroupsResult">
        <include refid="selectSourceGroupsVo"/>
        <where>
            <if test="groupNumber != null  and groupNumber != ''"> and group_number = #{groupNumber}</if>
            <if test="status != null "> and STATUS = #{status}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
    </select>

    <select id="selectSourceGroupsById" parameterType="Long" resultMap="SourceGroupsResult">
        <include refid="selectSourceGroupsVo"/>
        where id = #{id}
    </select>

    <insert id="insertSourceGroups" parameterType="SourceGroups" useGeneratedKeys="true" keyProperty="id">
        insert into source_groups
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupNumber != null and groupNumber != ''">group_number,</if>
            <if test="status != null">STATUS,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupNumber != null and groupNumber != ''">#{groupNumber},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
        </trim>
    </insert>

    <update id="updateSourceGroups" parameterType="SourceGroups">
        update source_groups
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupNumber != null and groupNumber != ''">group_number = #{groupNumber},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSourceGroupsById" parameterType="Long">
        delete from source_groups where id = #{id}
    </delete>

    <delete id="deleteSourceGroupsByIds" parameterType="String">
        delete from source_groups where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
