package com.juyou.system.service;

import com.juyou.system.utils.GoogleAuthenticator;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class GoogleAuthenticatorTest {

    @Test
    public void generateGoogleSecret(){
        //Google密钥
        String randomSecretKey = GoogleAuthenticator.getRandomSecretKey();
        String googleAuthenticatorBarCode = GoogleAuthenticator.getGoogleAuthenticatorBarCode(randomSecretKey, "zhangsan", "zhiqing");
        Map<String,Object> data = new HashMap<>();
        //Google密钥
        data.put("secret",randomSecretKey);
        //用户二维码内容
        data.put("secretQrCode",googleAuthenticatorBarCode);
        log.info("result:{}", data);
    }

    @Test
    public void checkCode(){
        String secret = "SDQESSBDNFRIDMTVL7LX2CIAMR5YVVI4";
        Long code = Long.valueOf("538463");
        boolean isTrue = GoogleAuthenticator.check_code(secret, code, System.currentTimeMillis());
        log.info("is:{}", isTrue);
    }

}
