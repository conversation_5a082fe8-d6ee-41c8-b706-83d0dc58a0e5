package com.juyou.system.mapper;

import java.util.List;
import com.juyou.system.domain.SellingGroups;

/**
 * 出售群信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
public interface SellingGroupsMapper
{
    /**
     * 查询出售群信息
     *
     * @param id 出售群信息主键
     * @return 出售群信息
     */
    public SellingGroups selectSellingGroupsById(Long id);

    /**
     * 查询出售群信息列表
     *
     * @param sellingGroups 出售群信息
     * @return 出售群信息集合
     */
    public List<SellingGroups> selectSellingGroupsList(SellingGroups sellingGroups);

    /**
     * 新增出售群信息
     *
     * @param sellingGroups 出售群信息
     * @return 结果
     */
    public int insertSellingGroups(SellingGroups sellingGroups);

    /**
     * 修改出售群信息
     *
     * @param sellingGroups 出售群信息
     * @return 结果
     */
    public int updateSellingGroups(SellingGroups sellingGroups);

    /**
     * 删除出售群信息
     *
     * @param id 出售群信息主键
     * @return 结果
     */
    public int deleteSellingGroupsById(Long id);

    /**
     * 批量删除出售群信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSellingGroupsByIds(Long[] ids);
}
