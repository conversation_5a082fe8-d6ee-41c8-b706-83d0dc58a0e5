package com.juyou.system.utils;

import cn.hutool.core.util.ObjUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * JuYou业务工具类
 */
public class JuYouBusinessUtil {


    /**
     * 计算平均成本
     *
     * @param buyAmt
     * @param rechargeAmt
     * @return
     */
    public static Double calculateAverageCost(Double buyAmt, Double rechargeAmt) {
        // 充值明细、出售明细详情：平均进价：完成充值时，核算平均进价；成本/充值金额=平均进价，两位小数
        if (ObjUtil.isNotNull(buyAmt) && ObjUtil.isNotNull(rechargeAmt)) {
            if (Double.compare(buyAmt, 0.0) != 0 && Double.compare(rechargeAmt, 0.0) != 0) {
                BigDecimal buyPrice = BigDecimal.valueOf(buyAmt).divide(BigDecimal.valueOf(rechargeAmt), 2, RoundingMode.HALF_UP);
                return buyPrice.doubleValue();
            }
            return null;
        }
        return null;
    }

}
