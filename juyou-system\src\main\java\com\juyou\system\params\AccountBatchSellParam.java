package com.juyou.system.params;

import com.juyou.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号出售-批量出售-param
 */
@Data
@ApiModel("AccountBatchSellParam")
public class AccountBatchSellParam implements Serializable {

    private static final long serialVersionUID = -7402438458597960168L;

    @ApiModelProperty("rechargeId")
    private Integer rechargeId;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("售卖人-客户")
    private String custName;

    @ApiModelProperty("出售单价")
    private Double sellPrice;

    @ApiModelProperty("账号区域")
    private String accountZone;

    @ApiModelProperty("出售金额")
    @Excel(name = "出售金额")
    private Double sellAmt;

}
