package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountRecharge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号充值统计明细-vo
 */
@Data
@ApiModel("AccountRechargeStatisticsDetailVo")
public class AccountRechargeStatisticsDetailVo extends AccountRecharge implements Serializable {

    private static final long serialVersionUID = -5602168133925879928L;

    @ApiModelProperty("充值人")
    private String rechargeUsername;

}
