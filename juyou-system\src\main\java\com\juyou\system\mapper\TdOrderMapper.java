package com.juyou.system.mapper;

import com.juyou.system.domain.TdOrder;

import java.util.List;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-17
 */
public interface TdOrderMapper 
{
    /**
     * 查询订单
     * 
     * @param id 订单ID
     * @return 订单
     */
    public TdOrder selectTdOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param tdOrder 订单
     * @return 订单集合
     */
    public List<TdOrder> selectTdOrderList(TdOrder tdOrder);

    /**
     * 新增订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    public int insertTdOrder(TdOrder tdOrder);

    /**
     * 修改订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    public int updateTdOrder(TdOrder tdOrder);

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    public int deleteTdOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTdOrderByIds(Long[] ids);
}
