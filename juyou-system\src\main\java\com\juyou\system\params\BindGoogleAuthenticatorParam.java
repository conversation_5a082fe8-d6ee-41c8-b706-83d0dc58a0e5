package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 绑定谷歌验证器-param
 */
@Data
@ApiModel("BindGoogleAuthenticatorParam")
public class BindGoogleAuthenticatorParam implements Serializable {

    private static final long serialVersionUID = 7004115532967466875L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户账号")
    private String username;

    @ApiModelProperty("谷歌动态验证码")
    private Long code;

    @ApiModelProperty("谷歌验证秘钥")
    private String googleSecret;

    @ApiModelProperty("谷歌密钥二维码内容")
    private String secretQrCode;



}
