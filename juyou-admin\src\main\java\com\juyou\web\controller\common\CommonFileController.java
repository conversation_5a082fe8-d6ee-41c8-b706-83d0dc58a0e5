package com.juyou.web.controller.common;

import cn.hutool.core.util.IdUtil;
import com.juyou.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 文件上传公共Controller
 *
 * <AUTHOR>
 */
@Api(value = "文件上传公共Controller", tags = "文件上传公共Controller")
@RestController
@RequestMapping("/commonFile")
public class CommonFileController {

    @Value("${file.upload.path}")
    private String fileUploadPath;

    @ApiOperation("上传文件")
    @PostMapping("/fileUpload")
    public AjaxResult fileUpload(@RequestParam("file") MultipartFile file) throws IOException {

        String fileSuffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        String fileName = IdUtil.simpleUUID();
        String filePath = fileUploadPath + fileName + fileSuffix;
        file.transferTo(new File(filePath));

        // FileVo vo = new FileVo();
        // vo.setFileName("/showFile/" + fileName + fileSuffix);
        AjaxResult success = AjaxResult.success();
        success.put("fileName", "/showFile/" + fileName + fileSuffix);
        return success;
    }

}
