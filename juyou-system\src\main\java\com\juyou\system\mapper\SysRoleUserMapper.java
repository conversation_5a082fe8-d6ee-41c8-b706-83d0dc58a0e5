package com.juyou.system.mapper;

import com.juyou.system.domain.SysRoleUser;

import java.util.List;

/**
 * 角色与用户关联表 数据层
 * 
 * <AUTHOR>
 */
public interface SysRoleUserMapper
{
    /**
     * 通过角色ID删除角色和用户关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleUserByRoleId(Long roleId);

    /**
     * 批量删除角色用户关联信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleUser(Long[] ids);

    /**
     * 批量新增角色用户信息
     * 
     * @param roleUserList 角色用户列表
     * @return 结果
     */
    public int batchRoleUser(List<SysRoleUser> roleUserList);

    /**
     * 获取角色下关联的用户ID列表
     * @param roleId
     * @return
     */
    public List<Long> selectUserListByRoleId(Long roleId);
}
