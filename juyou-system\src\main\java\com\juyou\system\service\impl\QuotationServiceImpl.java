package com.juyou.system.service.impl;

import com.juyou.system.domain.Quotation;
import com.juyou.system.mapper.QuotationMapper;
import com.juyou.system.service.IQuotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报价Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
public class QuotationServiceImpl implements IQuotationService
{
    @Autowired
    private QuotationMapper quotationMapper;

    /**
     * 查询报价
     *
     * @param id 报价主键
     * @return 报价
     */
    @Override
    public Quotation selectQuotationById(Long id)
    {
        return quotationMapper.selectQuotationById(id);
    }

    @Override
    public int openQuotation(Long[] ids) {
        int i = 0;
        Quotation quotation = new Quotation();
        for (Long id : ids) {
            quotation.setId(id);
            quotation.setOpen("0");
            int row = quotationMapper.updateQuotation(quotation);
            if (row > 0) {
                i++;
            }
        }
        if (i != ids.length) {
            throw new RuntimeException("批量修改失败");
        }
        return i;
    }

    @Override
    public int openList(Long[] ids) {
        int i = 0;
        Quotation quotation = new Quotation();
        for (Long id : ids) {
            quotation.setId(id);
            quotation.setOpen("1");
            int row = quotationMapper.updateQuotation(quotation);
            if (row > 0) {
                i++;
            }
        }
        if (i != ids.length) {
            throw new RuntimeException("批量修改失败");
        }
        return i;
    }
    /**
     * 查询报价列表
     *
     * @param quotation 报价
     * @return 报价
     */
    @Override
    public List<Quotation> selectQuotationList(Quotation quotation)
    {
        return quotationMapper.selectQuotationList(quotation);
    }

    @Override
    public List<Quotation> selectQuotationGroup() {
        return quotationMapper.selectQuotationGroup();
    }

    /**
     * 新增报价
     *
     * @param quotation 报价
     * @return 结果
     */
    @Override
    public int insertQuotation(Quotation quotation)
    {
        return quotationMapper.insertQuotation(quotation);
    }

    @Override
    public int batch(List<Quotation> quotation) {
        int i = 0;
        for (Quotation item:quotation) {
            quotationMapper.insertQuotation(item);
            i++;
        }
        if (i != quotation.size()) {
            throw new RuntimeException("批量新增失败");
        }
        return i;
    }

    @Override
    public int batchUpdateQuotation(List<Quotation> quotation) {
        int i = 0;
        for (Quotation item:quotation) {
            int row = quotationMapper.updateQuotation(item);
            if (row > 0) {
                i++;
            }
        }
        if (i != quotation.size()) {
            throw new RuntimeException("批量修改失败");
        }
        return i;
    }

    /**
     * 修改报价
     *
     * @param quotation 报价
     * @return 结果
     */
    @Override
    public int updateQuotation(Quotation quotation)
    {
        return quotationMapper.updateQuotation(quotation);
    }

    /**
     * 批量删除报价
     *
     * @param ids 需要删除的报价主键
     * @return 结果
     */
    @Override
    public int deleteQuotationByIds(Long[] ids)
    {
        return quotationMapper.deleteQuotationByIds(ids);
    }

    /**
     * 删除报价信息
     *
     * @param id 报价主键
     * @return 结果
     */
    @Override
    public int deleteQuotationById(Long id)
    {
        return quotationMapper.deleteQuotationById(id);
    }
}
