<template>
  <div class="app-container">
    <el-tabs v-model="tableTab">
      <el-tab-pane label="充值统计" name="third">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="来源群" prop="sourceGroup">
          <el-select v-model="queryParams.sourceGroup" clearable filterable placeholder="请选择来源群">
            <el-option
              v-for="item in selectSourceGroupList"
              :key="item.groupNumber"
              :label="item.groupNumber"
              :value="item.groupNumber">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="完成充值时间" prop="rechargeCompleteTime">
          <el-date-picker v-model="queryParams.rechargeCompleteTime" type="datetimerange" range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
          <el-button type="primary" size="mini" @click="today">今天</el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 充值来源群列表 -->
      <el-table v-loading="loading" :data="sourceGroupList" max-height="460" :summary-method="sourceGroupSummaries"
                show-summary>
        <el-table-column label="充值来源群" align="center" prop="sourceGroup"/>
        <el-table-column label="充值金额" align="center" prop="faceValue">
          <template slot-scope="scope">
            <el-button style="font-size: 18px;" type="text"  @click="goDetail(scope.row)">{{
                scope.row.faceValue
              }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="平均汇率" align="center" prop="avgPrice"></el-table-column>
        <el-table-column label="充值成本总额(CNY)" align="center" prop="buyAmt"></el-table-column>
      </el-table>
    </el-tab-pane>
      <el-tab-pane label="出售统计" name="first">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="95px">
          <el-form-item label="出售群" prop="sellChatgroupName">
            <el-select v-model="queryParams.sellChatgroupName" clearable filterable placeholder="请选择出售群">
              <el-option
                v-for="item in sellGroups"
                :key="item.groupNumber"
                :label="item.groupNumber"
                :value="item.groupNumber">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="出售日期" prop="sellTime">
            <el-date-picker v-model="queryParams.sellTime" type="datetimerange" range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
            <el-button type="primary" size="mini" @click="today">今天</el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

<!--        <el-row :gutter="10" class="mb8">-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" @click="handleExport">导出</el-button>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <el-table v-loading="loading" :data="sellChatgroupList" :summary-method="getSellSummaries" show-summary>
          <el-table-column label="出售群" align="center" prop="sellChatgroupName"/>
          <el-table-column label="出售ID数量" align="center" prop="sellCount"/>
          <el-table-column label="出售面值" align="center" prop="cardBalance">
            <template slot-scope="scope">
              <el-button style="font-size: 18px;" type="text" @click="cardBalanceClick(scope.row)">{{
                  scope.row.cardBalance
                }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="平均价格" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.cardBalance != 0">{{(scope.row.sellAmt/scope.row.cardBalance).toFixed()}}</span>
              <span v-else>0</span>
            </template>
          </el-table-column>
          <el-table-column label="出售总金额（CNY）" align="center" prop="sellAmt"/>
          <el-table-column label="充值成本总额(CNY)" align="center" prop="buyAmt"></el-table-column>


          <el-table-column label="利润总额(CNY)" align="center" prop="grossProfit"/>

        </el-table>
      </el-tab-pane>
      <el-tab-pane label="作废统计" name="second">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="95px">
<!--          <el-form-item label="出售群" prop="sellChatgroupName">-->
<!--            <el-input v-model="queryParams.sellChatgroupName" placeholder="请输入出售群" clearable-->
<!--                      @keyup.enter.native="handleQuery"/>-->
<!--          </el-form-item>-->

          <el-form-item label="作废时间" prop="cancelTime">
            <el-date-picker v-model="queryParams.cancelTime" type="datetimerange" range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
            <el-button type="primary" size="mini" @click="today">今天</el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="cancelList" :summary-method="getCancelSummaries" show-summary>
          <el-table-column label="作废类型" align="center" prop="cancelType"/>
          <el-table-column label="作废ID数量" align="center" prop="cancelTotal"/>
          <el-table-column label="ID余额" align="center" prop="cancelAmt">
            <template slot-scope="scope">
              <el-button style="font-size: 18px;" type="text" @click="cancelAmtClick">
                {{
                  scope.row.cancelAmt
                }}
              </el-button>
            </template>
          </el-table-column>
<!--          <el-table-column label="亏损总额（CNY）" align="center" prop="lossAmt"></el-table-column>-->
        </el-table>
      </el-tab-pane>

    </el-tabs>

    <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" /> -->




    <!-- 明细弹窗 -->
    <el-dialog :title="statisticalDetailTile" :visible.sync="statisticalDetailspen" :close-on-click-modal="false"
               :close-on-press-escape="false" width="980px" append-to-body>
      <el-form :model="statisticalDetailParams" ref="statisticalDetailParams" size="small" :inline="true"
               label-width="65px">
        <el-form-item v-if="this.statisticalDetailParams.type === '4'" label="账号" prop="account">
          <el-input v-model="statisticalDetailParams.account" placeholder="请输入账号" clearable
                    @keyup.enter.native="statisticalDetailQuery"/>
        </el-form-item>

        <el-form-item v-else label="账号:" prop="accountName">
          <el-input v-model="statisticalDetailParams.accountName" placeholder="请输入账号" clearable
                    @keyup.enter.native="statisticalDetailQuery"/>
        </el-form-item>

        <el-form-item label="实际充值金额:" prop="cardBalance" label-width="100">
          <el-input v-model="statisticalDetailParams.cardBalance" placeholder="请输入实际充值金额" clearable
                    @keyup.enter.native="statisticalDetailQuery"/>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="statisticalDetailQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetDetailQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="statisticalDetailList" max-height="360"
                v-if="this.statisticalDetailParams.type === '4'"
                :summary-method="getDetailCardRechargeSummaries" show-summary>
        <el-table-column label="充值人" align="center" prop="createBy"/>
        <el-table-column label="账号" align="center" prop="account">
          <template slot-scope="scope">
            {{
              scope.row.account?scope.row.account.substr(0, 4) +
              '***' + scope.row.account.substr(scope.row.account.length - 4, scope.row.account.length):''
            }}
          </template>
        </el-table-column>
        <el-table-column label="密码" align="center" prop="accountPwd"/>
        <el-table-column label="礼品卡" align="center" prop="giftCard"/>
        <el-table-column label="面值" align="center" prop="faceValue"></el-table-column>
        <el-table-column label="单价" align="center" prop="buyPrice"></el-table-column>
        <el-table-column label="执行时间" align="center" prop="executionTime"/>
      </el-table>

      <el-table v-else v-loading="loading" :data="statisticalDetailList" max-height="360"
                :summary-method="getDetailSummaries" show-summary>
        <el-table-column label="充值人" align="center" prop="rechargeUsername"/>
        <el-table-column label="出售人" align="center" prop="sellUsername"/>
        <el-table-column label="账号" align="center" prop="accountName"></el-table-column>
        <el-table-column label="密码" align="center" prop="accountPwd"></el-table-column>
        <!-- <el-table-column label="应充值金额" align="center" prop="shouldAmt"></el-table-column> -->
        <el-table-column label="进价" align="center" prop="buyPrice"></el-table-column>
        <el-table-column label="成本金额" align="center" prop="buyAmt"></el-table-column>

        <!-- 充值作废实际充值金额 -->
        <el-table-column v-if="statisticalDetailParams.type === '2'" label="实际充值金额" align="center"
                         prop="rechargeAmt"></el-table-column>

        <el-table-column v-else label="实际充值金额" align="center" prop="cardBalance"></el-table-column>


        <el-table-column label="出售单价" align="center" prop="sellPrice"></el-table-column>
        <el-table-column label="出售金额" align="center" prop="sellAmt"></el-table-column>
        <el-table-column label="出售时间" align="center" prop="sellTime" width="155"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSellChatgroupList,
  sellStatisticsGetUnsoldTotal,
  sellStatisticsGetCancelList,
  getRechargeCancelAmtDetailList,
  getSellCancelAmtDetailList,
  getSellGroupDetailList,
  getGiftCardRechargeStatisticsList,
  getGiftCardRechargeStatisticsDetailList
} from "@/api/newSystem/sellStatistics";
import Cookies from "js-cookie";
import { getSourceGroupsList } from '@/api/newSystem/notEffective'
import { getTheDetailsOfTheTopUpSettings, sellingGroupsList } from '@/api/newSystem/substitutionSettings'

export default {
  name: "SellStatistics",
  dicts: ['recharge_status','sell_status'],
  data() {
    return {
      sellGroups:[],
      tableTab: 'third',//tab切换
      sourceGroupList: [],//来源群列表
      selectSourceGroupList: [],//来源群列表
      statisticalDetailTile: '',//统计标题
      statisticalDetailspen: false,//统计明细弹窗
      statisticalDetailList: [],//统计明细弹窗列表数组
      statisticalDetailParams: {},//统计详情查询条件
      remainingActualRechargeTotal: [],
      remainingCostTotal: 0,
      sellChatgroupName: undefined,
      sellTime: undefined,
      cancelTime: undefined,
      rechargeCompleteTime: undefined,
      //充值记录列表列表
      sellChatgroupList: [],
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10000,
        cancelTime:[],
        sellTime:[],
        rechargeCompleteTime:[],
      },
      // 充值账号详情弹窗
      sellcardReportDetailsVisible: false,
      sellcardReportDetailsForm: {},
      giftCardRechargeRecordList: [],//详情弹窗列表
      sellChatgroupSum: [],
      cancelChatgroupSum: [],
      cancelList: [],//作废列表
    };
  },
   async created() {
     sellingGroupsList().then(e=>{
       this.sellGroups = e.rows
     })
     getSourceGroupsList().then(e=>{
      this.selectSourceGroupList = e.rows
    })

    await this.yesterday()
    this.sellStatisticsGetCancelList();//获取作废列表
    this.getGiftCardRechargeStatisticsList();//礼品卡充值来源群统计
    this.getSellStatisticsGetUnsoldTotal();
    this.into();
  },
  computed: {
    totalProfit() {
      if (this.sellChatgroupSum.length > 0 && this.cancelChatgroupSum.length > 0) {
        let sellItme = this.sellChatgroupSum.slice(-1);//出售利润总额
        let canselItme = this.cancelChatgroupSum.slice(-1);//作废亏损总额
        if (isNaN(sellItme)) {
          sellItme = 0;
        }
        if (isNaN(canselItme)) {
          canselItme = 0;
        }
        return (Number(sellItme) - Number(canselItme)).toFixed(2);
      }
      return '0'
    }
  },
  methods: {
    cancelAmtClick(){
      if (this.queryParams.cancelTime && this.queryParams.cancelTime.length){
        this.$router.push(`/applereg/rechargeCardRecordList?status=4&startTime=${this.queryParams.cancelTime[0]}&endTime=${this.queryParams.cancelTime[1]}`)
      }else {
        this.$router.push(`/applereg/rechargeCardRecordList?status=4`)
      }
    },
    cardBalanceClick(row){
      if (this.queryParams.sellTime && this.queryParams.sellTime.length){
        this.$router.push(`/applereg/sellInquiries?group=${row.sellChatgroupName}&startTime=${this.queryParams.sellTime[0]}&endTime=${this.queryParams.sellTime[1]}`)
      }else {
        this.$router.push(`/applereg/sellInquiries?group=${row.sellChatgroupName}`)
      }
    },
    goDetail(row){
      if (this.queryParams.rechargeCompleteTime && this.queryParams.rechargeCompleteTime.length){
        this.$router.push(`/applereg/cardRecord?group=${row.sourceGroup}&startTime=${this.queryParams.rechargeCompleteTime[0]}&endTime=${this.queryParams.rechargeCompleteTime[1]}`)
      }else {
        this.$router.push(`/applereg/cardRecord?group=${row.sourceGroup}`)
      }

    },
    into() {
      this.getList();//获取销售列表
    },
    /**明细弹窗*/
    async gotoReportDialog(type, row) {//sourceGroup
      try {
        this.statisticalDetailParams = {};
        this.$set(this.statisticalDetailParams, 'type', type);
        // type 1:代表查询出售详情  2：代表作废充值详情 3：代表出售作废详情
        if (type === '1') {//查询出售详情
          this.statisticalDetailTile = '群出售统计明细';
          this.$set(this.statisticalDetailParams, 'sellChatgroupName', row.sellChatgroupName);
        } else if (type === '2') {
          this.statisticalDetailTile = '充值作废统计明细';
        } else if (type === '3') {
          this.statisticalDetailTile = '出售作废统计明细';
        } else if (type === '4') {
          this.statisticalDetailTile = '充值来源群统计明细';
          this.$set(this.statisticalDetailParams, 'sourceGroup', row.sourceGroup);
        }
        const detail_res = await this.netDetail();
        this.statisticalDetailList = detail_res.rows;
        this.statisticalDetailspen = true;
      } catch (err) {
        console.error(err);
        this.statisticalDetailspen = false;
      }
    },
    /**弹窗查询按钮事件*/
    async statisticalDetailQuery() {
      try {
        const detail_res = await this.netDetail();
        this.statisticalDetailList = detail_res.rows;
      } catch (err) {
        console.error(err);
      }
    },
    netDetail() {
      if (this.statisticalDetailParams.type === '1') {//查询出售详情
        const request = {
          ...this.statisticalDetailParams,
          startSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[0] : '',//结束完成时间
          endSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[1] : '',//开始完成时间
        }
        return getSellGroupDetailList(request);
      } else if (this.statisticalDetailParams.type === '2') {//查询作废充值详情
        const request = {
          ...this.statisticalDetailParams,
          cancelStartDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[0] : '',//作废开始时间
          cancelEndDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[1] : '',//作废结束时间
        }
        return getRechargeCancelAmtDetailList(request);
      } else if (this.statisticalDetailParams.type === '3') {//查询出售作废详情
        const request = {
          ...this.statisticalDetailParams,
          cancelStartDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[0] : '',//作废开始时间
          cancelEndDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[1] : '',//作废结束时间
        }
        return getSellCancelAmtDetailList(request);
      } else if (this.statisticalDetailParams.type === '4') {//来源群统计详情
        const request = {
          ...this.statisticalDetailParams,
          startRechargeCompleteTime: this.queryParams.rechargeCompleteTime ? this.queryParams.rechargeCompleteTime[0] : '',
          endRechargeCompleteTime: this.queryParams.rechargeCompleteTime ? this.queryParams.rechargeCompleteTime[1] : '',
          cancelStartDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[0] : '',//作废开始时间
          cancelEndDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[1] : '',//作废结束时间
        }
        return getGiftCardRechargeStatisticsDetailList(request);
      }
    },
    async yesterday() {
      const res = await getTheDetailsOfTheTopUpSettings()
      let getDate = res.data.date
      // 昨天
      const date = new Date();
      const enddate = new Date(date);
      enddate.setDate(date.getDate() - 1);
      const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
      // 今天
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.$set(this.queryParams, 'cancelTime', [`${yesterday} ${getDate || '00:00:00'}`, `${today} ${getDate || '00:00:00'}`]);
      this.$set(this.queryParams, 'sellTime', [`${yesterday} ${getDate || '00:00:00'}`, `${today} ${getDate || '00:00:00'}`]);
      this.$set(this.queryParams, 'rechargeCompleteTime', [`${yesterday} ${getDate || '00:00:00'}`, `${today} ${getDate || '00:00:00'}`]);
    },
    async today() {
      const res = await getTheDetailsOfTheTopUpSettings()
      let getDate = res.data.date
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      const end = new Date();
      end.setDate(end.getDate() + 1);
      const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
      this.$set(this.queryParams, 'cancelTime', [`${today} ${getDate || '00:00:00'}`, `${endStr} ${getDate || '00:00:00'}`]);
      this.$set(this.queryParams, 'sellTime', [`${today} ${getDate || '00:00:00'}`, `${endStr} ${getDate || '00:00:00'}`]);
      this.$set(this.queryParams, 'rechargeCompleteTime', [`${today} ${getDate || '00:00:00'}`, `${endStr} ${getDate || '00:00:00'}`]);
    },
    async sellStatisticsGetCancelList() {
      const request = {
        cancelStartDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[0] : null,//作废开始时间
        cancelEndDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[1] : null,//作废结束时间
      }
      const res = await sellStatisticsGetCancelList(request);
      this.cancelList = res.data;
    },
    /**礼品卡充值来源群统计*/
    async getGiftCardRechargeStatisticsList() {
      const request = {
        startRechargeCompleteTime: this.queryParams.rechargeCompleteTime ? this.queryParams.rechargeCompleteTime[0] : null,// 充值完成开始时间
        endRechargeCompleteTime: this.queryParams.rechargeCompleteTime ? this.queryParams.rechargeCompleteTime[1] : null, // 充值完成结束时间
        cancelStartDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[0] : null,//作废开始时间
        cancelEndDate: this.queryParams.cancelTime ? this.queryParams.cancelTime[1] : null,//作废结束时间
        sourceGroup: this.queryParams.sourceGroup,//来源群
      }
      const res = await getGiftCardRechargeStatisticsList(request);
      this.sourceGroupList = res.rows;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/new/system/sellStatistics/export', {
        ...this.queryParams
      }, `sellStatistics${new Date().getTime()}.xlsx`)
    },
    async getSellStatisticsGetUnsoldTotal() {
      const total_res = await sellStatisticsGetUnsoldTotal();
      if (total_res.code === 200) {
        this.remainingActualRechargeTotal = total_res.data.list;
        this.remainingCostTotal = total_res.data.remainingCostTotal;
      }
    },

    /**转译售卡状态 */
    formatStatus(status) {
      let data = ''
      switch (status) {
        case '1':
          data = '未生效';
          break;
        case '2':
          data = '待出售';
          break;
        case '3':
          data = '已出售';
          break;
        case '4':
          data = '作废';
          break;
      }
      return data
    },
    /** 查询未生效账号列表 */
    async getList() {
      try {
        this.loading = true;
        const request = {
          ...this.queryParams,
          startSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[0] : null,//结束完成时间
          endSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[1] : null,//开始完成时间
        };
        const sellChatgroupList_res = await getSellChatgroupList(request);
        if (sellChatgroupList_res.code === 200) {
          this.sellChatgroupList = sellChatgroupList_res.rows;
          this.total = sellChatgroupList_res.total;
          this.loading = false;
        }

      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.sellStatisticsGetCancelList();
      this.getGiftCardRechargeStatisticsList();
      Cookies.set('sell_statistics', JSON.stringify(this.queryParams));
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 100,
        accountName: null,
        accountZone: null,
        createBy: null,
        createTime: null,
        exceed: false,
        operator: null,
        params: null,
        remark: null,
        searchValue: null,
        updateBy: null,
        updateTime: null,
      }
      this.handleQuery();
    },
    /**查询重置操作*/
    resetDetailQuery() {
      this.statisticalDetailParams.cardBalance = '';
      this.statisticalDetailParams.account = '';
      this.statisticalDetailParams.accountName = '';
      this.statisticalDetailParams.giftCard = '';
      this.statisticalDetailQuery();
      return

      if (this.statisticalDetailParams.type === '1') {//如果是群出售详情 不能去掉群

        const type = this.statisticalDetailParams.type;
        const sellChatgroupName = this.statisticalDetailParams.sellChatgroupName;
        this.statisticalDetailParams = {};
        this.$set(this.statisticalDetailParams, 'type', type)
        this.$set(this.statisticalDetailParams, 'sellChatgroupName', sellChatgroupName);
      } else if (this.statisticalDetailParams.type === '4') {//如果充值来源群详情 不能去掉群
        const type = this.statisticalDetailParams.type;
        const sourceGroup = this.statisticalDetailParams.sourceGroup;
        this.statisticalDetailParams = {};
        this.$set(this.statisticalDetailParams, 'type', type)
        this.$set(this.statisticalDetailParams, 'sourceGroup', sourceGroup);
      } else {
        const type = this.statisticalDetailParams.type;
        this.statisticalDetailParams = {};
        this.$set(this.statisticalDetailParams, 'type', type)
      }
      this.statisticalDetailQuery();
    },
    // 查看详情弹窗
    async handleDetail(row) {
      const detail_res = await sellcardReportDetail(row.rechargeId);
      if (detail_res.code === 200) {
        this.giftCardRechargeRecordList = detail_res.data.giftCardRechargeRecordList;
        this.sellcardReportDetailsVisible = true;
        this.sellcardReportDetailsForm = row;

      }
    },
    detailCancel() {
      this.rechargeDetailsVisible = false;
    },
    detailCopyToClip() {
      const content = `账号：${this.rechargeDetailsForm.accountName} 密码：${this.rechargeDetailsForm.accountPwd}`;
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    /**出售合计*/
    getSellSummaries(param) {
      const notTotals = [] //不需要小计的列数组
      const sum = this.commonSummaries(param, notTotals, '出售小计');
      this.sellChatgroupSum = sum;
      return sum;
    },
    /**作废合计*/
    getCancelSummaries(param) {
      const notTotals = [] //不需要小计的列数组
      const sum = this.commonSummaries(param, notTotals, '作废小计');
      this.cancelChatgroupSum = sum;
      return sum;
    },
    /**来源群合计*/
    sourceGroupSummaries(param) {
      const notTotals = [0, 2] //不需要小计的列数组
      return this.commonSummaries(param, notTotals, '小计');
    },
    /**详情合计*/
    getDetailSummaries(param) {
      const notTotals = [1, 2, 3, 4, 7, 10] //不需要小计的列数组
      return this.commonSummaries(param, notTotals, '小计');
    },
    /**礼品卡充值详情来源群统计合计*/
    getDetailCardRechargeSummaries(param) {
      const notTotals = [1, 2, 3, 5] //不需要小计的列数组
      return this.commonSummaries(param, notTotals, '小计');
    },
    commonSummaries(param, notTotals, amountName) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = amountName || '小计';
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += ' ';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    needStyle(column) {
      return "color:red"

    },
  }
};
</script>
<style>
.total {
  line-height: 80px;
}

.total .el-row {
  margin-bottom: 25px;
  color: #606266;
  font-size: 22px;
  font-weight: 700;
}
</style>
