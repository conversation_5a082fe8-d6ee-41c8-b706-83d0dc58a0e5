package com.juyou.web.controller.report;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.domain.vo.AccountSellReportDetailVo;
import com.juyou.system.params.AccountSellPageParam;
import com.juyou.system.service.IAccountSellService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import com.juyou.system.utils.JuYouBusinessUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 账号出售报表-controller
 */
@Api(value = "出售明细报表", tags = "出售明细报表")
@RestController
@RequestMapping("/system/sellcardReport")
public class AccountSellReportController extends BaseController {

    @Autowired
    private IAccountSellService iAccountSellService;
    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @ApiOperation("出售明细报表列表")
    @Log(title = "出售明细报表-出售明细报表列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcardReport:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountSell> list(AccountSellPageParam param) {
        startPage();
        List<AccountSell> list = iAccountSellService.selectAccountReportList(param);
        return getDataTable(list);
    }


    @ApiOperation("导出出售明细报表")
    @Log(title = "出售明细报表-导出出售明细报表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcardReport:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountSellPageParam param) {
        List<AccountSell> list = iAccountSellService.selectAccountSellList(param);
        if (CollUtil.isNotEmpty(param.getIds())) {
            List<Integer> ids = param.getIds();
            list = new ArrayList<>();
            for (Integer id : ids) {
                AccountSell item = this.iAccountSellService.selectAccountSellByRechargeId(id);
                list.add(item);
            }
        }
        ExcelUtil<AccountSell> util = new ExcelUtil<AccountSell>(AccountSell.class);
        util.exportExcel(response, list, "出售明细报表");
    }

    @ApiOperation("获取详情")
    @Log(title = "出售明细报表-获取详情", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcardReport:detail')")
    @PostMapping("/getDetail/{rechargeId}")
    public ResultData<AccountSellReportDetailVo> getDetail(@PathVariable Integer rechargeId) {
        if (ObjUtil.isNull(rechargeId)) {
            throw new ServiceException("rechargeId不能为空");
        }

        AccountSellReportDetailVo vo = new AccountSellReportDetailVo();

        AccountSell sell = this.iAccountSellService.selectAccountSellByRechargeId(rechargeId);
        BeanUtil.copyProperties(sell, vo);

        GiftCardRechargeRecord record = new GiftCardRechargeRecord();
        record.setAccount(sell.getAccountName());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(record);
        vo.setGiftCardRechargeRecordList(list);

        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getCardBalance()));

        return ResultUtil.success(vo);
    }


}
