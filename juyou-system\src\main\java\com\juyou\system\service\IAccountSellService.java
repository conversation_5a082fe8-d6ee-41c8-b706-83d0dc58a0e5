package com.juyou.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * sellcardService接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface IAccountSellService extends IService<AccountSell> {
    /**
     * 修改出售群
     *
     * @param param
     * @return
     */
    Integer updateSellChatgroupName(@RequestBody AccountSellUpdateChatgroupNameParam param);

    /**
     * 批量核对
     *
     * @param param
     * @return
     */
    Integer batchWriteOff(@RequestBody AccountSellBatchWriteOffParam param);

    /**
     * 获取账号销售-已出售列表
     *
     * @param param
     * @return
     */
    List<AccountSell> getAccountSellSoldList(AccountSellSoldListSearchParam param);

    /**
     * 获取账号销售-已出售账号列表
     *
     * @param param
     * @return
     */
    List<AccountSellVo> getAccountSellSoldReportList(AccountSellPendingSaleSearchParam param);

    /**
     * 获取账号销售-待出售账号列表
     *
     * @param param
     * @return
     */
    List<AccountSellVo> getAccountSellPendingSaleList(AccountSellPendingSaleSearchParam param);

    /**
     * 账号出售可领取的系统剩余账号列表
     *
     * @return
     */
    List<AccountSellResidualAccountVoList> getSystemResidualAccountList(String accountZone, String idType);

    /**
     * 我今日已出售金额
     *
     * @param userId
     * @return
     */
    BigDecimal getTodayAmountSold(Long userId, String accountZone, String idType);



    BigDecimal   getSellVoidedBalances(Long userId);


    /**
     *  带时间查询的出售作废率
     *
     * @return
     */
    BigDecimal   getSellVoidedBalancesDate(Long userId,DateRangeParam dateRangeParam);
    /**
     * 待领取总金额
     *
     * @return
     */
    BigDecimal getAwaitReceiveTotalAmount(String accountZone, String idType);

    /**
     * 账号销售列表统计分页
     *
     * @param param
     * @return
     */
    List<SellChatgroupVo> getSellChatgroupList(SellChatgroupParam param);

    /**
     * 未出售合计(累计)
     *
     * @return
     */
    AccountUnsoldTotalVo getUnsoldTotal();

    /**
     * 转交账号
     *
     * @param param
     * @return
     */
    int transferAccount(AccountTransferAccountParam param);

    /**
     * 转交账号
     *
     * @param count
     * @param userId
     * @param userName
     * @param deptId
     * @return
     */
    public int receiveAccount(int count, List<Double> cardBalanceList, Long userId, String userName, Long deptId, String chargeStage);


    public int DesignationReceiveAccount(int count, List<Double> cardBalanceList, Long userId, String userName, Long deptId, String walletArea);

    /**
     * 更新账号出售完成时间分钟数(账号在未生效持续的时间,单位分钟)
     *
     * @return
     */
    public int updateNotEffectiveCompletedTimeMinutesNum();

    /**
     * 未生效账号列表
     *
     * @param param
     * @return
     */
    public List<AccountSellVo> notEffectiveList(AccountSellNotEffectiveParam param);

    /**
     * 获取待生效账号出售列表
     *
     * @return
     */
    public List<AccountSell> selectToBeEffectiveList();


    /**
     * 获取账号出售对象
     * @param acid 账号充值id
     * @return
     */
    AccountSell selectByAcid(Integer acid);

    /**
     * 查询sellcard
     *
     * @param rechargeId sellcard主键
     * @return sellcard
     */
    public AccountSell selectAccountSellByRechargeId(Integer rechargeId);


    /**
     * 出售明细报表列表
     *
     * @param param
     * @return
     */
    public List<AccountSell> selectAccountReportList(AccountSellPageParam param);

    /**
     * 查询sellcard列表
     *
     * @param param sellcard
     * @return sellcard集合
     */
    public List<AccountSell> selectAccountSellList(AccountSellPageParam param);

    /**
     * 查询903sellcard列表
     *
     * @param param sellcard
     * @return sellcard集合
     */
    public List<AccountSell> selectAccountSellListByAnchor(SellAnchorVo param);

    /**
     * 新增sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int insertAccountSell(AccountSell accountSell);

    /**
     * 修改sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int updateAccountSell(AccountSell accountSell);

    /**
     * 修改903sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int updateAccountSellByAnchor(Integer[] ids);
    /**
     * 恢复903sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int updateAccountSellByAnchorRecovery(Integer id);



    public int putAccountRechargeOne(List<AccountSell> accountSell ,String level);
    /**
     * 修改sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int updateAccountSellMin(Integer[] ids);

    /**
     * 批量修改
     *
     * @param list
     * @return
     */
    public int batchAccountSell(List<AccountSell> list);

    /**
     * 批量修改账号出售状态
     *
     * @param rechargeIds 主键id集合
     * @param status      状态
     * @return
     */
    public int batchUpdateAccountSellStatus(@Param("rechargeIds") List<Integer> rechargeIds, @Param("status") String status);

    /**
     * 账号出售作废
     *
     * @param param
     * @return
     */
    public int cancel(AccountSellCancelParam param);

    /**
     * 批量删除sellcard
     *
     * @param rechargeIds 需要删除的sellcard主键集合
     * @return 结果
     */
    public int deleteAccountSellByRechargeIds(Integer[] rechargeIds);

    /**
     * 根据注册id集合删除
     * @param acidList
     * @return
     */
    int deleteByAcidList(List<Integer> acidList);

    /**
     * 删除sellcard信息
     *
     * @param rechargeId sellcard主键
     * @return 结果
     */
    public int deleteAccountSellByRechargeId(Integer rechargeId);

    /**
     * 置顶
     *
     * @param accountSell
     * @return
     */
    public int updatePinned(AccountSell accountSell);

    public List<AccountRechargeVo> selectAccountRechargeVoList(String idType);

    /**
     * @param accountZone 账户区域
     * @param idType
     * @param walletArea  钱包区域
     * @return
     **/
    public List<AccountSellResidualAccountVoList> getSystemResidualAccountLeiSheList(String accountZone, String idType, String walletArea);

    public int putAccountRecharge(List<AccountSell> accountSell ,String level);

    public int returnAccountSell( AccountSell accountSell);

    /**
     * 出售作废恢复
     * @param accountSell
     *  @return
     * */
    public   int  restore( AccountSell accountSell);
    /**
     * 出售查询
     * @param param
     * @return
     **/
    public List<AccountSell> selectAccountSellListAll(AccountSellPageParam param);


    public int putOldAccountRechargeToNewTwoAccountRecharge(List<AccountSell> accountSell ,String level);

}
