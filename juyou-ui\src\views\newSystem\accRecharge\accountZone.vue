<template>
  <div class="app-container">
    <div>
<!--      <h2><strong>请选择账户充值国家/地区</strong></h2>-->
<!--      <el-table :data="table" border style="width: 100%">-->
<!--        <el-table-column prop="dictLabel" label="国家/地区" align="center">-->
<!--          <template slot-scope="scope">-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--      </el-table>-->
      <div>
        <el-button v-hasPermi="['system:oneLevelAccountRecharge:list']" style="width: 150px;height: 50px;font-size: 18px;margin: 20px" type="primary" v-for="item in table" @click="gotoAccount(item.dictLabel)">{{ item.dictValue?'('+item.dictValue+')'+item.dictLabel:item.dictLabel }}</el-button>
      </div>

      <el-button v-hasPermi="['system:twoLevelAccountRecharge:list']" style="width: 150px;height: 50px;font-size: 18px;margin: 20px" type="primary" v-for="item in table" @click="gotoLevel2Account(item.dictLabel)">{{ item.dictValue?'('+item.dictValue+')'+item.dictLabel:item.dictLabel }}</el-button>
      <br />
      <el-button v-hasPermi="['system:accRecharge:import']" @click="showDialog" style="width: 240px;height: 50px;font-size: 18px;margin: 20px" type="primary">账号导入</el-button>
    </div>
    <el-dialog title="账号导入" :visible.sync="dialogVisible" width="600px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" label-width="100px">
        <el-form-item label="账号区域" prop="accountZone">
          <el-select v-model="uploadForm.accountZone" placeholder="请选择">
            <el-option v-for="(item,index) in accZone" :key="index" :label="item.dictLabel" :value="item.dictLabel">
              {{ item.dictLabel }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号类型" prop="idType">
          <el-select v-model="uploadForm.idType" placeholder="请选择" @change="zoneChange">
            <el-option value="0" label="苹果代充">苹果代充</el-option>
          </el-select>
        </el-form-item>
        <div class="upload-container">
          <el-upload
            :disabled="!uploadForm.idType"
            :headers="headers"
            :show-file-list="false"
            class="upload-demo"
            drag
            accept=".xls,.xlsx"
            :action="uploadaccFilePath"
            :on-success="handleUploadSuccess"
            multiple>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处</div>
            <div class="el-upload__text" style="margin-top: 10px;"><el-link @click.stop="downloadTemplate" type="primary" style="font-size: 12px">模板下载</el-link></div>
            <div class="el-upload__tip" slot="tip">只能上传不超过 3MB 的.xls,.xlsx文件</div>
          </el-upload>

        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {listAccRechargeZone} from "@/api/newSystem/accRecharge";
import Link from "@/layout/components/Sidebar/Link";
import { getToken } from '@/utils/auth'
import { getDicts } from '@/api/newSystem/dict/data'

export default {
  name: "accountZone",
  components: {Link},
  data() {
    return {
      disZone: true,
      uploadaccFilePath: undefined,
      uploadForm: {},
      dialogVisible:false,
      table: [],
      accZone: [],
      zoneList: [],
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    }
  },
  created() {
    this.into();
  },
  methods: {
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("导入失败，请重试");
      this.$modal.closeLoading();
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.info(this.fileList);
    },
    // 删除文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止删除。function(file, fileList)
    //上传文件事件
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    zoneChange() {
      if (this.uploadForm.accountZone == undefined) {
        this.$modal.msgError("请先选择国家");
        this.uploadForm.idType = undefined;
        return;
      }
      if (this.uploadForm.idType != undefined) {
        this.uploadaccFilePath = process.env.VUE_APP_BASE_API + "/new/system/accRecharge/importData?accountZone=" + this.uploadForm.accountZone+"&idType=" + this.uploadForm.idType;
      }
      if (this.uploadaccFilePath != undefined) {
        this.disZone = false;
      }
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      if (res.code === 200) {
        this.$modal.confirm(res.msg).then(function () {
        })
        this.dialogVisible = false
        this.into();//重新刷新列表
      } else {
        this.$modal.msgError(res.msg || '导入失败');
      }
    },
    downloadTemplate() {
      this.download('/new/system/accRecharge/import', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    },
    downloadTemplateById() {
      this.download('/new/system/accRecharge/importById', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    },
    showDialog(){
      this.dialogVisible = true
    },
    into() {
      getDicts("account_zone").then(response => {
          this.accZone = response.data;
        }
      );
      let obj = {
        idType: '0'
      }
      listAccRechargeZone(obj).then(res => {
        this.table = res.rows;
      })
    },
    gotoAccount(accountZone) {
      if (this.$store.state.user.userInfo.dept.deptId) {
        if (this.$store.state.user.userInfo.dept.deptId == 200) {
          //二级
          this.$router.push({ path: '/applereg/accLevel2Recharge/' + accountZone })
        }else {
          // 一级
          this.$router.push({ path: '/applereg/accRecharge/' + accountZone })
        }
      }
    },
    gotoLevel2Account(accountZone){
        //二级
        this.$router.push({path: '/applereg/accLevel2Recharge/'+accountZone})
    }
  }
}
</script>

<style scoped lang="scss">


.download-btn {
  margin-left: 5px;
  margin-top: -23px;
}

.home {
  display: flex;
  flex-direction: row;

  .box_itme {
    display: flex;
    margin: 10px;
    padding: 10px;
    background: #1890ff;
    color: #fff;
    width: 100px;
    height: 60px;
    align-items: center;
    justify-content: center;
    border-radius: 2px
  }

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
