package com.juyou.system.domain.vo;

import com.juyou.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 出售群Vo
 */
@ApiModel("SellChatgroupVo")
@Data
public class SellChatgroupVo implements Serializable {

    private static final long serialVersionUID = -8386860576850154528L;

    @Excel(name = "出售群")
    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @Excel(name = "出售数量")
    @ApiModelProperty("出售数量")
    private Integer sellCount;

    @Excel(name = "出售面值(实际充值金额)")
    @ApiModelProperty("出售面值(实际充值金额)")
    private BigDecimal cardBalance;

    @Excel(name = "成本总额(CNY)")
    @ApiModelProperty("成本总额(CNY)")
    private BigDecimal buyAmt;

    @Excel(name = "毛利总额(CNY)")
    @ApiModelProperty("毛利总额(CNY)")
    private BigDecimal sellAmt;

    @Excel(name = "利润总额(CNY)")
    @ApiModelProperty("利润总额(CNY)")
    private BigDecimal grossProfit;
}
