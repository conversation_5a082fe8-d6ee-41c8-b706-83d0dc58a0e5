<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.PerformanceMapper">


    <select id="getRemainingActualRechargeTotal" resultType="com.juyou.system.domain.vo.AccountZoneAmtVo">
        SELECT sum(r.recharge_amt - IFNULL(s.card_balance, 0)) as rechargeAmt, r.account_zone as accountZone
        from two_account_recharge r
                 left join two_account_sell s on r.acid = s.acid and s.`status` = 3
        where r.`status` in (0, 2, 3)
        GROUP BY r.account_zone

    </select>
    <select id="getRemainingCostTotal" resultType="java.math.BigDecimal">

        SELECT (SELECT sum(b.buy_amt)
                FROM two_account_recharge a
                         LEFT JOIN two_gift_card_recharge_record b on a.account_name = b.account
                WHERE a.`status` in (0, 1, 2, 3))
                   - (SELECT sum(buy_amt) FROM two_account_sell WHERE `status` = 3)

    </select>


    <select id="getRechargeCancelRate" resultType="java.math.BigDecimal">
        SELECT (SELECT SUM(buy_amt) FROM two_account_recharge WHERE STATUS = 4) /
               NULLIF((SELECT SUM(buy_amt) FROM two_account_recharge), 0) * 100
    </select>
    <select id="getSellCancelRate" resultType="java.math.BigDecimal">
        SELECT (SELECT SUM(buy_amt) FROM two_account_sell WHERE STATUS = 4) /
               NULLIF((SELECT SUM(buy_amt) FROM two_account_sell), 0) * 100

    </select>
    <select id="getDepartmentList" resultType="com.juyou.system.domain.vo.DepartmentVo">
        SELECT dept_id                                                                       AS deptId,
               GROUP_CONCAT(DISTINCT receive_user_id ORDER BY receive_user_id SEPARATOR ',') AS userId
        FROM (SELECT su.dept_id, ar.receive_user_id
              FROM two_account_recharge ar
                       LEFT JOIN sys_user su on su.user_id = ar.receive_user_id
              UNION ALL
              SELECT ase.dept_id, ase.receive_user_id
              FROM two_account_sell ase
                       LEFT JOIN sys_user su on su.user_id = ase.receive_user_id) AS combined_data
        GROUP BY dept_id
        HAVING userId IS NOT NULL
           AND userId != '';

    </select>
    <select id="getRechargeCancelRateDetail" resultType="java.math.BigDecimal">
        SELECT
        (
        SELECT
        SUM(ar.recharge_amt)
        FROM
        two_account_recharge ar
        <where>
            status = 4
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ) / (
        SELECT
        SUM(ar.recharge_amt)
        FROM
        two_account_recharge ar
        <where>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="startTime !=null and endTime !=null  ">
                AND ( ar.cancel_date BETWEEN #{startTime} AND #{endTime}
                OR ar.done_time BETWEEN #{startTime} AND #{endTime} )
            </if>
        </where>
        )*100 AS v;


    </select>
    <select id="getSellCancelRateDetail" resultType="java.math.BigDecimal">
        SELECT
        (
        SELECT
        SUM(buy_amt)
        FROM
        two_account_sell ar
        <where>
            status = 4
            <if test="userName != null and userName != ''">
                AND ar.receive_user LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ) / (
        SELECT
        SUM(buy_amt)
        FROM
        two_account_sell ar
        <where>
            <if test="userName != null and userName != ''">
                AND ar.receive_user LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and ( ar.cancel_date BETWEEN #{startTime} AND #{endTime}
                OR ar.done_time BETWEEN #{startTime} AND #{endTime} )
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and receive_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and sell_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ) *100 AS v;


    </select>
    <select id="getRechargeNum" resultType="java.lang.Integer">
        SELECT COUNT(*) AS status_count
        FROM two_account_recharge ar
        <where>
            status = 3
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and done_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>

    </select>
    <select id="getRechargeCostTotal" resultType="java.math.BigDecimal">
        <!--        SELECT sum(ar.buy_amt) AS status_count-->
        <!--        FROM two_account_recharge ar-->
        <!--        where 1=1-->
        <!--        <if test="idUser != null and idUser != '' ">-->
        <!--            AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')-->
        <!--        </if>-->
        <!--            <if test="deptId != null and deptId != ''">-->
        <!--                AND ar.dept_id = #{deptId}-->
        <!--            </if>-->
        <!--            <if test="userId != null and userId != '' ">-->
        <!--                AND ar.receive_user_id = #{userId}-->
        <!--            </if>-->
        <!--        <if test="idUser != null and idUser != '' ">-->
        <!--            AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')-->
        <!--        </if>-->
        <!--            <if test="startTime !=null and endTime !=null  ">-->
        <!--                AND ( ar.cancel_date BETWEEN #{startTime} AND #{endTime}-->
        <!--                OR ar.done_time BETWEEN #{startTime} AND #{endTime} )-->
        <!--            </if>-->

        SELECT
        SUM(gc.face_value)
        FROM
        two_account_recharge tar
        LEFT JOIN two_gift_card_recharge_record gc on gc.account=tar.account_name
        WHERE
        1 = 1
        <if test="idUser != null and idUser != '' ">
            AND tar.id_user LIKE CONCAT('%', #{idUser}, '%')
        </if>
        <if test="deptId != null and deptId != ''">
            AND tar.dept_id = #{deptId}
        </if>
        <if test="userId != null and userId != '' ">
            AND tar.receive_user_id = #{userId}
        </if>
        <if test="startTime !=null and endTime !=null  ">
            AND tar.done_time BETWEEN #{startTime} AND #{endTime}
        </if>


    </select>
    <select id="getRechargeCancelNum" resultType="java.lang.Integer">
        SELECT COUNT(*) AS status_count
        FROM two_account_recharge ar
        <where>
            status = 4
            <if test="userName != null and userName != ''">
                AND ar.receive_user LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getRechargeCancelCostTotal" resultType="java.math.BigDecimal">
        SELECT SUM(IFNULL(ar.buy_amt,0))
        FROM two_account_recharge ar
        left join two_account_sell as asell on ar.acid = asell.acid
        <where>
            ar.status = 4
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and ar.cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>

        </where>
    </select>
    <select id="getSellNum" resultType="java.lang.Integer">
        SELECT COUNT(*) AS status_count
        FROM two_account_sell ar
        <where>
            status = 3
            <if test="userName != null and userName != ''">
                AND ar.receive_user LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getSellTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM(buy_amt) AS status_count
        FROM two_account_sell ar
        <where>
            status = 3
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getSellCancelNum" resultType="java.lang.Integer">
        SELECT COUNT(*) AS status_count
        FROM two_account_sell ar
        <where>
            status = 4
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getSellCancelTotalAmount" resultType="java.math.BigDecimal">
        SELECT SUM(buy_amt) AS status_count
        FROM two_account_sell ar
        <where>
            status = 4
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime !=null and endTime !=null  ">
                and cancel_date BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getPerformanceDetailedVo" parameterType="com.juyou.system.params.CompanyPerformanceQueryParam"
            resultType="com.juyou.system.domain.vo.PerformanceDetailedVo">
        SELECT
        ar.account_zone AS accountZone,
        SUM(
        IF
        ( ase.`status` !=3 and ase.`status` !=4
        <if test="deptId != null and deptId != ''">
            AND ar.dept_id = #{deptId}
        </if>
        <if test="userId != null and userId != ''">
            AND ar.receive_user_id = #{userId}
        </if>
        <if test="startTime != null and endTime != null">
            AND ar.done_time BETWEEN #{startTime} AND #{endTime}
        </if>
        , ar.recharge_amt, 0 )) AS rechargeTotal,
        SUM(
        IF
        ( ar.`status` = 4
        <if test="deptId != null and deptId != ''">
            AND ar.dept_id = #{deptId}
        </if>
        <if test="userId != null and userId != ''">
            AND ar.receive_user_id = #{userId}
        </if>
        <if test="startTime != null and endTime != null">
            AND ar.cancel_date BETWEEN #{startTime} AND #{endTime}
        </if>
        , ar.recharge_amt, 0 )) AS rechargeCancelTotal,
        SUM(
        IF
        ( ase.`status` !=4 and ar.`status` = 3
        <if test="deptId != null and deptId != ''">
            AND ase.dept_id = #{deptId}
        </if>
        <if test="userId != null and userId != ''">
            AND ase.receive_user_id = #{userId}
        </if>
        <if test="startTime != null and endTime != null">
            AND ar.done_time BETWEEN #{startTime} AND #{endTime}
        </if>
        , ase.card_balance, 0 )) AS sellTotal,
        SUM(
        IF
        ( ase.`status` = 4
        <if test="deptId != null and deptId != ''">
            AND ase.dept_id = #{deptId}
        </if>
        <if test="userId != null and userId != ''">
            AND ase.receive_user_id = #{userId}
        </if>
        <if test="startTime != null and endTime != null">
            AND ase.cancel_date BETWEEN #{startTime} AND #{endTime}
        </if>,
        ase.card_balance, 0 )) AS sellCancelTotal
        FROM
        two_account_recharge ar
        LEFT JOIN two_account_sell ase ON ase.acid = ar.acid
        GROUP BY
        ar.account_zone
    </select>

    <select id="getPerformanceReportIsDetailed" resultType="com.juyou.system.domain.vo.PerformanceReportIsDetailedVo">
        SELECT
        IFNULL(SUM(CASE WHEN #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND ar.done_time BETWEEN #{startTime}
        AND #{endTime} THEN ar.buy_amt ELSE 0 END),0) AS rechargeCostTotal,
        IFNULL( SUM(CASE WHEN #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND ae.sell_time BETWEEN #{startTime}
        AND #{endTime} THEN ae.buy_amt ELSE 0 END),0) AS sellCostTotal,

        COALESCE(SUM(CASE WHEN ar.`status` = 4 AND #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND
        ar.cancel_date BETWEEN #{startTime} AND #{endTime} THEN ar.buy_amt ELSE 0 END), 0) AS rechargeCancelCostTotal,
        COALESCE((SELECT COUNT(*) FROM two_account_recharge WHERE `status` = 3 AND #{startTime} IS NOT NULL AND
        #{endTime} IS NOT NULL AND done_time BETWEEN #{startTime} AND #{endTime}), 0) AS rechargeCount,

        COALESCE(SUM(CASE WHEN ae.`status` = 4 AND #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND
        ae.cancel_date BETWEEN #{startTime} AND #{endTime} THEN ae.buy_amt ELSE 0 END), 0) AS sellCancelCostTotal,
        COALESCE((SELECT COUNT(*) FROM two_account_sell WHERE `status` = 3 AND #{startTime} IS NOT NULL AND #{endTime}
        IS NOT NULL AND sell_time BETWEEN #{startTime} AND #{endTime}), 0) AS sellCount,

        COALESCE((SELECT COUNT(*) FROM two_account_recharge WHERE `status` = 4 AND #{startTime} IS NOT NULL AND
        #{endTime} IS NOT NULL AND cancel_date BETWEEN #{startTime} AND #{endTime}), 0) AS rechargeCancelCount,
        COALESCE((SELECT COUNT(*) FROM two_account_sell WHERE `status` = 4 AND #{startTime} IS NOT NULL AND #{endTime}
        IS NOT NULL AND cancel_date BETWEEN #{startTime} AND #{endTime}), 0) AS sellCancelCount,

        COALESCE(SUM(CASE WHEN ar.`status` = 4 AND #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND
        ar.cancel_date BETWEEN #{startTime} AND #{endTime} THEN IFNULL(ar.buy_amt, 0) ELSE 0 END) /
        NULLIF(SUM(IFNULL(ar.buy_amt, 0)), 0) * 100, 0) AS rechargeCancelRate,
        COALESCE(SUM(CASE WHEN ae.`status` = 4 AND #{startTime} IS NOT NULL AND #{endTime} IS NOT NULL AND
        ae.cancel_date BETWEEN #{startTime} AND #{endTime} THEN IFNULL(ae.buy_amt, 0) ELSE 0 END) /
        NULLIF(SUM(IFNULL(ae.buy_amt, 0)), 0) * 100, 0) AS sellCancelRate
        FROM
        two_account_recharge ar
        LEFT JOIN two_account_sell ae ON ar.acid = ae.acid
        <where>
            1=1
            <if test="userName != null and userName != ''">
                AND ar.receive_user LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != '' ">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="idUser != null and idUser != '' ">
                AND ar.id_user LIKE CONCAT('%', #{idUser}, '%')
            </if>
        </where>
    </select>
    <select id="getBounceRates" resultType="java.math.BigDecimal">
        select IFNULL(((SELECT SUM(buy_amt) FROM two_refund_records) /
                       IFNULL((select sum(recharge_amt) FROM two_account_recharge where `status` in (2, 3)), 1)), 0) *
               100 as bounceRate
    </select>

    <select id="getBounceRatesDetail" resultType="java.math.BigDecimal">
        select IFNULL((
        SELECT SUM(buy_amt)
        FROM two_refund_records ar
        <where>
            1=1
            <if test="deptId != null and deptId != ''">
                AND ar.dept_id = #{deptId}
            </if>
            <if test="userId != null and userId != ''">
                AND ar.receive_user_id = #{userId}
            </if>
            <if test="startTime != null and endTime != null">
                AND ar.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ) / IFNULL((
        SELECT SUM(recharge_amt)
        FROM two_account_recharge
        WHERE `status` in (2,3)
        ), 1), 0) * 100 as bounceRate
    </select>


</mapper>
