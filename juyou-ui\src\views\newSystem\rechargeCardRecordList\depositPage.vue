<template>
  <div style="max-width: 1200px;margin: 20px auto">
    <el-radio-group style="margin-bottom: 20px" v-model="selectType">
      <el-radio-button label="0">充值</el-radio-button>
      <el-radio-button label="2">操作日志</el-radio-button>
    </el-radio-group>
    <div v-if="selectType === '0'">
      <el-form ref="form" :rules="rules" :model="form" label-width="120px" label-position="right" :inline="true">
        <div style="display:flex;justify-content: space-between">
          <el-form-item label="账号：" prop="accountName">
            <el-input v-model="form.accountName" placeholder="请输入账号名称"
                      disabled/>
          </el-form-item>
          <el-form-item label="密码：" prop="accountPwd">
            <el-input v-model="form.accountPwd" placeholder="请输入密码"
                      disabled/>
          </el-form-item>
          <el-form-item label="区域：" prop="accountZone">
            <el-input v-model="form.accountZone" disabled placeholder="请输入ID余额"/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="ID余额：" prop="rechargeAmt">
            <el-input v-model="form.rechargeAmt" disabled placeholder="请输入ID余额"/>
          </el-form-item>
          <el-form-item label="成本金额：" prop="buyAmt">
            <el-input disabled v-model="form.buyAmt" placeholder="请输入成本金额"/>
          </el-form-item>
          <el-form-item label="一级充值人：" prop="primaryCharger">
            <el-input disabled v-model="form.primaryChargerName" placeholder=""/>
          </el-form-item>
        </div>

        <div style="display:flex;justify-content: space-between">
          <el-form-item label="二级充值人：" prop="secondaryChargerName">
            <el-input disabled v-model="form.secondaryChargerName" placeholder=""/>
          </el-form-item>
          <el-form-item label="退回原因：" prop="backReason" v-if ="form.hasBack==='Y'">
            <el-input disabled v-model="form.backReason" placeholder="请输入退回原因"/>
          </el-form-item>
          <el-form-item label="充值完成时间：" prop="doneTime">
            <el-input disabled v-model="form.doneTime" placeholder="请输入充值完成时间"/>
          </el-form-item>
        </div>
        <div style="display:flex;justify-content: space-between">
          <el-form-item label="ID归属业务员：" prop="idUser">
            <el-input v-model="form.idUser" placeholder="请输入ID归属业务员"/>
          </el-form-item>
        </div>
        <el-table
          :data="tableCode"
          :row-class-name="tableRowClassName"
          style="width: 100%">
          <el-table-column
            prop="giftCard"
            label="卡密"
            width="180">
            <template slot-scope="{row}">
              <el-input v-model="row.giftCard"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="faceValue"
            label="面值"
            width="180">
            <template slot-scope="{row}">
              <el-input v-model="row.faceValue"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="buyPrice"
            label="汇率(价格)">
            <template slot-scope="{row}">
              <el-input v-model="row.buyPrice"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="sourceGroup"
            label="来源群">
            <template slot-scope="{row}">
              <el-select v-model="row.sourceGroup" filterable placeholder="请选择来源群">
                <el-option
                  v-for="item in sourceGroupList"
                  :key="item.groupNumber"
                  :label="item.groupNumber"
                  :value="item.groupNumber">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="pledgeThirtyMinutes"
            label="是否质押30分钟">
            <template slot-scope="{row}">
              <el-checkbox true-label="1" false-label="0" v-model="row.pledgeThirtyMinutes"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            prop="pledgeThirtyMinutes"
            label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="delItem(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
          <div style="float: right">
            <el-button style="margin-top: 10px" type="primary" @click="addItem()">添加一行</el-button>
            <br />
            <el-button style="margin-top: 10px" type="primary" @click="submitData()">保存</el-button>
            <el-button @click="copyToClip(`${form.accountName}----${form.accountPwd}`)">一键复制账号</el-button>
          </div>
      </el-form>
    </div>
    <div v-else-if="selectType === '2'">
      <operation-logs></operation-logs>
    </div>
  </div>

</template>
<script>
import {
  chargingInquiryDetail,
  chargingInquiryEdit,
  oneLevelAccountRechargeDetail,
  topUpYourAccount
} from '@/api/newSystem/accRecharge'
import ca from 'element-ui/src/locale/lang/ca'
import OperationLogs from '@/views/newSystem/accRecharge/operationLogs.vue'
import { getSourceGroupsList } from '@/api/newSystem/notEffective'

export default {
  name: "rechargeModification",
  components: { OperationLogs },
  data() {
    return {
      rules: {
        idUser: [
          { required: true, message: '请输入ID归属业务员', trigger: 'blur' },
        ],
      },
      errCol:[],
      selectType:'0',
      form:{
        accountZone:'',
        accountName:'',
        accountPwd:'',
        rechargeAmt:'',
        secondaryCharger:'',
        doneTime:'',
        buyAmt:'',
        primaryCharger:'',
        idUser:'',
        acid:'',
        hasBack:''
      },
      cardCode:'',
      tableCode:[],
      cardList:[],
      sourceGroupList:[]
    }
  },
  watch:{
    tableCode: {
      handler: function(val, oldVal){
        if (val.length){
          this.cardCode = val.map(e=>{
            return `${e.giftCard} ${e.faceValue}*${e.buyPrice} #${e.sourceGroup}`
          }).join('\n')
          let sum  = val.reduce((a,b)=>{
           return  a+ +b.faceValue * +b.buyPrice
          },0)
          let rechargeAmt  = val.reduce((a,b)=>{
            return  a+ +b.faceValue
          },0)
          if (isNaN(sum) || isNaN(rechargeAmt)){
            this.$modal.msgError('面值或汇率必须为数字')
            return
          }
          let arr = []
          val.forEach((e,i)=>{
            let str = val.findIndex((item,index)=>{
              if (index === i){
                return false
              }else {
                return  item.giftCard === e.giftCard
              }

            })
            if (str !== -1){
              arr.push(i)
            }
            this.errCol = Array.from(new Set(arr))
            console.log(this.errCol)
          })
          this.form.buyAmt = sum
          this.form.rechargeAmt = rechargeAmt
        }
      },
      deep:true,
      immediate: true
    },
  },
  async mounted() {
    if (this.$route.params.id){
      const res = await chargingInquiryDetail(this.$route.params.id)
      this.form.accountName = res.data.accountName
      this.form.accountPwd = res.data.accountPwd
      this.form.accountZone = res.data.accountZone
      this.form.rechargeAmt = res.data.rechargeAmt || 0
      this.form.doneTime = res.data.doneTime
      this.form.secondaryChargerName = res.data.secondaryChargerName
      this.form.idUser = res.data.idUser
      this.form.primaryChargerName = res.data.primaryChargerName
      if (res.data.chargeStage === '1'){
        this.rules = {}
      }
      this.form.buyAmt = res.data.buyAmt
      this.form.acid = this.$route.params.id
      this.form.hasBack = res.data.hasBack
      this.form.backReason= res.data.backReason
      this.tableCode = res.data.giftCardRechargeRecordList
    }
    const res = await getSourceGroupsList();
    this.sourceGroupList = res.rows

  },
  methods:{
    tableRowClassName({row, rowIndex}) {
      if (this.errCol.includes(rowIndex)) {
        return 'warning-row';
      }
      return '';
    },
    delItem(e){
      this.tableCode.splice(e.$index, 1);
    },
    addItem(){
      this.tableCode.push({
        faceValue:0,
        buyPrice:0
      })
    },
    async submitData(){
      if (this.errCol.length) return this.$modal.msgError('请清除重复卡号')
      this.$refs["form"].validate(async valid => {
        if (valid) {
          let flag = null
          if (this.tableCode.length){
            this.tableCode.forEach(e=>{
              if (isNaN(e.faceValue)){
                flag = '面值必须为数字'
                return
              }
              if (isNaN(e.buyPrice)){
                flag = '汇率必须为数字'
                return
              }
              e.pledgeThirtyMinutes = e.pledgeThirtyMinutes === '1'? '1':'0'
            })
          }else {
            flag = '请输入正确的卡密代码'
          }
          this.form.cardList = this.tableCode

          const res = await chargingInquiryEdit(this.form)
          this.$modal.msgSuccess('修改成功')
          this.$router.go(-1)
          this.$tab.closePage()
        }

        })


    },
    resetForm(){

    },
    /**一键复制账号和密码*/
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    async recharge(e){
      this.tableCode.forEach(e=>{
        e.pledgeThirtyMinutes = e.pledgeThirtyMinutes === '1'? '1':'0'
      })
      let form = {
        ...this.form,
        cardList:this.tableCode,
        status:e
      }
      const res = await topUpYourAccount(form)
    },
    checkCartAmt(){

    }
  }
}
</script>



<style>
.el-table .warning-row {
  background: #ff977f;
}
</style>
