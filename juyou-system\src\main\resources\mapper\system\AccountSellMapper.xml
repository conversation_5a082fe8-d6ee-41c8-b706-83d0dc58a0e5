<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.AccountSellMapper">

    <resultMap type="AccountSell" id="AccountSellResult">
        <result property="rechargeId" column="recharge_id"/>
        <result property="acid" column="acid"/>
        <result property="accountName" column="account_name"/>
        <result property="accountPwd" column="account_pwd"/>
        <result property="accountZone" column="account_zone"/>
        <result property="walletArea" column="wallet_area"/>
        <result property="idType" column="id_type"/>
        <result property="spareCode" column="spare_code"/>
        <result property="status" column="status"/>
        <result property="writeOffStatus" column="write_off_status"/>
        <result property="completedTimeMinutesNum" column="completed_time_minutes_num"/>
        <result property="cardBalance" column="card_balance"/>
        <result property="buyPrice" column="buy_price"/>
        <result property="buyAmt" column="buy_amt"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="sellAmt" column="sell_amt"/>
        <result property="shouldAmt" column="should_amt"/>
        <result property="chargeStage" column="charge_stage"/>
        <result property="sellChatgroupName" column="sell_chatgroup_name"/>
        <result property="custName" column="cust_name"/>
        <result property="sellTime" column="sell_time"/>
        <result property="remark" column="remark"/>
        <result property="cancelDate" column="cancel_date"/>
        <result property="cancelReason" column="cancel_reason"/>
        <result property="cancelImgs" column="cancel_imgs"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="doneTime" column="done_time"/>
        <result property="doneUser" column="done_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="receiveUser" column="receive_user"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="pinnedStatus" column="pinned_status"/>
        <result property="isUse" column="is_use"/>
        <result property="useTime" column="use_time"/>
        <result property="hasEdit" column="has_edit"/>
    </resultMap>
    <sql id="selectAccountSellLevelVo">
        select ase.recharge_id,
               ase.acid,
               ase.card_balance,
               ase.account_name,
               ase.account_pwd,
               ase.account_zone,
               ase.wallet_area,
               ase.id_type,
               ase.spare_code,
               ase.status,
               ase.write_off_status,
               ase.completed_time_minutes_num,
               ase.card_balance,
               ase.buy_price,
               ar.charge_stage,
               ase.buy_amt,
               ase.should_amt,
               ase.sell_price,
               ase.sell_amt,
               ase.sell_chatgroup_name,
               ase.cust_name,
               ase.sell_time,
               ase.remark,
               ase.cancel_date,
               ase.cancel_reason,
               ase.cancel_imgs,
               ase.create_time,
               ase.create_by,
               ase.done_time,
               ase.done_user,
               ase.update_time,
               ase.update_by,
               ase.receive_time,
               ase.receive_user,
               ase.receive_user_id,
               ase.dept_id,
               ase.pinned_status,
               ase.is_use,
               ase.use_time

        from two_account_sell ase
    </sql>

    <sql id="selectAccountSellVo">
        select ase.recharge_id,
               ase.acid,
               ase.card_balance,
               ase.account_name,
               ase.account_pwd,
               ase.account_zone,
               ase.wallet_area,
               ase.id_type,
               ase.spare_code,
               ase.status,
               ase.write_off_status,
               ase.completed_time_minutes_num,
               ase.card_balance,
               ase.buy_price,
               ase.buy_amt,
               ase.should_amt,
               ase.sell_price,
               ase.sell_amt,
               ase.sell_chatgroup_name,
               ase.cust_name,
               ase.sell_time,
               ase.remark,
               ase.cancel_date,
               ase.cancel_reason,
               ase.cancel_imgs,
               ase.create_time,
               ase.create_by,
               ase.done_time,
               ase.done_user,
               ase.update_time,
               ase.update_by,
               ase.receive_time,
               ase.receive_user,
               ase.receive_user_id,
               ase.dept_id,
               ase.pinned_status
        from two_account_sell ase
    </sql>
    <sql id="selectOldAccountSellVo">
        select ase.recharge_id,
               ase.acid,
               ase.card_balance,
               ase.account_name,
               ase.account_pwd,
               ase.account_zone,
               ase.wallet_area,
               ase.id_type,
               ase.spare_code,
               ase.status,
               ase.write_off_status,
               ase.completed_time_minutes_num,
               ase.card_balance,
               ase.buy_price,
               ase.buy_amt,
               ase.should_amt,
               ase.sell_price,
               ase.sell_amt,
               ase.sell_chatgroup_name,
               ase.cust_name,
               ase.sell_time,
               ase.remark,
               ase.cancel_date,
               ase.cancel_reason,
               ase.cancel_imgs,
               ase.create_time,
               ase.create_by,
               ase.done_time,
               ase.done_user,
               ase.update_time,
               ase.update_by,
               ase.receive_time,
               ase.receive_user,
               ase.receive_user_id,
               ase.dept_id,
               ase.pinned_status
        from account_sell ase
    </sql>
    <sql id="selectAccountSellListVo">
        select
            ase.recharge_id as rechargeId,
            ase.acid as acid,
            ase.account_name as accountName,
            ase.account_pwd as accountPwd,
            ase.account_zone as accountZone,
            ase.id_type as idType,
            ase.spare_code as spareCode,
            ase.status as status,
            ase.write_off_status as writeOffStatus,
            ase.completed_time_minutes_num as completedTimeMinutesNum,
            ase.card_balance as cardBalance,
            ase.buy_price as buyPrice,
            ase.buy_amt as buyAmt,
            ase.should_amt as shouldAmt,
            ase.sell_price as sellPrice,
            ase.sell_amt as sellAmt,
            ase.sell_chatgroup_name as sellChatgroupName,
            ase.cust_name as custName,
            ase.sell_time as sellTime,
            ase.remark as remark,
            ase.create_time as createTime,
            ase.create_by as createBy,
            ase.done_time as doneTime,
            ase.done_user as doneUser,
            ase.update_time as updateTime,
            ase.update_by as updateBy,
            ase.receive_time as receiveTime,
            ase.receive_user as receiveUser,
            ase.receive_user_id as receiveUserId,
            ase.dept_id as deptId,
            ase.pinned_status as pinnedStatus
        from two_account_sell ase
    </sql>


    <select id="selectAccountSellByRechargeId" parameterType="Integer" resultMap="AccountSellResult">
        <include refid="selectAccountSellVo"/>
        where recharge_id = #{rechargeId}
    </select>

    <select id="selectToBeEffectiveList" resultMap="AccountSellResult">
        <include refid="selectAccountSellVo"/>
        where
        ase.status = 1
        <![CDATA[
        and DATE_ADD(ase.create_time , INTERVAL 120 HOUR) <= NOW()
        ]]>
    </select>
    <select id="notEffectiveList"
            parameterType="com.juyou.system.params.AccountSellNotEffectiveParam"
            resultType="com.juyou.system.domain.vo.AccountSellVo">
        select
        ase.recharge_id as rechargeId,
        ase.acid as acid,
        ase.account_name as accountName,
        ase.account_pwd as accountPwd,
        ase.account_zone as accountZone,
        ase.id_type as idType,
        ase.spare_code as spareCode,
        ase.status as status,
        ase.write_off_status as writeOffStatus,
        ase.card_balance as cardBalance,
        ase.buy_price as buyPrice,
        ase.buy_amt as buyAmt,
        ase.should_amt as shouldAmt,
        ase.sell_price as sellPrice,
        ase.sell_amt as sellAmt,
        ase.sell_chatgroup_name as sellChatgroupName,
        ase.cust_name as custName,
        ase.sell_time as sellTime,
        ase.remark as remark,
        ase.create_time as createTime,
        ase.create_by as createBy,
        ase.done_time as doneTime,
        ase.done_user as doneUser,
        ase.update_time as updateTime,
        ase.update_by as updateBy,
        ase.receive_time as receiveTime,
        ase.receive_user as receiveUser,
        ase.receive_user_id as receiveUserId,
        ase.dept_id as deptId,
        ase.pinned_status as pinnedStatus,
        ar.charge_stage as chargeStage,
        ar.primary_charger as primaryCharger,
        ar.secondary_charger as secondaryCharger,
        (case when ar.charge_stage = 1 then #{param.firstRechargeEffectTime} - ase.completed_time_minutes_num else #{param.secondRechargeEffectTime} - ase.completed_time_minutes_num end) as surplusMinutesNum
        from two_account_sell ase
        left join two_account_recharge ar on ase.acid = ar.acid
        <where>
            AND ase.`status` = 1
            <if test=" param.chargeStage != null and param.chargeStage != '' ">
                and ar.charge_stage = #{param.chargeStage}
            </if>
            <if test=" param.remainingTime != null and param.remainingTime != '' ">
                and surplusMinutesNum &lt; #{param.remainingTime}
            </if>
            <if test=" param.idBalances != null and param.idBalances.size() > 0 ">
                and cardBalance in
                <foreach collection="param.idBalances" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" param.firstRechargeEffectTime != null and param.firstRechargeEffectTime != '' ">
                and ase.completed_time_minutes_num &lt; (case when ar.charge_stage = 1 then #{param.firstRechargeEffectTime} else #{param.secondRechargeEffectTime} end)
            </if>
            <if test=" param.accountName != null and param.accountName != '' ">
                and ase.account_name LIKE CONCAT( '%', #{param.accountName}, '%' )
            </if>
            <if test=" param.accountZone != null and param.accountZone != '' ">
                and ase.account_zone LIKE CONCAT( '%', #{param.accountZone}, '%' )
            </if>
            <if test="param.startEffect != null and param.endEffect != null">
                and ase.completed_time_minutes_num BETWEEN #{param.startEffect} and #{param.endEffect}
            </if>
            <if test=" param.operator != null and param.operator != '' ">
                AND ase.create_by = #{param.operator}
            </if>
        </where>
        ORDER BY surplusMinutesNum DESC,ase.acid ASC
    </select>
    <select id="selectAccountSellList"
            parameterType="com.juyou.system.params.AccountSellPageParam" resultMap="AccountSellResult">
        <include refid="selectAccountSellLevelVo"/>
        LEFT JOIN sys_user u ON u.user_id = ase.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN two_account_recharge ar ON ar.account_name = ase.account_name
        <where>
            <if test="param.status == null or param.status == ''">
                and ase.`status`  in(2,3)
            </if>
            ${param.params.dataScope}
            <if test="param.accountName != null and param.accountName != ''">
                and ase.account_name LIKE CONCAT('%',#{param.accountName},'%')
            </if>
            <if test="param.status != null and param.status != ''">
                and ase.`status` = #{param.status}
            </if>
            <if test="param.accountZone != null and param.accountZone != ''">
                and ase.account_zone LIKE CONCAT('%',#{param.accountZone},'%')
            </if>
            <if test="param.walletArea != null and param.walletArea != ''">
                and ase.wallet_area LIKE CONCAT('%',#{param.walletArea},'%')
            </if>
            <if test="param.idType != null and param.idType != ''">
                and ase.id_type = #{param.idType}
            </if>
            <if test="param.startShouldAmt != null and param.startShouldAmt != 0 and param.endShouldAmt != null and param.endShouldAmt != 0">
                and ase.should_amt BETWEEN #{param.startShouldAmt} and #{param.endShouldAmt}
            </if>
            <if test="param.startReceiveTime != null and param.endReceiveTime != null ">
                and ase.receive_time BETWEEN #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null ">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
            <if test="param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%',#{param.sellChatgroupName},'%')
            </if>
            <if test="param.custName != null and param.custName != ''">
                and ase.cust_name LIKE CONCAT('%',#{param.custName},'%')
            </if>
            <if test="param.startCardBalance != null and param.endCardBalance != null">
                and ase.card_balance BETWEEN #{param.startCardBalance} and #{param.endCardBalance}
            </if>
            <if test="param.chargeStage != null  ">
               and ase.charge_stage = #{param.chargeStage}
            </if>
        </where>
        order by ase.pinned_status ASC,ase.`status` ASC, ase.sell_time DESC,recharge_id DESC
    </select>

    <select id="selectAccountSellListByAnchor"
            parameterType="com.juyou.system.domain.vo.SellAnchorVo" resultMap="AccountSellResult">
        <include refid="selectAccountSellLevelVo"/>
        LEFT JOIN sys_user u ON u.user_id = ase.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN two_account_recharge ar ON ar.account_name = ase.account_name
        where ase.`status` = 3 and ase.sell_chatgroup_name = '903'
            ${param.params.dataScope}
            <if test="param.accountName != null and param.accountName != ''">
                and ase.account_name LIKE CONCAT('%',#{param.accountName},'%')
            </if>
            <if test="param.isUse != null and param.isUse != ''">
                and ase.`is_use` = #{param.isUse}
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null ">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
            <if test="param.useStartTime != null and param.useEndTime != null ">
                and ase.use_time BETWEEN #{param.useStartTime} and #{param.useEndTime}
            </if>
        order by ase.sell_time DESC
    </select>

    <select id="selectAccountReportList"
            parameterType="com.juyou.system.params.AccountSellPageParam" resultMap="AccountSellResult">
        <include refid="selectAccountSellVo"/>
        LEFT JOIN sys_user u ON u.user_id = ase.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <where>
            <!--            <if test="param.params.dataScope != null and param.params.dataScope != ''">-->
            <!--                (-->
            <!--                1 = 1 ${param.params.dataScope}-->
            <!--                or-->
            <!--                ase.`status` in (1,3,4)-->
            <!--                )-->
            <!--            </if>-->
            <if test="param.accountName != null and param.accountName != ''">
                and ase.account_name LIKE CONCAT('%',#{param.accountName},'%')
            </if>
            <if test="param.status != null and param.status != ''">
                and ase.`status` = #{param.status}
            </if>
            <if test="param.accountZone != null and param.accountZone != ''">
                and ase.account_zone LIKE CONCAT('%',#{param.accountZone},'%')
            </if>
            <if test="param.idType != null and param.idType != ''">
                and ase.id_type = #{param.idType}
            </if>
            <if test="param.startShouldAmt != null and param.startShouldAmt != 0 and param.endShouldAmt != null and param.endShouldAmt != 0">
                and ase.should_amt BETWEEN #{param.startShouldAmt} and #{param.endShouldAmt}
            </if>
            <if test="param.startReceiveTime != null and param.endReceiveTime != null ">
                and ase.receive_time BETWEEN #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null ">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
            <if test="param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%',#{param.sellChatgroupName},'%')
            </if>
            <if test="param.custName != null and param.custName != ''">
                and ase.cust_name LIKE CONCAT('%',#{param.custName},'%')
            </if>
        </where>
        order by ase.`status` ASC, ase.`recharge_id` ASC
    </select>

    <select id="selectCanBeClaimedAccount" resultMap="AccountSellResult"
            parameterType="int">
        <include refid="selectAccountSellVo"/>
        LEFT JOIN two_account_recharge as r  on  r.account_name =ase.account_name and r.charge_stage= #{chargeStage}
        where
        ase.`status` = 1
        and ase.completed_time_minutes_num >=0
        and ase.card_balance in
        <foreach collection="cardBalanceList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY ase.create_time asc
        LIMIT 0,#{count}
    </select>

    <select id="selectCanBeClaimedAccountByWalletArea" resultMap="AccountSellResult"
            parameterType="int">
        <include refid="selectAccountSellVo"/>
        where
        ase.`status` = 1
        and ase.completed_time_minutes_num >= #{accountSellPendingMinutes}
        and ase.wallet_area = #{walletArea}
        and ase.card_balance in
        <foreach collection="cardBalanceList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        LIMIT 0,#{count}
    </select>


    <select id="getRemainingActualRechargeTotal" resultType="com.juyou.system.domain.vo.ZoneGroupVo">
        SELECT
            main.account_zone as zone,
            (
                (
                    SELECT IFNULL(SUM(are.recharge_amt), 0)
                    FROM two_account_recharge are
        WHERE are.`status` = 2
          AND are.account_zone = main.account_zone
            )
            +
            (
        SELECT IFNULL(SUM(se.card_balance), 0)
        FROM two_account_sell se
        WHERE se.`status` IN (1, 2)
          AND se.account_zone = main.account_zone
            )
            ) AS price
        FROM
            (
            SELECT DISTINCT account_zone
            FROM two_account_recharge
            UNION
            SELECT DISTINCT account_zone
            FROM two_account_sell
            ) AS main;
    </select>

    <select id="getRemainingCostTotal" resultType="java.math.BigDecimal">
        SELECT (
            (select
            IFNULL(SUM(are.buy_amt),0)
            FROM
            two_account_recharge are
            where are.`status` = 2
            )
            +
            (
            select
            IFNULL(SUM(se.buy_amt),0)
            from two_account_sell se
            where se.`status` in (1,2)
            )
            ) as amt
    </select>
    <select id="getSellChatgroupList" resultType="com.juyou.system.domain.vo.SellChatgroupVo"
            parameterType="com.juyou.system.params.SellChatgroupParam">
        SELECT
        ase.sell_chatgroup_name AS sellChatgroupName,
        COUNT(*) as sellCount,
        ROUND(SUM( ase.card_balance ),2) AS cardBalance,
        ROUND(SUM( ase.buy_amt ),2) AS buyAmt,
        ROUND(SUM( ase.sell_amt ),2) AS sellAmt,
        ROUND((SUM( ase.sell_amt )- SUM( ase.buy_amt )),2) AS grossProfit
        FROM `two_account_sell` ase
        <where>
            ase.status = 3
            <if test="param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%',#{param.sellChatgroupName},'%')
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
        </where>
        GROUP BY ase.sell_chatgroup_name
        ORDER BY
        CASE
        WHEN ase.sell_chatgroup_name REGEXP '^[0-9]+$' THEN CAST(ase.sell_chatgroup_name AS UNSIGNED)
        ELSE 9999
        END
        DESC;

    </select>
    <select id="getTodayAmountSold" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(ase.card_balance),0)
        FROM `two_account_sell` ase
        where
        <if test=" userId != null and userId != ''">
            ase.receive_user_id = #{userId}
            </if>
        <if test=" accountZone != null and accountZone != ''">
          and ase.account_zone = #{accountZone}
          </if>
          <if test=" idType != null and idType != ''">
              and ase.id_type = #{idType}
          </if>
        <if test=" startDate != null and endDate != null">
          and ase.sell_time BETWEEN #{startDate} and #{endDate}
        </if>
    </select>

    <select id="getSystemResidualAccountList" resultType="com.juyou.system.domain.vo.AccountSellResidualAccountVoList">
        SELECT
            ase.card_balance AS cardBalance,
            ar.charge_stage as chargeStage,
            COUNT(*) AS count
        FROM `two_account_sell` ase
            LEFT JOIN two_account_recharge ar ON  ar.account_name=ase.account_name
        WHERE ase.`status` = 1
          AND (ase.completed_time_minutes_num >= 0 and ar.charge_stage = 1 or (ase.completed_time_minutes_num >=0 and ar.charge_stage = 2))
        GROUP BY ase.card_balance ,ar.charge_stage
        ORDER BY ase.card_balance ASC;
    </select>

    <select id="getSystemResidualAccountLeiSheList" resultType="com.juyou.system.domain.vo.AccountSellResidualAccountVoList">
        SELECT
            ase.card_balance cardBalance,
            ase.wallet_area  walletArea,
            COUNT(*) count
        FROM `two_account_sell` ase
        where ase.`status` = 1
          and ase.account_zone = #{accountZone}
          and ase.id_type = #{idType}
          and ase.wallet_area = #{walletArea}
          and ase.completed_time_minutes_num >= #{accountSellPendingMinutes}
        GROUP BY ase.card_balance
        ORDER BY  ase.card_balance ASC
    </select>

    <select id="getAccountSellPendingSaleList" resultType="com.juyou.system.domain.vo.AccountSellVo"
            parameterType="com.juyou.system.params.AccountSellPendingSaleSearchParam">
        <include refid="selectAccountSellListVo"/>
        <where>
            AND ase.`status` = 2
            <if test=" param.accountName != null and param.accountName != '' ">
                and ase.account_name LIKE CONCAT( '%', #{param.accountName}, '%' )
            </if>
            <if test=" param.accountZone != null and param.accountZone != '' ">
                and ase.account_zone LIKE CONCAT( '%', #{param.accountZone}, '%' )
            </if>
        </where>
    </select>
    <select id="getAwaitReceiveTotalAmount" resultType="java.math.BigDecimal">
        SELECT
            SUM(ase.card_balance)
        FROM
            `two_account_sell` ase
        WHERE
            ase.`status` = 1
          and ase.account_zone = #{accountZone}
          and ase.id_type = #{idType}
          and ase.completed_time_minutes_num >= #{accountSellPendingMinutes}
    </select>
    <select id="getAccountSellSoldReportList" resultType="com.juyou.system.domain.vo.AccountSellVo"
            parameterType="com.juyou.system.params.AccountSellPendingSaleSearchParam">
        <include refid="selectAccountSellListVo"/>
        <if test="param.giftCard != null and param.giftCard != ''">
            inner join ( select gcrr.account FROM two_gift_card_recharge_record gcrr WHERE gcrr.gift_card LIKE
            CONCAT('%',#{param.giftCard},'%') GROUP BY gcrr.account ) as gcrr on gcrr.account = ase.account_name
        </if>
        <where>
            AND ase.`status` in (3,4)
            <if test=" param.accountName != null and param.accountName != '' ">
                and ase.account_name LIKE CONCAT( '%', #{param.accountName}, '%' )
            </if>
            <if test=" param.accountZone != null and param.accountZone != '' ">
                and ase.account_zone LIKE CONCAT( '%', #{param.accountZone}, '%' )
            </if>
            <if test=" param.idType != null and param.idType != ''">
                and ase.id_type = #{param.idType}
            </if>
            <if test=" param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%', #{param.sellChatgroupName} ,'%')
            </if>
            <if test=" param.status != null and param.status != '' ">
                and ase.status = #{param.status}
            </if>
            <if test=" param.writeOffStatus != null and param.writeOffStatus != ''">
                and ase.write_off_status = #{param.writeOffStatus}
            </if>
            <if test=" param.startSellTime != null and param.endSellTime != null ">
                and ase.sell_time BETWEEN #{param.startSellTime} AND #{param.endSellTime}
            </if>
        </where>
        order by ase.`status` ASC , ase.recharge_id DESC
    </select>

    <select id="getSellGroupDetailList" resultMap="AccountSellResult"
            parameterType="com.juyou.system.params.SellGroupDetailSearchParam">
        <include refid="selectAccountSellVo"/>
        <if test="param.giftCard != null and param.giftCard != ''">
            inner join ( select gcrr.account FROM two_gift_card_recharge_record gcrr WHERE gcrr.gift_card LIKE
            CONCAT('%',#{param.giftCard},'%') GROUP BY gcrr.account ) as gcrr on gcrr.account = ase.account_name
        </if>
        <where>
            ase.status = 3
            and ase.sell_chatgroup_name = #{param.sellChatgroupName}
            <if test="param.accountName != null and param.accountName != ''">
                and ase.account_name like CONCAT('%',#{param.accountName},'%')
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
            <if test="param.cardBalance != null and param.cardBalance != 0">
                and ase.card_balance = #{param.cardBalance}
            </if>
        </where>
    </select>

    <select id="getAccountSellSoldList" resultType="com.juyou.system.domain.AccountSell"
            parameterType="com.juyou.system.params.AccountSellSoldListSearchParam">
        SELECT *
        FROM `two_account_sell` ase
        <where>
            <if test="param.status == null and param.status == ''">
                and ase.`status` in (3,4)
            </if>
            <if test="param.status != null and param.status != ''">
                and ase.`status` = #{status}
            </if>
            <if test="param.accountName != null and param.accountName != ">
                and ase.account_name LIKE CONCAT('%',#{accountName},'%')
            </if>
            <if test="param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%',#{sellChatgroupName},'%')
            </if>
            <if test="param.startSellDate != null and param.endSellDate != null">
                and ase.sell_time BETWEEN #{startSellDate} and #{endSellDate}
            </if>
        </where>
    </select>

    <select id="findPerformanceList" resultType="com.juyou.system.domain.vo.PerformanceVoList">
        select
        MAX(su.nick_name) as nickName,
        ase.done_user account,
        DATE(ase.done_time) workDate,
        COUNT(1) as sellCount,
        SUM(ase.sell_amt) as sellAmt
        FROM two_account_sell ase
        LEFT JOIN sys_user su on su.user_name = ase.done_user
        WHERE ase.done_user IS NOT NULL
        <if test="param.userName != null and param.userName != ''">
            AND (
            su.user_name LIKE CONCAT('%',#{param.userName},'%')
            OR
            su.nick_name LIKE CONCAT('%',#{param.userName},'%')
            )
        </if>
        <if test="param.startDate != null and param.endDate != null">
            AND DATE(ase.done_time) BETWEEN #{param.startDate} AND #{param.endDate}
        </if>
        GROUP BY ase.done_user, DATE(ase.done_time)
        ORDER BY DATE(ase.done_time) DESC
    </select>


    <insert id="insertAccountSell" parameterType="AccountSell" useGeneratedKeys="true" keyProperty="rechargeId">
        insert into two_account_sell
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="acid != null">acid,</if>
            <if test="accountName != null">account_name,</if>
            <if test="accountPwd != null">account_pwd,</if>
            <if test="accountZone != null">account_zone,</if>
            <if test="walletArea != null">wallet_area,</if>
            <if test="idType != null">id_type,</if>
            <if test="spareCode != null">spare_code,</if>
            <if test="completedTimeMinutesNum != null">completed_time_minutes_num,</if>
            <if test="status != null">status,</if>
            <if test="writeOffStatus != null">write_off_status,</if>
            <if test="cardBalance != null">card_balance,</if>
            <if test="buyPrice != null">buy_price,</if>
            <if test="buyAmt != null">buy_amt,</if>
            <if test="shouldAmt != null">should_amt,</if>
            <if test="sellPrice != null">sell_price,</if>
            <if test="sellAmt != null">sell_amt,</if>
            <if test="sellChatgroupName != null">sell_chatgroup_name,</if>
            <if test="custName != null">cust_name,</if>
            <if test="sellTime != null">sell_time,</if>
            <if test="remark != null">remark,</if>
            <if test="cancelDate != null">cancel_date,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="cancelImgs != null">cancel_imgs,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="doneTime != null">done_time,</if>
            <if test="doneUser != null">done_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="receiveUser != null">receive_user,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="acid != null">#{acid},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="accountPwd != null">#{accountPwd},</if>
            <if test="accountZone != null">#{accountZone},</if>
            <if test="walletArea != null">#{walletArea},</if>
            <if test="idType != null">#{idType},</if>
            <if test="spareCode != null">#{spareCode},</if>
            <if test="completedTimeMinutesNum != null">#{completedTimeMinutesNum},</if>
            <if test="status != null">#{status},</if>
            <if test="writeOffStatus != null">#{writeOffStatus},</if>
            <if test="cardBalance != null">#{cardBalance},</if>
            <if test="buyPrice != null">#{buyPrice},</if>
            <if test="buyAmt != null">#{buyAmt},</if>
            <if test="shouldAmt != null">#{shouldAmt},</if>
            <if test="sellPrice != null">#{sellPrice},</if>
            <if test="sellAmt != null">#{sellAmt},</if>
            <if test="sellChatgroupName != null">#{sellChatgroupName},</if>
            <if test="custName != null">#{custName},</if>
            <if test="sellTime != null">#{sellTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="cancelImgs != null">#{cancelImgs},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="doneTime != null">#{doneTime},</if>
            <if test="doneUser != null">#{doneUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="receiveUser != null">#{receiveUserId},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>
    <insert id="insertRemarks">
        insert
    </insert>

    <update id="updateAccountSell" parameterType="AccountSell">
        update two_account_sell
        <trim prefix="SET" suffixOverrides=",">
            <if test="acid != null">acid = #{acid},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="accountPwd != null">account_pwd = #{accountPwd},</if>
            <if test="accountZone != null">account_zone = #{accountZone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="writeOffStatus != null">write_off_status = #{writeOffStatus},</if>
            <if test="completedTimeMinutesNum != null">completed_time_minutes_num = #{completedTimeMinutesNum},</if>
            <if test="cardBalance != null">card_balance = #{cardBalance},</if>
            <if test="buyPrice != null">buy_price = #{buyPrice},</if>
            <if test="buyAmt != null">buy_amt = #{buyAmt},</if>
            <if test="shouldAmt != null">should_amt = #{shouldAmt},</if>
            <if test="sellPrice != null">sell_price = #{sellPrice},</if>
            <if test="sellAmt != null">sell_amt = #{sellAmt},</if>
            <if test="sellChatgroupName != null">sell_chatgroup_name = #{sellChatgroupName},</if>
            <if test="custName != null">cust_name = #{custName},</if>
            <if test="sellTime != null">sell_time = #{sellTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelImgs != null">cancel_imgs = #{cancelImgs},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="doneTime != null">done_time = #{doneTime},</if>
            <if test="doneUser != null">done_user = #{doneUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="receiveUser != null">receive_user = #{receiveUser},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="pinnedStatus != null">pinned_status = #{pinnedStatus},</if>
            <if test="isUse != null">is_use = #{isUse},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="hasEdit != null">has_edit = #{hasEdit},</if>
        </trim>
        where recharge_id = #{rechargeId}
    </update>
    <update id="updateAccountSellStatus">
        update two_account_sell
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
        </trim>
        where recharge_id = #{rechargeId}
    </update>

    <update id="updateOldAccountSellStatus">
        update account_sell
        <trim prefix="SET" suffixOverrides=",">
            <if test="acid != null">acid = #{acid},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="accountPwd != null">account_pwd = #{accountPwd},</if>
            <if test="accountZone != null">account_zone = #{accountZone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="writeOffStatus != null">write_off_status = #{writeOffStatus},</if>
            <if test="completedTimeMinutesNum != null">completed_time_minutes_num = #{completedTimeMinutesNum},</if>
            <if test="cardBalance != null">card_balance = #{cardBalance},</if>
            <if test="buyPrice != null">buy_price = #{buyPrice},</if>
            <if test="buyAmt != null">buy_amt = #{buyAmt},</if>
            <if test="shouldAmt != null">should_amt = #{shouldAmt},</if>
            <if test="sellPrice != null">sell_price = #{sellPrice},</if>
            <if test="sellAmt != null">sell_amt = #{sellAmt},</if>
            <if test="sellChatgroupName != null">sell_chatgroup_name = #{sellChatgroupName},</if>
            <if test="custName != null">cust_name = #{custName},</if>
            <if test="sellTime != null">sell_time = #{sellTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="cancelImgs != null">cancel_imgs = #{cancelImgs},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="doneTime != null">done_time = #{doneTime},</if>
            <if test="doneUser != null">done_user = #{doneUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="receiveUser != null">receive_user = #{receiveUser},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="deptId != null">dept_id = #{receiveUserId},</if>
            <if test="pinnedStatus != null">pinned_status = #{pinnedStatus}</if>
        </trim>
        where recharge_id = #{rechargeId}
    </update>

    <update id="batchUpdateAccountSellStatus">
        update two_account_sell
        set status = #{status}
        where recharge_id in
        <foreach collection="rechargeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateNotEffectiveCompletedTimeMinutesNum">
        UPDATE two_account_sell ase
        SET ase.completed_time_minutes_num =  TIMESTAMPDIFF(MINUTE, ase.create_time, NOW())
        WHERE ase.`status` = 1 and ase.id_type = 0 and ase.completed_time_minutes_num &lt; TIMESTAMPDIFF(MINUTE, ase.create_time, NOW())
    </update>
    <update id="updateReceiveAccount">
        update two_account_sell ase
        set ase.receive_time = NOW(), ase.receive_user = #{userName}, ase.receive_user_id = #{userId} , ase.dept_id =
        #{deptId}, ase.status = 2
        where ase.recharge_id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="transferAccount" parameterType="com.juyou.system.params.AccountTransferAccountParam">
        update two_account_sell ase
        set ase.receive_user = #{param.userName},ase.receive_user_id = #{param.userId}
        where ase.`status` = 2 and ase.receive_user_id = #{param.currentUserId}
    </update>
    <update id="updatePinned" parameterType="AccountSell">
        update two_account_sell
        <trim prefix="SET" suffixOverrides=",">
            <if test="pinnedStatus != null">pinned_status = #{pinnedStatus},</if>
            <if test="remark != null and remark != ''">remark = #{remark}</if>
        </trim>
        where recharge_id = #{rechargeId}
    </update>
    <update id="updateOldAccountSellByAcid">
        UPDATE  two_account_sell    SET `status`= 3    WHERE acid =4
        where acid = #{acid}
    </update>

    <delete id="deleteAccountSellByRechargeId" parameterType="Integer">
        delete from two_account_sell where recharge_id = #{rechargeId}
    </delete>

    <delete id="deleteAccountSellByRechargeIds" parameterType="String">
        delete from two_account_sell where recharge_id in
        <foreach item="rechargeId" collection="array" open="(" separator="," close=")">
            #{rechargeId}
        </foreach>
    </delete>

    <select id="selectAccountRechargeVoList" resultType="accountRechargeVo">
        SELECT u.account_zone as country,count(u.acid) as quantity
        FROM `two_account_sell` u
        WHERE u.`status` = 2 and u.id_type = #{idType}
        GROUP BY u.account_zone
    </select>
    <select id="getSellVoidedBalances" resultType="java.math.BigDecimal">
        SELECT IFNULL(
                       SUM(
                               IFNULL(
                                       (CASE
                                            WHEN ar.`status` = '4' AND ar.receive_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 WEEK) AND ar.receive_user_id = #{receiveUserId}
                                                THEN IFNULL(ar.buy_amt, 0)
                                            ELSE 0
                                           END), 0)
                       )
                           / SUM(IFNULL(ar.buy_amt, 0))*100, 0
               )
        FROM two_account_sell ar;
    </select>
    <select id="filterTheNumberOfClaimedAndUnsoldAccounts" resultType="java.lang.Integer">
            select count(*)
            from two_account_sell   where receive_user_id  = #{receiveUserId} AND STATUS = '2'

    </select>
    <delete id="deleteAccountSellByAcid" parameterType="Integer">
        delete from two_account_sell where acid = #{acid}
    </delete>

    <delete id="deleteByAcidList">
        delete from two_account_sell ase
        where ase.acid in
        <foreach item="item" collection="acidList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectAccountSellListAll"
            parameterType="com.juyou.system.params.AccountSellPageParam" resultMap="AccountSellResult">
        select ase.recharge_id,
        ase.acid,
        ase.card_balance,
        ase.account_name,
        ase.account_pwd,
        ase.account_zone,
        ase.wallet_area,
        ase.id_type,
        ase.spare_code,
        ase.status,
        ase.write_off_status,
        ase.completed_time_minutes_num,
        ase.card_balance,
        ase.buy_price,
        ar.charge_stage,
        ase.buy_amt,
        ase.should_amt,
        ase.sell_price,
        ase.sell_amt,
        ase.sell_chatgroup_name,
        ase.cust_name,
        ase.sell_time,
        ase.remark,
        ase.cancel_date,
        ase.cancel_reason,
        ase.cancel_imgs,
        ase.create_time,
        ase.create_by,
        ase.done_time,
        ase.done_user,
        ase.update_time,
        ase.update_by,
        ase.receive_time,
        ase.receive_user,
        ase.receive_user_id,
        ase.dept_id,
        ase.pinned_status,
        ase.is_use,
        ase.use_time,
        ar.charge_stage,
        ase.has_edit
        from two_account_sell ase
        LEFT JOIN sys_user u ON u.user_id = ase.receive_user_id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN two_account_recharge ar ON ar.account_name = ase.account_name
        <where>
            ${param.params.dataScope}
            <if test="param.accountName != null and param.accountName != ''">
                and ase.account_name LIKE CONCAT('%',#{param.accountName},'%')
            </if>
            <if test="param.status != null and param.status != ''">
                and ase.`status` = #{param.status}
            </if>
            <if test="param.accountZone != null and param.accountZone != ''">
                and ase.account_zone LIKE CONCAT('%',#{param.accountZone},'%')
            </if>
            <if test="param.walletArea != null and param.walletArea != ''">
                and ase.wallet_area LIKE CONCAT('%',#{param.walletArea},'%')
            </if>
            <if test="param.idType != null and param.idType != ''">
                and ase.id_type = #{param.idType}
            </if>
            <if test="param.startShouldAmt != null and param.startShouldAmt != 0 and param.endShouldAmt != null and param.endShouldAmt != 0">
                and ase.should_amt BETWEEN #{param.startShouldAmt} and #{param.endShouldAmt}
            </if>
            <if test="param.startReceiveTime != null and param.endReceiveTime != null ">
                and ase.receive_time BETWEEN #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test="param.startSellTime != null and param.endSellTime != null ">
                and ase.sell_time BETWEEN #{param.startSellTime} and #{param.endSellTime}
            </if>
            <if test="param.sellChatgroupName != null and param.sellChatgroupName != ''">
                and ase.sell_chatgroup_name LIKE CONCAT('%',#{param.sellChatgroupName},'%')
            </if>
            <if test="param.custName != null and param.custName != ''">
                and ase.cust_name LIKE CONCAT('%',#{param.custName},'%')
            </if>
            <if test="param.startCardBalance != null and param.endCardBalance != null">
                and ase.card_balance BETWEEN #{param.startCardBalance} and #{param.endCardBalance}
            </if>
            <if test="param.chargeStage != null  ">
                and ar.charge_stage = #{param.chargeStage}
            </if>

        </where>
        order by ase.pinned_status ASC,ase.`status` ASC, ase.sell_time DESC,recharge_id DESC
    </select>
    <select id="selectOldAccountSellByRechargeId" resultType="com.juyou.system.domain.AccountSell">
        <include refid="selectOldAccountSellVo"/>
        where recharge_id = #{rechargeId}
    </select>
    <select id="getSellVoidedBalancesDate" resultType="java.math.BigDecimal">
        SELECT
            (
                SELECT
                    SUM(IF( ar.`status` = '4', IFNULL( ar.card_balance, 0 ), 0 ))
                FROM
                    two_account_sell ar
                WHERE
                    1 = 1
                  AND ar.receive_user_id = #{receiveUserId}
                  AND ar.cancel_date BETWEEN #{Date.start} and #{Date.end}
            )
                /
            (
                SELECT
                    SUM(IFNULL( ar.card_balance, 0 ))
                FROM
                    two_account_sell ar
                WHERE
                    1 = 1
                  AND ar.receive_user_id = #{receiveUserId}
                  AND ( ar.cancel_date BETWEEN #{Date.start} and #{Date.end}
                    OR ar.done_time BETWEEN #{Date.start} and #{Date.end}
                    OR ar.receive_time BETWEEN #{Date.start} and #{Date.end})
            ) * 100
    </select>
</mapper>
