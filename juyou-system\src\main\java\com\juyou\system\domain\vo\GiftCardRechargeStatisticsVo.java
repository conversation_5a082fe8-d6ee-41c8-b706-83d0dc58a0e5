package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 礼品卡充值统计-vo
 */
@Data
@ApiModel("GiftCardRechargeStatisticsVo")
public class GiftCardRechargeStatisticsVo implements Serializable {

    private static final long serialVersionUID = 4333208841564765949L;

    @ApiModelProperty("充值来源群")
    private String sourceGroup;

    @ApiModelProperty("充值金额")
    private BigDecimal faceValue;

    @ApiModelProperty("平均进价")
    private BigDecimal avgPrice;

    @ApiModelProperty("充值成本金额")
    private BigDecimal buyAmt;

}
