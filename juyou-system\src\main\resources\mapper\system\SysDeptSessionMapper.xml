<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.SysDeptSessionMapper">

    <resultMap type="SysDeptSession" id="SysDeptSessionResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="session"    column="session"    />
    </resultMap>

    <sql id="selectSysDeptSessionVo">
        select id, dept_id, session from sys_dept_session
    </sql>

    <select id="selectSysDeptSessionList" parameterType="SysDeptSession" resultMap="SysDeptSessionResult">
        <include refid="selectSysDeptSessionVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="session != null "> and session = #{session}</if>
        </where>
    </select>

    <select id="selectSysDeptSessionById" parameterType="Long" resultMap="SysDeptSessionResult">
        <include refid="selectSysDeptSessionVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysDeptSession" parameterType="SysDeptSession" useGeneratedKeys="true" keyProperty="id">
        insert into sys_dept_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="session != null">session,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="session != null">#{session},</if>
        </trim>
    </insert>

    <update id="updateSysDeptSession" parameterType="SysDeptSession">
        update sys_dept_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="session != null">session = #{session},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateSysDeptSessionByDeptId" parameterType="SysDeptSession">
        update sys_dept_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="session != null">session = #{session},</if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <delete id="deleteSysDeptSessionById" parameterType="Long">
        delete from sys_dept_session where id = #{id}
    </delete>

    <delete id="deleteSysDeptSessionByIds" parameterType="String">
        delete from sys_dept_session where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
