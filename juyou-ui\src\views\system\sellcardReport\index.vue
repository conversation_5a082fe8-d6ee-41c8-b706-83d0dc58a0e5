<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="95px">
      <el-form-item label="账号" prop="accountName">
        <el-input v-model="queryParams.accountName" placeholder="请输入账号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="ID类型" prop="idType">
        <el-select v-model="queryParams.idType" placeholder="请选择ID类型" :style="{ width: '80%' }" clearable>
          <el-option
            v-for="dict in [{ label: '全部', value: '' }, { label: '苹果', value: '0' }, { label: '雷蛇', value: '1' }]"
            :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="出售状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择出售状态" :style="{ width: '80%' }" clearable>
          <el-option v-for="dict in dict.type.sell_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="区域" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option value="" label="全部"></el-option>
          <el-option
            v-for="item in zonrList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="出售群" prop="sellChatgroupName">
        <el-input v-model="queryParams.sellChatgroupName" placeholder="请输入出售群" clearable
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="售卖人-客户" prop="custName">
        <el-input v-model="queryParams.custName" placeholder="请输入售卖人" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="领取日期" prop="receiveTime">
        <el-date-picker v-model="queryParams.receiveTime" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="出售日期" prop="sellTime">
        <el-date-picker v-model="queryParams.sellTime" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="yesterday">昨天</el-button>
        <el-button type="primary" size="mini" @click="today">今天</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="recordList" tooltip-effect="dark" border :summary-method="getSummaries"
              show-summary @select="selectFn"
              @select-all="selectAllFn" ref="mutipleTable">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" align="center" type="index" width="55">
      </el-table-column>
      <el-table-column label="ID类型" align="center" prop="idType">
        <template slot-scope="scope">
          <span>{{ scope.row.idType==='0' ? '苹果' : '雷蛇' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">{{
              scope.row.accountName?
            scope.row.accountName.substr(0,4) + '***' +
            scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd"/>
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <!-- <el-table-column label="来源群" align="center" prop="sourceGroup" /> -->
      <el-table-column label="出售状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sell_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="应充值金额" align="center" prop="shouldAmt" /> -->
      <el-table-column label="实际充值金额" align="center" prop="cardBalance"/>
      <el-table-column label="出售单价" align="center" prop="sellPrice"/>
      <el-table-column label="出售金额" align="center" prop="sellAmt"/>
      <el-table-column label="出售群" align="center" prop="sellChatgroupName"/>
      <el-table-column label="进价(CNY)" align="center" prop="buyPrice"/>
      <el-table-column label="成本金额(CNY)" align="center" prop="buyAmt"/>
      <el-table-column label="领取时间" align="center" width="200" prop="receiveTime"/>
      <el-table-column fixed="right" label="操作" align="center" width="145" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)"
                     v-hasPermi="['system:accRecharge:edit']">查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <!-- 充值明细详情 -->
    <el-dialog title="账号详情" :visible.sync="sellcardReportDetailsVisible" :close-on-click-modal="false"
               :close-on-press-escape="false" width="850px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">账号： {{ sellcardReportDetailsForm.accountName }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">密码： {{ sellcardReportDetailsForm.accountPwd }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">区域： {{ sellcardReportDetailsForm.accountZone }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">充值状态：{{ formatRechargeStatus(sellcardReportDetailsForm.status) }}</div>
        </el-col>
        <!-- <el-col :span="8">
          <div class="grid-content bg-purple">应充值金额： {{ sellcardReportDetailsForm.shouldAmt }}</div>
        </el-col> -->
        <el-col :span="8">
          <div class="grid-content bg-purple">实际充值金额： {{ sellcardReportDetailsForm.rechargeAmt }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">进价： {{ sellcardReportDetailsForm.buyPrice }}</div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- <el-col :span="8">
          <div class="grid-content bg-purple">进价： {{ sellcardReportDetailsForm.buyPrice }}</div>
        </el-col> -->
        <el-col :span="8">
          <div class="grid-content bg-purple">成本： {{ sellcardReportDetailsForm.buyAmt }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">创建人： {{ sellcardReportDetailsForm.createBy }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">创建时间： {{ sellcardReportDetailsForm.createTime }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="8">
          <div class="grid-content bg-purple">创建时间： {{ sellcardReportDetailsForm.createTime }}</div>
        </el-col> -->
        <el-col :span="8">
          <div class="grid-content bg-purple">领取人： {{ sellcardReportDetailsForm.receiveUser }}</div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple">领取时间： {{ sellcardReportDetailsForm.receiveTime }}</div>
        </el-col>
        <!-- <el-col :span="8"><div class="grid-content bg-purple">出售状态： {{ rechargeDetailsForm(rechargeDetailsForm.status)}}</div></el-col> -->
        <el-col :span="8">
          <div class="grid-content bg-purple">完成人： {{ sellcardReportDetailsForm.doneUser }}</div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">出售群： {{ sellcardReportDetailsForm.sellChatgroupName }}</div>
        </el-col>
        <!-- <el-col :span="8">
          <div class="grid-content bg-purple">完成人： {{ sellcardReportDetailsForm.doneUser }}</div>
        </el-col> -->
        <el-col :span="8">
          <div class="grid-content bg-purple">完成时间： {{ sellcardReportDetailsForm.doneTime }}</div>
        </el-col>

        <el-col :span="8">
          <div class="grid-content bg-purple">作废时间： {{ sellcardReportDetailsForm.cancelDate }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="4">
        <el-col :span="8">
          <div class="grid-content bg-purple">作废原因： {{ sellcardReportDetailsForm.cancelReason }}</div>
        </el-col>

        <el-col :span="8">
          <div>作废图片:</div>
          <image-upload v-if="sellcardReportDetailsForm.cancelImgs" v-model="sellcardReportDetailsForm.cancelImgs"
                        :limit="getLimit(sellcardReportDetailsForm.cancelImgs)" :uploadImgUrl="uploadImgUrl"
                        :isShowTip="false"/>
        </el-col>
        <el-col :span="8">
          <div>备用码:</div>
          <img :src="ImgUrl+sellcardReportDetailsForm.spareCode" :width="80" :height="80">
        </el-col>
        <!-- <image-preview  v-if="sellcardReportDetailsForm.cancelImgs" :src="sellcardReportDetailsForm.cancelImgs || ''" :width="80" :height="80"/> -->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple">备注： {{ sellcardReportDetailsForm.remark }}</div>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="giftCardRechargeRecordList" :summary-method="getSubSummaries" show-summary>
        <el-table-column label="序号" align="center" type="index" width="50"></el-table-column>
        <!-- <el-table-column label="账号" align="center" prop="account" />
        <el-table-column label="密码" align="center" prop="accountPwd" /> -->
        <el-table-column label="礼品卡" align="center" prop="giftCard"/>
        <el-table-column label="来源群" align="center" prop="sourceGroup"/>
        <!-- <el-table-column label="卡状态" align="center" prop="cardStatus"></el-table-column> -->
        <el-table-column label="面值" align="center" prop="faceValue"></el-table-column>
        <el-table-column label="余额" align="center" prop="balance"/>
        <el-table-column label="执行时间" align="center" prop="executionTime"/>
        <el-table-column label="操作人" align="center" prop="createBy"/>
        <!-- <el-table-column label="执行信息" align="center" width="185" prop="executionInfo" /> -->
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary"
                   @click="copyToClip(`${sellcardReportDetailsForm.accountName}----${sellcardReportDetailsForm.accountPwd}`)">
          一键复制账号
        </el-button>
        <el-button @click="detailCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    sellcardReportList,
    sellcardReportDetail
  } from "@/api/system/sellcardReport";
  import {getDicts} from "@/api/system/dict/data";
  import Cookies from "js-cookie";

  export default {
    name: "sellcardReport",
    dicts: ['recharge_status', 'sell_status'],
    data() {
      return {
        uploadImgUrl: process.env.VUE_APP_BASE_API + "/commonFile/fileUpload",//,//dev-api/commonFile/fileUpload
        ImgUrl: process.env.VUE_APP_BASE_API + "/showFile/",
        //充值记录列表列表
        recordList: [],
        // 遮罩层
        loading: true,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,

        acids: [],
        // 总条数
        total: 0,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,

        },
        // 充值账号详情弹窗
        sellcardReportDetailsVisible: false,
        sellcardReportDetailsForm: {},
        giftCardRechargeRecordList: [],//详情弹窗列表
        zonrList: [],
      };
    },
    created() {
      this.into();
    },
    methods: {
      into() {
        let cook = Cookies.get("business_earning_report");
        if (cook != null && cook != undefined) {
          this.queryParams = JSON.parse(cook);
        }
        getDicts("account_zone").then(response => {
            this.zonrList = response.data;
          }
        );
        this.getList();//获取未生效状态的账号
      },
      yesterday() {
        // 昨天
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 1);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        this.$set(this.queryParams, 'receiveTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);
        this.$set(this.queryParams, 'sellTime', [`${yesterday} 00:00:00`, `${today} 00:00:00`]);


        // const date = new Date();
        // const enddate = new Date(date);
        // enddate.setDate(date.getDate() - 1);
        // const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // this.$set(this.queryParams, 'receiveTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
        // this.$set(this.queryParams, 'sellTime', [`${yesterday} 00:00:00`, `${yesterday} 22:00:00`]);
      },
      today() {
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        const end = new Date();
        end.setDate(end.getDate() + 1);
        const endStr = end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate();
        this.$set(this.queryParams, 'receiveTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);
        this.$set(this.queryParams, 'sellTime', [`${today} 00:00:00`, `${endStr} 00:00:00`]);

        // const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        // this.$set(this.queryParams, 'receiveTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
        // this.$set(this.queryParams, 'sellTime', [`${today} 00:00:00`, `${today} 22:00:00`]);
      },
      /** 导出按钮操作 */
      handleExport() {
        if (this.acids.length > 0) {
          this.download('/system/sellcardReport/export', {
            ids: this.acids
          }, `sellcardReport_${new Date().getTime()}.xlsx`)

        } else {
          this.download('/system/sellcardReport/export', {
            ...this.queryParams
          }, `sellcardReport_${new Date().getTime()}.xlsx`)
          // this.recordList.forEach(itme=>{
          //   request.push(itme.rechargeId);
          // })
        }

      },
      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          const request = {
            ...this.queryParams,
            startSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[0] : null,//结束完成时间
            endSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[1] : null,//开始完成时间
            startReceiveTime: this.queryParams.receiveTime ? this.queryParams.receiveTime[0] : null,//开始领取时间
            endReceiveTime: this.queryParams.receiveTime ? this.queryParams.receiveTime[1] : null,//结束执行时间
          };
          Cookies.set('business_earning_report',JSON.stringify(this.queryParams));
          const recordList_res = await sellcardReportList(request);
          if (recordList_res.code === 200) {
            this.recordList = recordList_res.rows;
            this.total = recordList_res.total;
            this.loading = false;
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: false,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
        }
        this.handleQuery();
      },
      // 查看详情弹窗
      async handleDetail(row) {
        const detail_res = await sellcardReportDetail(row.rechargeId);
        if (detail_res.code === 200) {
          this.giftCardRechargeRecordList = detail_res.data.giftCardRechargeRecordList;
          this.sellcardReportDetailsVisible = true;
          this.sellcardReportDetailsForm = detail_res.data;
        }
      },
      formatRechargeStatus(status) {
        let statusName = '';
        if (status) {
          let rechargeItme = this.dict.type.recharge_status.find(itme => {
            return itme.value === status;
          })
          if (rechargeItme) {
            statusName = rechargeItme.label;
          }
        }
        return statusName
      },
      detailCancel() {
        this.sellcardReportDetailsVisible = false;
      },
      copyToClip(content) {
        const input = document.createElement('input');
        input.setAttribute('value', content);
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$modal.msgSuccess("已复制到黏贴版");

      },
      selectFn(selection, row) {
        let flag = selection.some((itme) => {
          return itme.rechargeId === row.rechargeId;
        })
        const index = this.arrFindObjIndex(this.acids, row, "rechargeId");
        if (!flag) {
          if (index !== -1) {
            this.acids.splice(index, 1);
          }
        } else {
          if (index === -1) {
            this.acids.push(row.rechargeId);
          }
        }
      },
      selectAllFn(selection) {
        if (!selection.length) {
          this.recordList.forEach(itme => {
            const index = this.arrFindObjIndex(this.acids, itme, "rechargeId");
            if (index !== -1) {
              this.acids.splice(index, 1);
            }
          })
        } else {
          selection.forEach(itme => {
            const index = this.arrFindObjIndex(this.acids, itme, "rechargeId");
            if (index === -1) {
              this.acids.push(itme.rechargeId);
            }
          })

        }

      },
      arrFindObjIndex(list, obj, key) {
        let index = -1;
        list.forEach((itme, idx) => {
          if (itme[key] === obj[key]) {
            index = idx;
          }
        })
        return index;
      },
      getLimit(limitStr) {
        if (limitStr) return limitStr.split(',').length;
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3, 4, 5, 6, 8, 10, 13] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
      getSubSummaries(param) {
        const notTotals = [1, 2, 5, 6] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
<style>
  .el-row {
    margin-bottom: 25px;
    color: #606266;
    font-size: 14px;
    font-weight: 700;
  }
</style>
