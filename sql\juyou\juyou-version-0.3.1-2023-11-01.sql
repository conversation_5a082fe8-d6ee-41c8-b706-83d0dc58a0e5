-- 出售表添加核对状态
ALTER TABLE `two_account_sell` ADD COLUMN `write_off_status` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '核销状态：1 未核对 2 已核对' AFTER `buy_price`;

-- 初始化核对状态
UPDATE two_account_sell ase
set ase.write_off_status = 1
;

-- 添加菜单权限
-- 批量核对
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2068, '批量核对', 2056, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:accountSellSoldReport:batchWriteOff', '#', 'admin', '2023-11-01 15:19:49', '', NULL, '');
-- 修改出售群
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2069, '修改出售群', 2056, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:accountSellSoldReport:updateSellChatgroupName', '#', 'admin', '2023-11-01 17:19:06', '', NULL, '');
