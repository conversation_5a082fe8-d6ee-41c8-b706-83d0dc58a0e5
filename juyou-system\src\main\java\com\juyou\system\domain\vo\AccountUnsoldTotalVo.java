package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账号未出售合计(累计)-vo
 */
@ApiModel("AccountUnsoldTotalVo-未出售合计(累计)")
@Data
public class AccountUnsoldTotalVo implements Serializable {

    private static final long serialVersionUID = -7309462106843305833L;

    @ApiModelProperty("剩余实际充值总额")
    private BigDecimal remainingActualRechargeTotal;

    private List<ZoneGroupVo> list;

    @ApiModelProperty("剩余成本总额")
    private BigDecimal remainingCostTotal;

}
