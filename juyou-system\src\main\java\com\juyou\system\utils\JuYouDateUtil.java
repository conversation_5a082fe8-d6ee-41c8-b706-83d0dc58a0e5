package com.juyou.system.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.juyou.system.domain.vo.ClassesDateRangeVo;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * juyou日期工具类
 */

@Slf4j

public class JuYouDateUtil {

    /**
     * 根据当前时间获取白班，晚班时间段
     *
     * @return
     */
    public static ClassesDateRangeVo getClassesBetweenDate() {
        // 新需求
        //now
        //今天10点前: [昨天10:00,今天9:59]
        //今天10点后:[今天10点:明天9:59]

        ClassesDateRangeVo vo = new ClassesDateRangeVo();

        Date now = new Date();

        // 今天10点
        DateTime tenTime = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 10:00:00"); //当前时间与十点进行比较

        if (now.before(tenTime)) {
            // 今天十点前
            DateTime startDate = DateUtil.offsetDay(tenTime, -1);
            DateTime endDate = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 09:59:59");
            vo.setStartDate(startDate);
            vo.setEndDate(endDate);
        } else {
            // 今天十点后
            DateTime startDate = tenTime;
            DateTime endDate = DateUtil.parseDateTime(DateUtil.formatDate(DateUtil.offsetDay(tenTime, 1)) + " 09:59:59");
            vo.setStartDate(startDate);
            vo.setEndDate(endDate);
        }

        return vo;

        //今天10点前: [昨天晚上22:00,今天9:59]
        //今天10点后 and 今天22:00前:[今天10点:今天21:59]
        //今天22点后:[今天22:00:明天9:59]
        /*ClassesDateRangeVo vo = new ClassesDateRangeVo();

        Date now = new Date();

        // Date now = DateUtil.parseDateTime("2023-09-21 01:11:11");
        // 白天
        DateTime cWhiteTenDate = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 10:00:00");

        // 晚上
        DateTime nightTenDate = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 21:59:59");

        if (now.before(cWhiteTenDate)) {
            DateTime startDate = DateUtil.offsetDay(now, -1);
            DateTime startDateTime = DateUtil.parseDateTime(DateUtil.formatDate(startDate) + " 22:00:00");
            vo.setStartDate(startDateTime);
            DateTime endDateTime = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 09:59:59");
            vo.setEndDate(endDateTime);
        } else if (now.after(nightTenDate)) {
            DateTime startDateTime = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 22:00:00");
            vo.setStartDate(startDateTime);
            DateTime endDate = DateUtil.offsetDay(now, 1);
            vo.setEndDate(DateUtil.parseDateTime(DateUtil.formatDate(endDate) + " 09:59:59"));
        } else {
            DateTime startDateTime = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 10:00:00");
            vo.setStartDate(startDateTime);
            DateTime endDateTime = DateUtil.parseDateTime(DateUtil.formatDate(now) + " 21:59:59");
            vo.setEndDate(endDateTime);
        }
        return vo;*/
    }

    public static void main(String[] args) {
        ClassesDateRangeVo vo = getClassesBetweenDate();
        log.info("classes:{}", vo);
    }


}
