<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.TwoRefundRecordsMapper">
    
    <resultMap type="TwoRefundRecords" id="TwoRefundRecordsResult">
        <result property="id"    column="id"    />
        <result property="acid"    column="acid"    />
        <result property="deptId"    column="dept_id"    />
        <result property="receiveUserId"    column="receive_user_id"    />
        <result property="receiveUser"    column="receive_user"    />
        <result property="buyAmt"    column="buy_amt"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="backReason"    column="back_reason"    />
    </resultMap>

    <sql id="selectTwoRefundRecordsVo">
        select id, acid, dept_id, receive_user_id, receive_user, buy_amt, create_by, create_time, update_by, update_time, remark, back_reason from two_refund_records
    </sql>

    <select id="selectTwoRefundRecordsList" parameterType="TwoRefundRecords" resultMap="TwoRefundRecordsResult">
        <include refid="selectTwoRefundRecordsVo"/>
        <where>  
            <if test="acid != null "> and acid = #{acid}</if>
            <if test="receiveUserId != null "> and receive_user_id = #{receiveUserId}</if>
            <if test="receiveUser != null  and receiveUser != ''"> and receive_user = #{receiveUser}</if>
            <if test="buyAmt != null "> and buy_amt = #{buyAmt}</if>
            <if test="backReason != null  and backReason != ''"> and back_reason = #{backReason}</if>
        </where>
    </select>
    
    <select id="selectTwoRefundRecordsById" parameterType="Long" resultMap="TwoRefundRecordsResult">
        <include refid="selectTwoRefundRecordsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTwoRefundRecords" parameterType="TwoRefundRecords" useGeneratedKeys="true" keyProperty="id">
        insert into two_refund_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="acid != null">acid,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="receiveUser != null">receive_user,</if>
            <if test="buyAmt != null">buy_amt,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="backReason != null">back_reason,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="acid != null">#{acid},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
            <if test="receiveUser != null">#{receiveUser},</if>
            <if test="buyAmt != null">#{buyAmt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="backReason != null">#{backReason},</if>
         </trim>
    </insert>

    <update id="updateTwoRefundRecords" parameterType="TwoRefundRecords">
        update two_refund_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="acid != null">acid = #{acid},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="receiveUser != null">receive_user = #{receiveUser},</if>
            <if test="buyAmt != null">buy_amt = #{buyAmt},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="backReason != null">back_reason = #{backReason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTwoRefundRecordsById" parameterType="Long">
        delete from two_refund_records where id = #{id}
    </delete>

    <delete id="deleteTwoRefundRecordsByIds" parameterType="String">
        delete from two_refund_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>