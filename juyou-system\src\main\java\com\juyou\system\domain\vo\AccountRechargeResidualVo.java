package com.juyou.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号充值-可领取-vo
 */
@Data
@ApiModel("AccountRechargeResidualVo")
public class AccountRechargeResidualVo implements Serializable {

    private static final long serialVersionUID = 6582278133204338862L;

    @ApiModelProperty("一级账号列表")
    private List<AccountSellResidualAccountVoList> oneList;

    @ApiModelProperty("二级账号列表")
    private List<AccountSellResidualAccountVoList> twoList;

    @ApiModelProperty("我的可领取账号")
    private List<AccountSellResidualAccountVoList> myList;


}
