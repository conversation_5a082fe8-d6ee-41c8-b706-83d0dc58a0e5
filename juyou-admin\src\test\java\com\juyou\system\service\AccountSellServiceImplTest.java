package com.juyou.system.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.juyou.common.core.domain.entity.SysDictData;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.AccountRechargeVo;
import com.juyou.system.domain.vo.AccountSellResidualAccountVoList;
import com.juyou.system.domain.vo.AccountUnsoldTotalVo;
import com.juyou.system.domain.vo.SellChatgroupVo;
import com.juyou.system.mapper.AccountRechargeMapper;
import com.juyou.system.mapper.AccountSellMapper;
import com.juyou.system.params.SellChatgroupParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class AccountSellServiceImplTest {

    @Autowired
    private IAccountSellService accountSellService;
    @Autowired
    private ISysDictDataService iSysDictDataService;
    @Autowired
    private  AccountRechargeMapper  accountRechargeMapper;
    @Autowired
    private AccountSellMapper  accountSellMapper;

    @Test
    public void selectByAcid(){
        Integer acid = 8;
        AccountSell sell = this.accountSellService.selectByAcid(acid);
        log.info("sell:{}", JSONUtil.toJsonPrettyStr(sell));
    }

    @Test
    public void getSellChatgroupList(){
        SellChatgroupParam param = new SellChatgroupParam();
        param.setStartSellTime(DateUtil.parseDateTime("2023-09-12 16:19:47"));
        param.setEndSellTime(DateUtil.parseDateTime("2023-09-15 16:19:47"));
        List<SellChatgroupVo> list = this.accountSellService.getSellChatgroupList(param);
        log.info("list:{}", JSONUtil.toJsonPrettyStr(list));
    }

    @Test
    public void AccountUnsoldTotalVo(){
        AccountUnsoldTotalVo vo = this.accountSellService.getUnsoldTotal();
        log.info("vo:{}", JSONUtil.toJsonPrettyStr(vo));
    }
    
    @Test
    public void cancel(){
        //this.accountSellService.cancel(4);
    }
    @Test
    public void AccountSell(){
        String idType = "1";
        Map<String, Integer> map = new HashMap<>();
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("account_zone");
        List<SysDictData> data = iSysDictDataService.selectDictDataList(sysDictData);
        List<AccountRechargeVo> list = accountSellService.selectAccountRechargeVoList(idType);
        list.forEach(item -> {
            map.put(item.getCountry(), item.getQuantity());
        });
        for (SysDictData item : data){
            if (map.get(item.getDictLabel()) != null){
                item.setDictValue(map.get(item.getDictLabel()).toString());
            } else {
                item.setDictValue("0");
            }
        }
        log.info("data:{}", JSONUtil.toJsonPrettyStr(data));
        //this.accountSellService.updateSellChatgroupName(1);
    }

    @Test
    public void AccountSellResidualAccountVoListTest(){
        AccountRechargeVo param=new AccountRechargeVo();

        List<AccountSellResidualAccountVoList> list = this.accountSellService.getSystemResidualAccountList("美国", String.valueOf(0));
        list.forEach(item -> {
            log.info("item:{}", JSONUtil.toJsonPrettyStr(item));
        });

    }
    @Test
    public void putAccountRechargeTest(){
        List<AccountSell> list = new ArrayList<>();
        AccountSell accountSell = new AccountSell();
        accountSell.setAcid(6);
        list.add(accountSell);
        accountSellService.putAccountRecharge(list,"1");

    }
    @Test
    public void obsolescenceRateTest(){

        //log.info("liu 的作废率 ： "+ accountSellMapper.getSellVoidedBalances());
    }
}
