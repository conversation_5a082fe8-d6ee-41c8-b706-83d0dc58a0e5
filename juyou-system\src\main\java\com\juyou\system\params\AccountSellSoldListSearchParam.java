package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号出售已出售Search-param
 */
@Data
@ApiModel("AccountSellSoldListSearchParam")
public class AccountSellSoldListSearchParam implements Serializable {

    private static final long serialVersionUID = 4300812671826450136L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("售卡状态：1 未生效 2 待出售 3 已出售 4 作废")
    private String status;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellDate;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellDate;

}
