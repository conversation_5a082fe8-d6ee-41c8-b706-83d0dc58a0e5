package com.juyou.web.controller.business;

import cn.hutool.core.util.StrUtil;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.vo.AccountRechargeNotEffectiveListVo;
import com.juyou.system.params.AccountRechargeNotEffectiveParam;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "账号充值未生效账号", tags = "账号充值为生效账号")
@RestController
@RequestMapping("/accountRecharge/notEffective")
public class AccountRechargeNotEffectiveController extends BaseController {

    @Autowired
    private IAccountRechargeService accountRechargeService;
    @Autowired
    private ISysConfigService sysConfigService;

    @ApiOperation("未生效账号列表")
    @GetMapping("/notEffectiveList")
    public TableDataInfo<AccountRechargeNotEffectiveListVo> notEffectiveList(AccountRechargeNotEffectiveParam param){
        startPage();
        String onwPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(onwPendingMinutes)){
            throw new ServiceException("id2-账号充值-一级库等待分钟没有配置!");
        }
        param.setOnePendingMinutes(Integer.valueOf(onwPendingMinutes));

        String twoPendingMinutes = sysConfigService.selectConfigByKey(ConfigConstant.ID2_ACCOUNT_RECHARGE_TWO_LEVEL_PENDING_MINUTES);
        if(StrUtil.isBlank(twoPendingMinutes)){
            throw new ServiceException("id2-账号充值-二级库等待分钟没有配置!");
        }
        param.setTwoPendingMinutes(Integer.valueOf(twoPendingMinutes));

        List<AccountRechargeNotEffectiveListVo> voList = this.accountRechargeService.notEffectiveList(param);
        return getDataTable(voList);
    }

    @ApiOperation("批量生效账号")
    @PutMapping("/updateNotEffectiveList")
    public ResultData<Boolean> updateNotEffectiveList(@RequestBody AccountRechargeNotEffectiveParam param){
        // 账号充值的id集合
        Integer[] rechargeIds = param.getRechargeIds();
        boolean b = this.accountRechargeService.updateNotEffectiveList(rechargeIds);
        return ResultUtil.success(b);
    }

}
