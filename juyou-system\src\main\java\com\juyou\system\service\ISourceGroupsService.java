package com.juyou.system.service;

import java.util.List;
import com.juyou.system.domain.SourceGroups;

/**
 * 来源群信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
public interface ISourceGroupsService
{
    /**
     * 查询来源群信息
     *
     * @param id 来源群信息主键
     * @return 来源群信息
     */
    public SourceGroups selectSourceGroupsById(Long id);

    /**
     * 查询来源群信息列表
     *
     * @param sourceGroups 来源群信息
     * @return 来源群信息集合
     */
    public List<SourceGroups> selectSourceGroupsList(SourceGroups sourceGroups);

    /**
     * 新增来源群信息
     *
     * @param sourceGroups 来源群信息
     * @return 结果
     */
    public int insertSourceGroups(SourceGroups sourceGroups);

    /**
     * 修改来源群信息
     *
     * @param sourceGroups 来源群信息
     * @return 结果
     */
    public int updateSourceGroups(SourceGroups sourceGroups);

    /**
     * 批量删除来源群信息
     *
     * @param ids 需要删除的来源群信息主键集合
     * @return 结果
     */
    public int deleteSourceGroupsByIds(Long[] ids);

    /**
     * 删除来源群信息信息
     *
     * @param id 来源群信息主键
     * @return 结果
     */
    public int deleteSourceGroupsById(Long id);
}
