package com.juyou.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juyou.common.annotation.DataScope;
import com.juyou.common.constant.SysEnable;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.DateUtils;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.constants.LogTypeConstant;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.*;
import com.juyou.system.enums.AccountRechArgeChargeStageEnum;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.enums.AccountSellEnum;
import com.juyou.system.enums.AccountSellWriteOffStatusEnum;
import com.juyou.system.log.RechargeLog;
import com.juyou.system.mapper.AccountRechargeMapper;
import com.juyou.system.mapper.AccountSellMapper;
import com.juyou.system.mapper.GiftCardRechargeRecordMapper;
import com.juyou.system.params.*;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IAccountSellService;
import com.juyou.system.service.ISysConfigService;
import com.juyou.system.service.ITwoRefundRecordsService;
import com.juyou.system.utils.JuYouDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLIntegrityConstraintViolationException;
import java.sql.SQLNonTransientException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * sellcardService业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AccountSellServiceImpl extends ServiceImpl<AccountSellMapper, AccountSell> implements IAccountSellService {

    @Autowired
    private AccountSellMapper accountSellMapper;
    @Autowired
    private IAccountRechargeService accountRechargeService;
    @Autowired
    private ISysConfigService iSysConfigService;

    @Autowired
    private ITwoRefundRecordsService iTwoRefundRecordsServiceImpl;

    @Autowired
    private GiftCardRechargeRecordMapper giftCardRechargeRecordMapper;

    @Override
    public Integer updateSellChatgroupName(AccountSellUpdateChatgroupNameParam param) {
        AccountSell sell = new AccountSell();
        sell.setRechargeId(param.getRechargeId());
        sell.setSellChatgroupName(param.getSellChatgroupName());
        return this.accountSellMapper.updateAccountSell(sell);
    }

    @Override
    public Integer batchWriteOff(AccountSellBatchWriteOffParam param) {

        if (CollUtil.isNotEmpty(param.getRechargeIdList())) {
            List<AccountSell> list = param.getRechargeIdList().stream().map(item -> {
                AccountSell sell = new AccountSell();
                sell.setRechargeId(item);
                sell.setWriteOffStatus(AccountSellWriteOffStatusEnum.CHECKED.getCode());
                return sell;
            }).collect(Collectors.toList());
            int i = 0;
            for (AccountSell item : list) {
                i += this.accountSellMapper.updateAccountSell(item);
            }
            return i;
        }

        return null;
    }

    @Override
    public List<AccountSell> getAccountSellSoldList(AccountSellSoldListSearchParam param) {
        return this.accountSellMapper.getAccountSellSoldList(param);
    }

    @Override
    public List<AccountSellVo> getAccountSellSoldReportList(AccountSellPendingSaleSearchParam param) {
        List<AccountSellVo> listVo = this.accountSellMapper.getAccountSellSoldReportList(param);
        if (CollUtil.isNotEmpty(listVo)) {
            listVo.forEach(item -> {
                AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(item.getAcid());
                if (ObjUtil.isNotNull(accountRecharge)) {
                    item.setRechargeAmt(BigDecimal.valueOf(accountRecharge.getRechargeAmt()));
                }
            });
        }
        return listVo;
    }

    @Override
    public List<AccountSellVo> getAccountSellPendingSaleList(AccountSellPendingSaleSearchParam param) {
        return this.accountSellMapper.getAccountSellPendingSaleList(param);
    }

    /**
     * 获取账号出售待生效分钟数配置没有获取到
     *
     * @return
     */
    private Integer getAccountSellPendingMinutes() {
        //查询参数配置信息
        String accountSellPendingMinutesStr = this.iSysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES);
        if (StrUtil.isBlank(accountSellPendingMinutesStr)) {
            throw new ServiceException("账号出售待生效分钟数配置没有获取到!");
        }
        log.info("获取账号出售待生效分钟:{}", accountSellPendingMinutesStr);
        return Integer.valueOf(accountSellPendingMinutesStr);
    }

    private Integer getAccountSellPendingMinutesTwo() {
        //查询参数配置信息
        String accountSellPendingMinutesStr = this.iSysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES_TWO);
        if (StrUtil.isBlank(accountSellPendingMinutesStr)) {
            throw new ServiceException("账号出售待生效分钟数配置没有获取到!");
        }
        log.info("获取账号出售待生效分钟:{}", accountSellPendingMinutesStr);
        return Integer.valueOf(accountSellPendingMinutesStr);
    }

    @Override
    public List<AccountSellResidualAccountVoList> getSystemResidualAccountList(String accountZone, String idType) {
        Integer accountSellPendingMinutes = this.getAccountSellPendingMinutes();
        Integer accountSellPendingMinutesTwo = this.getAccountSellPendingMinutesTwo();
        return this.accountSellMapper.getSystemResidualAccountList(accountSellPendingMinutes, accountZone, idType, accountSellPendingMinutesTwo);
    }

    @Override
    public List<AccountSellResidualAccountVoList> getSystemResidualAccountLeiSheList(String accountZone, String idType, String walletArea) {
        Integer accountSellPendingMinutes = this.getAccountSellPendingMinutes();
        return this.accountSellMapper.getSystemResidualAccountLeiSheList(accountSellPendingMinutes, accountZone, walletArea, idType);
    }

    @Override
    public int putAccountRecharge(List<AccountSell> accountSell, String level) {
        accountSell.forEach(item -> {

            AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(item.getRechargeId());
            if (oldAcc != null) {

                if (!oldAcc.getStatus().equals(AccountSellEnum.STATUS_2.getStatus())) {
                    throw new ServiceException("请选择待出售ID");
                }

            }
            item.setAcid(oldAcc.getAcid());
            RechargeLog.insertSell(oldAcc, item, LogTypeConstant.POST_TWO_STORAGE);

            AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(item.getAcid());
            if (accountRecharge.getChargeStage().equals(level)) {
                throw new ServiceException("请选择一级充值阶段ID");
            }
            accountRecharge.setReceiveUserId(null);
            accountRecharge.setReceiveUser(null);
            accountRecharge.setStatus(AccountRechargeEnum.STATUS_0.getCode());
            accountRecharge.setChargeStage(level);

            accountRechargeService.putAccountRecharge(accountRecharge);
            accountSellMapper.deleteAccountSellByAcid(item.getAcid());
        });
        return 1;
    }

    @Override
    public int putAccountRechargeOne(List<AccountSell> accountSell, String level) {
        accountSell.forEach(item -> {
            AccountSell oldAcc = accountSellMapper.selectOldAccountSellByRechargeId(item.getRechargeId());
            item.setStatus(AccountSellEnum.STATUS_3.getStatus());
            accountSellMapper.updateOldAccountSellStatus(item); //修改老系统中的状态

            AccountRecharge accountRecharge = new AccountRecharge();
            accountRecharge.setAccountName(oldAcc.getAccountName());
            accountRecharge.setAccountZone(oldAcc.getAccountZone());
            accountRecharge.setAccountPwd(oldAcc.getAccountPwd());
            accountRecharge.setRechargeAmt(oldAcc.getCardBalance());

            accountRecharge.setReceiveUserId(null);
            accountRecharge.setReceiveUser(null);
            accountRecharge.setStatus(AccountRechargeEnum.STATUS_0.getCode());
            accountRecharge.setChargeStage(level);
            try {
                item.setAcid(oldAcc.getAcid());
                RechargeLog.insertSell(oldAcc, item, LogTypeConstant.POST_NON_STORAGE);
                accountRechargeService.insertAccountRecharge(accountRecharge);
            } catch (RuntimeException e) {
                throw new RuntimeException(oldAcc.getAccountName() + "账号已经存在");
            }
            giftCardRechargeRecordMapper.getOldAccountCaedRechargeInfo(oldAcc.getAccountName()).forEach(it -> {
                it.setId(null);
                giftCardRechargeRecordMapper.insertGiftCardRechargeRecord(it);
            });

        });
        return 1;
    }

    @Override
    public int returnAccountSell(AccountSell accountSell) {
        //使用作废原因 储存退回原因
        if (accountSell.getCancelReason() == null) {
            throw new ServiceException("请输入退回原因");
        }
        AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(accountSell.getRechargeId());
        accountSell.setAcid(oldAcc.getAcid());
        RechargeLog.insertSell(oldAcc, accountSell, LogTypeConstant.RETURN);

        AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(accountSell.getAcid());

        accountRecharge.setStatus(AccountRechargeEnum.STATUS_2.getCode());
        accountRecharge.setBackReason(accountSell.getCancelReason());
        accountRecharge.setHasBack(SysEnable.EANBLE_Y);

        accountRechargeService.putAccountRecharge(accountRecharge);

        TwoRefundRecords returnRecords = new TwoRefundRecords();
        //退回的ID
        returnRecords.setAcid(Long.valueOf(accountSell.getAcid()));
        //退回原因
        returnRecords.setBackReason(accountSell.getCancelReason());
        //退回人
        returnRecords.setReceiveUser(accountRecharge.getReceiveUser());
        //退回人ID
        returnRecords.setReceiveUserId(accountRecharge.getReceiveUserId());
        //退回的金额
        returnRecords.setBuyAmt(oldAcc.getCardBalance());
        //退回时间
        returnRecords.setCreateTime(DateUtil.date());
        //退回的人的部门
        returnRecords.setDeptId(accountRecharge.getDeptId());

        List<Integer> rechargeIds = new ArrayList<>();
        rechargeIds.add(accountSell.getRechargeId());
        accountSellMapper.batchUpdateAccountSellStatus(rechargeIds, "-1");
        accountSellMapper.deleteAccountSellByAcid(accountSell.getAcid());

        return iTwoRefundRecordsServiceImpl.insertTwoRefundRecords(returnRecords);

    }

    @Override
    public int restore(AccountSell accountSell) {
        AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(accountSell.getRechargeId());
        accountSell.setAcid(oldAcc.getAcid());
        RechargeLog.insertSell(oldAcc, accountSell, LogTypeConstant.RECOVERY);

        AccountSell accountSell1 = accountSellMapper.selectAccountSellByRechargeId(accountSell.getRechargeId());
        accountSell1.setStatus(AccountSellEnum.STATUS_2.getStatus());
        accountSell1.setUpdateTime(DateUtil.date());
        return accountSellMapper.updateAccountSell(accountSell1);
    }

    @Override
    public List<AccountSell> selectAccountSellListAll(AccountSellPageParam param) {

        return accountSellMapper.selectAccountSellListAll(param);
    }

    @Override
    public int putOldAccountRechargeToNewTwoAccountRecharge(List<AccountSell> accountSell, String level) {
        accountSell.forEach(item -> {

            AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(item.getRechargeId());
//          item.setAcid(oldAcc.getAcid());
//          RechargeLog.insertSell(oldAcc, item, LogTypeConstant.RETURN);
            AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(item.getAcid());
            accountRecharge.setReceiveUserId(null);
            accountRecharge.setReceiveUser(null);
            accountRecharge.setStatus(AccountRechargeEnum.STATUS_0.getCode());
            accountRecharge.setChargeStage(level);
            accountRechargeService.putAccountRecharge(accountRecharge);
            accountSellMapper.deleteAccountSellByAcid(item.getAcid());
        });
        return 1;

    }

    @Override
    public BigDecimal getTodayAmountSold(Long userId, String accountZone, String idType) {
        ClassesDateRangeVo vo = JuYouDateUtil.getClassesBetweenDate();
        return this.accountSellMapper.getTodayAmountSold(userId, vo.getStartDate(), vo.getEndDate(), accountZone, idType);
    }

    @Override
    public BigDecimal getSellVoidedBalances(Long userId) {

        return accountSellMapper.getSellVoidedBalances(userId);
    }

    @Override
    public BigDecimal getSellVoidedBalancesDate(Long userId, DateRangeParam dateRangeParam) {
        //默认是当天时间
        if (dateRangeParam.getEnd() == null || dateRangeParam.getStart() == null) {
            LocalDateTime start = LocalDate.now().atStartOfDay();
            LocalDateTime end = LocalDate.now().atTime(23, 59, 59);
            dateRangeParam.setStart(String.valueOf(start));
            dateRangeParam.setEnd(String.valueOf(end));
        }
        return accountSellMapper.getSellVoidedBalancesDate(userId, dateRangeParam);
    }

    @Override
    public BigDecimal getAwaitReceiveTotalAmount(String accountZone, String idType) {
        Integer accountSellPendingMinutes = this.getAccountSellPendingMinutes();
        return this.accountSellMapper.getAwaitReceiveTotalAmount(accountSellPendingMinutes, accountZone, idType);
    }

    @Override
    public List<SellChatgroupVo> getSellChatgroupList(SellChatgroupParam param) {
        return this.accountSellMapper.getSellChatgroupList(param);
    }

    @Override
    public AccountUnsoldTotalVo getUnsoldTotal() {
        AccountUnsoldTotalVo vo = new AccountUnsoldTotalVo();
        List<ZoneGroupVo> remainingActualRechargeTotal = this.accountSellMapper.getRemainingActualRechargeTotal();
        vo.setList(remainingActualRechargeTotal);
        BigDecimal remainingCostTotal = this.accountSellMapper.getRemainingCostTotal();
        vo.setRemainingCostTotal(remainingCostTotal);
        return vo;
    }

    @Override
    public int transferAccount(AccountTransferAccountParam param) {

        int i = this.accountSellMapper.transferAccount(param);

        if (i == 0) {
            throw new ServiceException("该账号名下没有待转交的账号的数据！");
        }

        return i;
    }

    @Override
    public int receiveAccount(int count, List<Double> cardBalanceList, Long userId, String userName, Long deptId, String chargeStage) {

        // 查询超出配置的参数分钟的未生效账号=可领取账号
        Integer accountSellPendingMinutes = this.getAccountSellPendingMinutes();

        List<AccountSell> list = this.accountSellMapper.selectCanBeClaimedAccount(accountSellPendingMinutes, count, cardBalanceList, chargeStage);

        if (CollUtil.isNotEmpty(list)) {
            if (list.size() < count) {
                throw new ServiceException("可领取的账号数量不足,只有" + list.size() + "条!");
            }
            List<Integer> ids = list.stream().map(AccountSell::getRechargeId).collect(Collectors.toList());
            int i = this.accountSellMapper.updateReceiveAccount(ids, userId, userName, deptId);
            return i;
        }
        throw new ServiceException("没有可领取的账号余额!");
    }

    @Override
    public int DesignationReceiveAccount(int count, List<Double> cardBalanceList, Long userId, String userName, Long deptId, String walletArea) {

        // 查询超出配置的参数分钟的未生效账号=可领取账号
        Integer accountSellPendingMinutes = this.getAccountSellPendingMinutes();
        int maxNumber = Integer.parseInt(iSysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL));
        int currNumber = accountSellMapper.filterTheNumberOfClaimedAndUnsoldAccounts(userId);
        if ((currNumber + count) > maxNumber) {
            throw new ServiceException("超出可领取数量,当前可领取" + (maxNumber - currNumber) + "条");
        }
        List<AccountSell> list = this.accountSellMapper.selectCanBeClaimedAccountByWalletArea(accountSellPendingMinutes, count, cardBalanceList, walletArea);
        if (CollUtil.isNotEmpty(list)) {
            if (list.size() < count) {
                throw new ServiceException("可领取的账号数量不足,只有" + list.size() + "条!");
            }
            List<Integer> ids = list.stream().map(AccountSell::getRechargeId).collect(Collectors.toList());
            int i = this.accountSellMapper.updateReceiveAccount(ids, userId, userName, deptId);
            return i;
        }

        throw new ServiceException("没有可领取的账号余额!");
    }

    @Override
    public int updateNotEffectiveCompletedTimeMinutesNum() {
        return this.accountSellMapper.updateNotEffectiveCompletedTimeMinutesNum();
    }

    @Override
    public List<AccountSellVo> notEffectiveList(AccountSellNotEffectiveParam param) {
        List<AccountSellVo> list = this.accountSellMapper.notEffectiveList(param);
        this.assign(list);
        return list;
    }

    /**
     * 赋值
     *
     * @param list
     * @return
     */
    private void assign(List<AccountSellVo> list) {
        if (CollUtil.isNotEmpty(list)) {
            for (AccountSellVo sell : list) {
                // 计算完成时间分钟数(账号在未生效持续的时间,单位分钟) 变成 小时
                if (ObjUtil.isNotNull(sell.getCompletedTimeMinutesNum())) {
                    BigDecimal hour = BigDecimal.valueOf(sell.getCompletedTimeMinutesNum()).divide(BigDecimal.valueOf(60), RoundingMode.HALF_UP);
                    sell.setCompletedTimeHourNum(hour);
                }
            }
        }
    }

    @Override
    public List<AccountSell> selectToBeEffectiveList() {
        return this.accountSellMapper.selectToBeEffectiveList();
    }

    @Override
    public AccountSell selectByAcid(Integer acid) {
        LambdaQueryWrapper<AccountSell> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountSell::getAcid, acid);
        AccountSell one = this.getOne(wrapper);
        return one;
    }

    /**
     * 查询sellcard
     *
     * @param rechargeId sellcard主键
     * @return sellcard
     */
    @Override
    public AccountSell selectAccountSellByRechargeId(Integer rechargeId) {
        return accountSellMapper.selectAccountSellByRechargeId(rechargeId);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountSell> selectAccountReportList(AccountSellPageParam param) {
        // 没有传领取时间条件，默认为当前班次时间范围
        /*ClassesDateRangeVo vo = JuYouDateUtil.getClassesBetweenDate();
        if (ObjUtil.isNull(param.getStartReceiveTime()) && ObjUtil.isNull(param.getEndReceiveTime())) {
            param.setStartReceiveTime(vo.getStartDate());
            param.setEndReceiveTime(vo.getEndDate());
        }*/
        return this.accountSellMapper.selectAccountReportList(param);
    }

    /**
     * 查询sellcard列表
     *
     * @param param sellcard
     * @return sellcard
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountSell> selectAccountSellList(AccountSellPageParam param) {
        if (param.getStatus() == null) {
            param.setStatus("2");
        }
        if (param.getStatus().equals("1")) {
            param.setStatus(null);
        }
        return accountSellMapper.selectAccountSellList(param);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountSell> selectAccountSellListByAnchor(SellAnchorVo param) {
        return accountSellMapper.selectAccountSellListByAnchor(param);
    }

    /**
     * 新增sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    @Override
    public int insertAccountSell(AccountSell accountSell) {
        accountSell.setCreateTime(DateUtils.getNowDate());
        return accountSellMapper.insertAccountSell(accountSell);
    }

    /**
     * 修改sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    @Override
    public int updateAccountSell(AccountSell accountSell) {
        //已作废不能修改成其他字段
        if (ObjUtil.isNull(accountSell.getRechargeId())) {
            throw new ServiceException("rechargeId唯一标识不能为空!");
        }
        AccountSell sell = this.selectAccountSellByRechargeId(accountSell.getRechargeId());
        if (AccountSellEnum.STATUS_4.getStatus().equals(sell.getStatus())) {
            throw new ServiceException("已作废的账号不能修改!");
        }
        AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(accountSell.getRechargeId());
        accountSell.setAcid(oldAcc.getAcid());
        RechargeLog.insertSell(oldAcc, accountSell, LogTypeConstant.SELL);
        accountSell.setUpdateTime(DateUtils.getNowDate());
        accountSell.setHasEdit(SysEnable.EANBLE_Y);
        return accountSellMapper.updateAccountSell(accountSell);
    }

    @Override
    public int updateAccountSellByAnchor(Integer[] ids) {
        AccountSell accountSell = new AccountSell();
        int row = 0;
        for (Integer id : ids) {
            accountSell.setRechargeId(id);
            accountSell.setIsUse("1");
            accountSell.setUseTime(DateUtils.getNowDate());
            accountSell.setUpdateTime(DateUtils.getNowDate());
            row = accountSellMapper.updateAccountSell(accountSell);
        }
        return row;
    }

    @Override
    public int updateAccountSellByAnchorRecovery(Integer id) {
        AccountSell accountSell = new AccountSell();
        accountSell.setRechargeId(id);
        accountSell.setIsUse("0");
        accountSell.setUpdateTime(DateUtils.getNowDate());
        return accountSellMapper.updateAccountSell(accountSell);
    }

    @Override
    public int updateAccountSellMin(Integer[] ids) {
        AccountSell param = new AccountSell();
        int row = 0;
        for (Integer id : ids) {
            param.setRechargeId(id);
            param.setCompletedTimeMinutesNum(999999.0);
            param.setUpdateTime(DateUtils.getNowDate());

            row = accountSellMapper.updateAccountSell(param);
        }
        return row;
    }

    @Override
    public int batchAccountSell(List<AccountSell> list) {
        if (CollUtil.isNotEmpty(list)) {
            int i = 0;
            for (AccountSell item : list) {
                AccountSell oldAcc = accountSellMapper.selectAccountSellByRechargeId(item.getRechargeId());
                i += this.updateAccountSell(item);
                item.setAcid(oldAcc.getAcid());
                RechargeLog.insertSell(oldAcc, item, LogTypeConstant.SELL);
            }
            return i;
        }
        return 0;
    }

    @Override
    public int batchUpdateAccountSellStatus(List<Integer> rechargeIds, String status) {
        return this.accountSellMapper.batchUpdateAccountSellStatus(rechargeIds, status);
    }

    @Override
    public int cancel(AccountSellCancelParam param) {
        Date now = new Date();

        AccountSell accountSell = accountSellMapper.selectAccountSellByRechargeId(param.getRechargeId());
        RechargeLog.insertSell(accountSell, accountSell, LogTypeConstant.NULLIFY);
        accountSell.setStatus(AccountSellEnum.STATUS_4.getStatus());
        accountSell.setUpdateTime(now);
        accountSell.setUpdateBy(param.getUpdateBy());
        accountSell.setCancelDate(now);
        accountSell.setCancelReason(param.getCancelReason());
        accountSell.setCancelImgs(param.getCancelImgs());

        return this.accountSellMapper.updateAccountSell(accountSell);
    }

    /**
     * 批量删除sellcard
     *
     * @param rechargeIds 需要删除的sellcard主键
     * @return 结果
     */
    @Override
    public int deleteAccountSellByRechargeIds(Integer[] rechargeIds) {
        return accountSellMapper.deleteAccountSellByRechargeIds(rechargeIds);
    }

    @Override
    public int deleteByAcidList(List<Integer> acidList) {
        return this.accountSellMapper.deleteByAcidList(acidList);
    }

    /**
     * 删除sellcard信息
     *
     * @param rechargeId sellcard主键
     * @return 结果
     */
    @Override
    public int deleteAccountSellByRechargeId(Integer rechargeId) {
        return accountSellMapper.deleteAccountSellByRechargeId(rechargeId);
    }

    @Override
    public int updatePinned(AccountSell accountSell) {
        if (accountSell.getPinnedStatus().equals("1")) {
            accountSell.setPinnedStatus("2");
        } else {
            accountSell.setPinnedStatus("1");
        }
        if (accountSell.getRemark() == null || accountSell.getRemark().equals("") || accountSell.getRemark().length() < 1) {
            accountSell.setRemark(null);
        }
        return accountSellMapper.updatePinned(accountSell);
    }

    @Override
//    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AccountRechargeVo> selectAccountRechargeVoList(String idType) {
        return accountSellMapper.selectAccountRechargeVoList(idType);
    }


}
