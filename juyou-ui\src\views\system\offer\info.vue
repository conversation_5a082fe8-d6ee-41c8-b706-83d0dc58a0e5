<template>
  <el-form  style="margin: 0 auto;max-width:900px" :inline="true" :model="form" class="demo-form-inline">
    <el-form-item label="最后更新人：">
      {{form.createBy}}
    </el-form-item>
    <el-form-item style="margin-left: 100px" label="最后更新时间：">
      {{form.createTime}}
    </el-form-item>
    <div>
      <el-form-item label="报价信息：">
        <el-input type="textarea" style="width: 800px" v-model="form.currentInfo" :rows="30" ></el-input>
      </el-form-item>
    </div>
    <el-form-item style="width: 100%;display: flex;justify-content: center">
        <el-button @click="restores">撤回修改</el-button>
        <el-button type="primary" @click="onSubmit(form.currentInfo)">保存并复制</el-button>
        <el-button type="primary" @click="copy(form.currentInfo)">仅复制</el-button>

    </el-form-item>
  </el-form>
</template>

<script>


import {historyAdd, getTheLatestNews,} from "@/api/system/offer";

export default {
  name: 'SubstitutionSettings',
  data() {
    return {
      form:{
        currentInfo: ""
      },
      previousInfo: ""

    }},
    created() {
      getTheLatestNews().then(response => {
         if(response.data != null){
           this.form = {...response.data};
           this. previousInfo= this.form.currentInfo;
         }
      })
    },
  methods: {
    onSubmit(content) {
      console.log(this.form)
      historyAdd(this.form).then(response => {
        this.$modal.msgSuccess("保存成功");

      })
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    copy(content) {
        const input = document.createElement('input');
        input.setAttribute('value', content);
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$modal.msgSuccess("已复制到黏贴版");
      },


    restores() {
      this.form.currentInfo= this.previousInfo;
    }
}


}
</script>
