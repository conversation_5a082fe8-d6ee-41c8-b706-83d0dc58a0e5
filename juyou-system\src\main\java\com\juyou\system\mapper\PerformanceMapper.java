package com.juyou.system.mapper;

import com.juyou.system.domain.vo.AccountZoneAmtVo;
import com.juyou.system.domain.vo.DepartmentVo;
import com.juyou.system.domain.vo.PerformanceDetailedVo;
import com.juyou.system.domain.vo.PerformanceReportIsDetailedVo;
import com.juyou.system.params.CompanyPerformanceQueryParam;

import java.math.BigDecimal;
import java.util.List;

public interface PerformanceMapper {

    //充值剩余余额
    List<AccountZoneAmtVo> getRemainingActualRechargeTotal();
    //出售剩余余额

    //获取剩余成本总额
    BigDecimal getRemainingCostTotal();

    //充值作废率
    BigDecimal getRechargeCancelRate();
    //出售作废率
    BigDecimal getSellCancelRate();
    //退回率
    BigDecimal  getBounceRates();

    //公司树
    List<DepartmentVo> getDepartmentList();

    //充值作废率
    BigDecimal getRechargeCancelRateDetail(CompanyPerformanceQueryParam param);
    //出售作废率
    BigDecimal getSellCancelRateDetail(CompanyPerformanceQueryParam param);




    //退回率
    BigDecimal getBounceRatesDetail(CompanyPerformanceQueryParam param);

    //充值数量
    Integer getRechargeNum(CompanyPerformanceQueryParam param);
    //充值成本总额
    BigDecimal getRechargeCostTotal(CompanyPerformanceQueryParam param);
    //充值作废数量
    Integer getRechargeCancelNum(CompanyPerformanceQueryParam param);
    //充值作废成本总额
    BigDecimal getRechargeCancelCostTotal(CompanyPerformanceQueryParam param);
    //出售数量
    Integer getSellNum(CompanyPerformanceQueryParam param);
    //出售金额总额
    BigDecimal getSellTotalAmount(CompanyPerformanceQueryParam param);
    //出售作废数量
    Integer getSellCancelNum(CompanyPerformanceQueryParam param);
    //出售作废金额总额
    BigDecimal getSellCancelTotalAmount(CompanyPerformanceQueryParam param);
    //详细数据
    List<PerformanceDetailedVo> getPerformanceDetailedVo(CompanyPerformanceQueryParam param);

    PerformanceReportIsDetailedVo getPerformanceReportIsDetailed(CompanyPerformanceQueryParam param);

}
