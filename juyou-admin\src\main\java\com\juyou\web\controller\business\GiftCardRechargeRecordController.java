package com.juyou.web.controller.business;

import cn.hutool.core.collection.CollUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.params.GiftCardRechargeRecordPageParam;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import com.juyou.web.controller.tool.GiftCardRechargeRecordParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 礼品卡充值记录Controller
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@Api(value = "礼品卡充值记录", tags = "礼品卡充值记录")
@RestController
@RequestMapping("/system/record")
public class GiftCardRechargeRecordController extends BaseController {
    @Autowired
    private IGiftCardRechargeRecordService giftCardRechargeRecordService;

    @ApiOperation("查询礼品卡充值记录列表")
    @Log(title = "礼品卡充值记录-查询礼品卡充值记录列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo<GiftCardRechargeRecord> list(GiftCardRechargeRecordPageParam param) {
        startPage();
        List<GiftCardRechargeRecord> list = giftCardRechargeRecordService.selectGiftCardRechargeRecordPage(param);
        return getDataTable(list);
    }


    @ApiOperation("批量新增礼品卡充值记录")
    @Log(title = "礼品卡充值记录-批量新增礼品卡充值记录", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:record:batchAdd')")
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@RequestBody List<GiftCardRechargeRecord> params) {
        if (CollUtil.isEmpty(params)) {
            throw new ServiceException("新增集合不能为空");
        }
        int count = 0;
        for (GiftCardRechargeRecord param : params) {
            count += this.giftCardRechargeRecordService.insertGiftCardRechargeRecord(param);
        }
        return toAjax(count);
    }

    @ApiOperation("导入")
    @Log(title = "礼品卡充值记录-导入", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasAnyPermi('system:record:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<GiftCardRechargeRecord> util = new ExcelUtil<>(GiftCardRechargeRecord.class);
        List<GiftCardRechargeRecord> list = util.importExcel(file.getInputStream());
        Date now = new Date();
        StringBuffer sb = new StringBuffer();
        sb.append("重复账号有:");
        if (CollUtil.isNotEmpty(list)) {
            for (GiftCardRechargeRecord rechargeRecord : list) {
                // check
                GiftCardRechargeRecord giftCardRechargeRecord = new GiftCardRechargeRecord();
                giftCardRechargeRecord.setAccount(rechargeRecord.getAccount());
                giftCardRechargeRecord.setGiftCard(rechargeRecord.getGiftCard());
                List<GiftCardRechargeRecord> repeatList = this.giftCardRechargeRecordService.selectGiftCardRechargeRecordList(giftCardRechargeRecord);
                if (CollUtil.isNotEmpty(repeatList)) {
                    sb.append(rechargeRecord.getAccount()).append(",");
                    continue;
                }
                rechargeRecord.setFaceValue(GiftCardRechargeRecordParseUtil.parseFaceValue(rechargeRecord.getExecutionInfo()));
                rechargeRecord.setBalance(GiftCardRechargeRecordParseUtil.parseBalance(rechargeRecord.getExecutionInfo()));
                rechargeRecord.setCreateTime(now);
                rechargeRecord.setCreateBy(super.getUsername());
                rechargeRecord.setUpdateTime(now);
                rechargeRecord.setUpdateBy(super.getUsername());
                this.giftCardRechargeRecordService.insertGiftCardRechargeRecord(rechargeRecord);
            }
        }
        return AjaxResult.success("导入成功!" + sb.toString());
    }


    @ApiOperation("新增礼品卡充值记录")
    @Log(title = "礼品卡充值记录-新增礼品卡充值记录", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @PostMapping
    public AjaxResult add(@RequestBody GiftCardRechargeRecord giftCardRechargeRecord) {
        return toAjax(giftCardRechargeRecordService.insertGiftCardRechargeRecord(giftCardRechargeRecord));
    }

    @ApiOperation("导出礼品卡充值记录列表")
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "礼品卡充值记录-礼品卡充值记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GiftCardRechargeRecord giftCardRechargeRecord) {
        List<GiftCardRechargeRecord> list = giftCardRechargeRecordService.selectGiftCardRechargeRecordList(giftCardRechargeRecord);
        ExcelUtil<GiftCardRechargeRecord> util = new ExcelUtil<GiftCardRechargeRecord>(GiftCardRechargeRecord.class);
        util.exportExcel(response, list, "礼品卡充值记录数据");
    }

    @ApiOperation("获取礼品卡充值记录详细信息")
    @Log(title = "礼品卡充值记录-获取礼品卡充值记录详细信息", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(giftCardRechargeRecordService.selectGiftCardRechargeRecordById(id));
    }

    @ApiOperation("修改礼品卡充值记录")
    @Log(title = "礼品卡充值记录-修改礼品卡充值记录", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody GiftCardRechargeRecord giftCardRechargeRecord) {
        return toAjax(giftCardRechargeRecordService.updateGiftCardRechargeRecord(giftCardRechargeRecord));
    }

    @ApiOperation("删除礼品卡充值记录")
    @Log(title = "礼品卡充值记录-删除礼品卡充值记录", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(giftCardRechargeRecordService.deleteGiftCardRechargeRecordByIds(ids));
    }

    @ApiOperation("删除礼品卡充值记录")
    @Log(title = "礼品卡充值记录-删除礼品卡充值记录", businessType = BusinessType.DELETE)
//    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @DeleteMapping("/removeById/{id}")
    public AjaxResult removeById(@PathVariable("id") Long id) {
        return toAjax(giftCardRechargeRecordService.deleteGiftCardRechargeRecordById(id));
    }
}
