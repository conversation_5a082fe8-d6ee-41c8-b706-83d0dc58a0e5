package com.juyou.system.service.impl;

import com.juyou.common.annotation.DataSource;
import com.juyou.common.constant.Constants;
import com.juyou.common.constant.UserConstants;
import com.juyou.common.core.redis.RedisCache;
import com.juyou.common.core.text.Convert;
import com.juyou.common.enums.DataSourceType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.StringUtils;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.domain.SysConfig;
import com.juyou.system.mapper.SysConfigMapper;
import com.juyou.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl implements ISysConfigService {
    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        //将Redis缓存中的配置转换为字符串类型
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);

        if (StringUtils.isNotNull(retConfig)) {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    @Override
    public boolean selectCaptchaOnOff() {
        String captchaOnOff = selectConfigByKey("sys.account.captchaOnOff");
        if (StringUtils.isEmpty(captchaOnOff)) {
            return true;
        }
        return Convert.toBool(captchaOnOff);
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfig config) {
        int row = configMapper.insertConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfig config) {
        int row = configMapper.updateConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    @Override
    public int updateConfigMinutes(String one, String two) {
        int row = configMapper.updateConfigMinutes(one,ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES), one);
        }
        row = configMapper.updateConfigMinutes(two,ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES_TWO);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES_TWO), two);
        }
        return row;
    }

    @Override
    public int updateConfigDate(String date) {
        int row = configMapper.updateConfigMinutes(date,ConfigConstant.DIURNAL_TANGENCY_POINT);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.DIURNAL_TANGENCY_POINT), date);
        }
        return row;
    }

    @Override
    public int updateConfigNumbers(String one, String two, String sell) {
        int row = configMapper.updateConfigMinutes(one,ConfigConstant.ACCOUNT_ECHARGE_ONE);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.ACCOUNT_ECHARGE_ONE), one);
        }
        row = configMapper.updateConfigMinutes(two,ConfigConstant.ACCOUNT_ECHARGE_TWO);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.ACCOUNT_ECHARGE_TWO), two);
        }
        row = configMapper.updateConfigMinutes(two,ConfigConstant.ACCOUNT_SELL);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(ConfigConstant.ACCOUNT_SELL), sell);
        }
        return row;
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            configMapper.deleteConfigById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(Constants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public String checkConfigKeyUnique(SysConfig config) {
        Long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }
}
