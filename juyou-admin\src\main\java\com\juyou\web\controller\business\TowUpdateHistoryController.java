package com.juyou.web.controller.business;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.system.domain.vo.TowUpdateHistory;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.enums.BusinessType;

import com.juyou.system.service.ITowUpdateHistoryService;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/system/history")
public class TowUpdateHistoryController extends BaseController
{
    @Autowired
    private ITowUpdateHistoryService towUpdateHistoryService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:history:list')")
    @GetMapping("/list")
    public TableDataInfo list(TowUpdateHistory towUpdateHistory)
    {
        startPage();
        List<TowUpdateHistory> list = towUpdateHistoryService.selectTowUpdateHistoryList(towUpdateHistory);
        return getDataTable(list);
    }

    /**
     * 获取最新消息
     */
    @ApiModelProperty("获取最新消息")
    @GetMapping("/getTheLatestNews")
    public ResultData<TowUpdateHistory> getTheLatestNews()
    {
        return ResultUtil.success(towUpdateHistoryService.selectTowUpdateHistoryNew());
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:history:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TowUpdateHistory towUpdateHistory)
    {
        List<TowUpdateHistory> list = towUpdateHistoryService.selectTowUpdateHistoryList(towUpdateHistory);
        ExcelUtil<TowUpdateHistory> util = new ExcelUtil<TowUpdateHistory>(TowUpdateHistory.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:history:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(towUpdateHistoryService.selectTowUpdateHistoryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiModelProperty("更新最新消息")
    @Log(title = "更新最新消息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TowUpdateHistory towUpdateHistory)
    {
        towUpdateHistory.setUpdateTime(new Date());
        towUpdateHistory.setCreateBy(super.getUsername());
        return toAjax(towUpdateHistoryService.insertTowUpdateHistory(towUpdateHistory));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:history:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TowUpdateHistory towUpdateHistory)
    {
        return toAjax(towUpdateHistoryService.updateTowUpdateHistory(towUpdateHistory));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:history:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(towUpdateHistoryService.deleteTowUpdateHistoryByIds(ids));
    }
}
