<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.GiftCardRechargeRecordMapper">

    <resultMap type="GiftCardRechargeRecord" id="GiftCardRechargeRecordResult">
        <result property="id" column="id"/>
        <result property="account" column="account"/>
        <result property="accountPwd" column="account_pwd"/>
        <result property="giftCard" column="gift_card"/>
        <result property="cardStatus" column="card_status"/>
        <result property="executionTime" column="execution_time"/>
        <result property="faceValue" column="face_value"/>
        <result property="balance" column="balance"/>
        <result property="buyPrice" column="buy_price"/>
        <result property="buyAmt" column="buy_amt"/>
        <result property="executionInfo" column="execution_info"/>
        <result property="pledgeThirtyMinutes" column="pledge_thirty_minutes"/>
        <result property="pledgeStartDate" column="pledge_start_date"/>
        <result property="pledgeEndDate" column="pledge_end_date"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="sourceGroup" column="source_group"/>
        <result property="accountZone" column="account_zone"/>
    </resultMap>

    <sql id="selectGiftCardRechargeRecordVo">
        select id,
               account,
               account_pwd,
               gift_card,
               card_status,
               execution_time,
               face_value,
               balance,
               buy_price,
               buy_amt,
               execution_info,
               pledge_thirty_minutes,
               pledge_start_date,
               pledge_end_date,
               create_time,
               create_by,
               update_time,
               update_by,
               source_group
        from two_gift_card_recharge_record gcrr
    </sql>
    <sql id="selectOldGiftCardRechargeRecordVo">
        select id,
               account,
               account_pwd,
               gift_card,
               card_status,
               execution_time,
               face_value,
               balance,
               buy_price,
               buy_amt,
               execution_info,
               pledge_thirty_minutes,
               pledge_start_date,
               pledge_end_date,
               create_time,
               create_by,
               update_time,
               update_by,
               source_group
        from gift_card_recharge_record gcrr
    </sql>

    <sql id="selelctGiftCardRechargeRecord">
        SELECT g.*, a.account_zone
        FROM two_gift_card_recharge_record AS g
                 LEFT JOIN two_account_recharge AS a ON g.account = a.account_name
    </sql>

    <select id="selectGiftCardRechargeRecordList" parameterType="GiftCardRechargeRecord"
            resultMap="GiftCardRechargeRecordResult">
        <include refid="selelctGiftCardRechargeRecord"/>
        <where>
            <if test="account != null  and account != ''">and g.account = #{account}</if>
            <if test="accountPwd != null  and accountPwd != ''">and g.account_pwd = #{accountPwd}</if>
            <if test="accountZone != null and accountZone != ''">and a.account_zone = #{accountZone}</if>
            <if test="giftCard != null  and giftCard != ''">and g.gift_card = #{giftCard}</if>
            <if test="cardStatus != null  and cardStatus != ''">and g.card_status = #{cardStatus}</if>
            <if test="executionTime != null ">and g.execution_time = #{executionTime}</if>
            <if test="faceValue != null ">and g.face_value = #{faceValue}</if>
            <if test="balance != null ">and g.balance = #{balance}</if>
            <if test="executionInfo != null  and executionInfo != ''">and g.execution_info = #{executionInfo}</if>
            <if test="sourceGroup != null and sourceGroup != ''">and g.source_group = #{sourceGroup}</if>
        </where>
    </select>

    <select id="selectGiftCardRechargeRecordById" parameterType="Long" resultMap="GiftCardRechargeRecordResult">
        <include refid="selectGiftCardRechargeRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectGiftCardRechargeRecordPage"
            parameterType="com.juyou.system.params.GiftCardRechargeRecordPageParam"
            resultMap="GiftCardRechargeRecordResult">
        <include refid="selelctGiftCardRechargeRecord"/>
        <where>
            <if test="param.account != null and param.account != ''">
                and g.account LIKE CONCAT('%',#{param.account},'%')
            </if>
            <if test="param.giftCard != null and param.giftCard != ''">
                and g.gift_card LIKE CONCAT('%',#{param.giftCard},'%')
            </if>
            <if test="param.accountZone != null and param.accountZone != ''">
                and a.account_zone LIKE CONCAT('%',#{param.accountZone},'%')
            </if>
            <if test="param.sourceGroup != null and param.sourceGroup != ''">
                and g.source_group LIKE CONCAT('%',#{param.sourceGroup},'%')
            </if>
            <if test="param.startExecutionTime != null and param.endExecutionTime != null">
                and g.execution_time BETWEEN #{param.startExecutionTime} and #{param.endExecutionTime}
            </if>
            <if test="param.operator != null and param.operator != ''">
                and g.create_by LIKE CONCAT('%',#{param.operator},'%')
            </if>
            <if test="param.faceValue != null and param.faceValue != ''">
                and g.face_value LIKE CONCAT('%',#{param.faceValue},'%')
            </if>
        </where>
        ORDER BY g.create_time DESC
    </select>

    <select id="countPledgeThirtyMinutes" resultType="java.lang.Integer" parameterType="java.lang.String">
        select COUNT(*)
        from two_gift_card_recharge_record gcrr
        WHERE gcrr.account = #{account}
          and gcrr.pledge_thirty_minutes = 1
          and gcrr.pledge_end_date <![CDATA[ > ]]> NOW()
    </select>

    <select id="getGiftCardRechargeStatisticsDetailList" resultMap="GiftCardRechargeRecordResult"
            parameterType="com.juyou.system.params.GiftCardRechargeStatisticsDetailSearchParam">
        <include refid="selectGiftCardRechargeRecordVo"/>
        <where>
            gcrr.source_group = #{param.sourceGroup}
            <if test="param.startRechargeCompleteTime != null and param.endRechargeCompleteTime != null">
                and gcrr.create_time BETWEEN #{param.startRechargeCompleteTime} AND #{param.endRechargeCompleteTime}
            </if>
            <if test="param.account != null and param.account != ''">
                and gcrr.account LIKE CONCAT('%',#{param.account},'%')
            </if>
            <if test="param.giftCard != null and param.giftCard != ''">
                and gcrr.gift_card LIKE CONCAT('%',#{param.giftCard},'%')
            </if>
            ORDER BY gcrr.source_group ASC
        </where>
    </select>

    <select id="getFaceValue" resultType="java.math.BigDecimal">
        SELECT SUM(rr.face_value)
        FROM two_gift_card_recharge_record rr
        WHERE rr.account = #{account}
    </select>

    <select id="getBuyAmt" resultType="java.math.BigDecimal">
        SELECT SUM(rr.buy_amt)
        FROM two_gift_card_recharge_record rr
        WHERE rr.account = #{account}
    </select>


    <insert id="insertGiftCardRechargeRecord" parameterType="GiftCardRechargeRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into two_gift_card_recharge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="account != null">account,</if>
            <if test="accountPwd != null">account_pwd,</if>
            <if test="giftCard != null">gift_card,</if>
            <if test="cardStatus != null">card_status,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="faceValue != null">face_value,</if>
            <if test="balance != null">balance,</if>
            <if test="buyPrice != null">buy_price,</if>
            <if test="buyAmt != null">buy_amt,</if>
            <if test="executionInfo != null">execution_info,</if>
            <if test="pledgeThirtyMinutes != null">pledge_thirty_minutes,</if>
            <if test="pledgeStartDate != null">pledge_start_date,</if>
            <if test="pledgeEndDate != null">pledge_end_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="sourceGroup != null">source_group,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="account != null">#{account},</if>
            <if test="accountPwd != null">#{accountPwd},</if>
            <if test="giftCard != null">#{giftCard},</if>
            <if test="cardStatus != null">#{cardStatus},</if>
            <if test="executionTime != null">#{executionTime},</if>
            <if test="faceValue != null">#{faceValue},</if>
            <if test="balance != null">#{balance},</if>
            <if test="buyPrice != null">#{buyPrice},</if>
            <if test="buyAmt != null">#{buyAmt},</if>
            <if test="executionInfo != null">#{executionInfo},</if>
            <if test="pledgeThirtyMinutes != null">#{pledgeThirtyMinutes},</if>
            <if test="pledgeStartDate != null">#{pledgeStartDate},</if>
            <if test="pledgeEndDate != null">#{pledgeEndDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="sourceGroup != null">#{sourceGroup},</if>
        </trim>
    </insert>

    <update id="updateGiftCardRechargeRecord" parameterType="GiftCardRechargeRecord">
        update two_gift_card_recharge_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="account != null">account = #{account},</if>
            <if test="accountPwd != null">account_pwd = #{accountPwd},</if>
            <if test="giftCard != null">gift_card = #{giftCard},</if>
            <if test="cardStatus != null">card_status = #{cardStatus},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
            <if test="faceValue != null">face_value = #{faceValue},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="buyPrice != null">buy_price = #{buyPrice},</if>
            <if test="buyAmt != null">buy_amt = #{buyAmt},</if>
            <if test="executionInfo != null">execution_info = #{executionInfo},</if>
            <if test="pledgeThirtyMinutes != null">pledge_thirty_minutes = #{pledgeThirtyMinutes},</if>
            <if test="pledgeStartDate != null">pledge_start_date = #{pledgeStartDate},</if>
            <if test="pledgeEndDate != null">pledge_end_date = #{pledgeEndDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="sourceGroup != null">source_group = #{sourceGroup},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGiftCardRechargeRecordById" parameterType="Long">
        delete
        from two_gift_card_recharge_record
        where id = #{id}
    </delete>

    <delete id="deleteGiftCardRechargeRecordByIds" parameterType="String">
        delete from two_gift_card_recharge_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByAccountName">
        delete from two_gift_card_recharge_record r
        WHERE r.account = #{accountName}
    </delete>
    <select id="getRecordOfChargingCards" parameterType="com.juyou.system.params.RecordOfChargingCardsParam"
            resultType="com.juyou.system.domain.vo.RecordOfChargingCardsVo">
        SELECT
        a.gift_card AS giftCard,
        ar.account_zone AS accountZone,
        a.source_group AS sourceGroup,
        a.buy_price AS exchangeRate,
        a.face_value AS faceValue,
        a.buy_amt AS buyAmt,
        a.pledge_thirty_minutes AS pledgeThirtyMinutes,
        a.account AS account,
        a.account_pwd AS accountPwd,
        a.execution_time AS executionTime,
        a.create_by AS receiveUser
        FROM
        two_gift_card_recharge_record a
        LEFT JOIN
        two_account_recharge ar ON ar.account_name = a.account
        left join sys_user su on su.user_name = a.create_by
        <where>
            (
            r.`status` in (1, 2)

            and r.receive_user_id =#{}
            )
            OR
            (
            r.`status` in (3,4,0)
            )
            <if test="accountName != null and accountName != '' ">
                and ar.account_name LIKE CONCAT('%',#{accountName},'%')
                </if>
            <if test="cardNo != null and cardNo != '' ">
                AND a.card_no = #{cardNo}
            </if>
            <if test="startTime != null and endTime != null">
                AND a.execution_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="receiveUser != null and receiveUser != ''">
                AND ar.receive_user = #{receiveUser}
            </if>
            <if test="sourceGroup != null and sourceGroup != '' ">
                AND a.source_group = #{sourceGroup}
            </if>
            <if test="userID != null and userID !=''">
                AND a.create_by = #{userID}
            </if>
        </where>
    </select>
    <select id="getRecordOfChargingCardsAll" parameterType="com.juyou.system.params.RecordOfChargingCardsParam"
            resultType="com.juyou.system.domain.vo.RecordOfChargingCardsVo">
        SELECT
        a.gift_card AS giftCard,
        ar.account_zone AS accountZone,
        a.source_group AS sourceGroup,
        a.buy_price AS exchangeRate,
        a.face_value AS faceValue,
        a.buy_amt AS buyAmt,
        a.pledge_thirty_minutes AS pledgeThirtyMinutes,
        a.account AS account,
        a.account_pwd AS accountPwd,
        a.execution_time AS executionTime,
        ar.receive_user AS receiveUser
        FROM
        two_gift_card_recharge_record a
        LEFT JOIN
        two_account_recharge ar ON ar.account_name = a.account
        left join sys_user su on su.user_name = a.create_by
        <where>
        1=1
            <if test="accountName != null and accountName!= ''">
                and ar.account_name LIKE CONCAT('%',#{accountName},'%')
            </if>
            <if test="accountZone != null and accountZone!= ''">
                AND ar.account_zone = #{accountZone}
            </if>
            <if test="startTime != null and endTime != null">
                AND a.execution_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="cardNo != null and cardNo!= ''">
                AND a.gift_card = #{cardNo}
            </if>
            <if test="sourceGroup != null and sourceGroup!= ''">
                AND a.source_group = #{sourceGroup}
            </if>
            <if test="receiveUser != null and receiveUser != ''">
                AND ar.receive_user = #{receiveUser}
            </if>

          and  ((
            ar.`status` in (1, 2)
            <if test="receiveUser != null and receiveUser != ''">
                AND ar.receive_user = #{receiveUser}
            </if>
            )
            OR
            (
            ar.`status` in (3,4,0)
            ))
        </where>
    </select>
    <select id="getOldAccountCaedRechargeInfo" resultType="com.juyou.system.domain.GiftCardRechargeRecord">

        <include refid="selectOldGiftCardRechargeRecordVo"/>
        where account = #{account}


    </select>
</mapper>
