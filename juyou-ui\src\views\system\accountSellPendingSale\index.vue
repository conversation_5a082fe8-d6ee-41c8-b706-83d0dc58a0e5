<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
      <el-form-item label="账号" prop="accountName">
        <el-input v-model="queryParams.accountName" placeholder="请输入账号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="ID类型" prop="idType">
        <el-select v-model="queryParams.idType" placeholder="请选择ID类型" :style="{ width: '80%' }" clearable>
          <el-option
            v-for="dict in [{ label: '全部', value: '' }, { label: '苹果', value: '0' }, { label: '雷蛇', value: '1' }]"
            :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="出售群" prop="sellChatgroupName">
        <el-input v-model="queryParams.sellChatgroupName" placeholder="请输入出售群"/>
      </el-form-item>

      <el-form-item label="状态：" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择出售状态" :style="{ width: '80%' }" clearable>
          <el-option
            v-for="dict in [{ label: '全部', value: '' }, { label: '已出售', value: '3' }, { label: '作废', value: '4' }]"
            :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="核对状态：" prop="writeOffStatus">
        <el-select v-model="queryParams.writeOffStatus" placeholder="请选择核对状态" :style="{ width: '80%' }" clearable>
          <el-option
            v-for="dict in [{ label: '全部', value: '' }, { label: '未核对', value: '1' }, { label: '已核对', value: '2' }]"
            :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="区域" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option value="" label="全部"></el-option>
          <el-option
            v-for="item in zonrList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="出售日期" prop="sellTime">
        <el-date-picker v-model="queryParams.sellTime" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>


      <!--      <el-form-item label="区域" prop="custName">-->
      <!--        <el-input v-model="queryParams.accountZone" placeholder="请输入区域" clearable @keyup.enter.native="handleQuery" />-->
      <!--      </el-form-item>-->

      <!--      <el-form-item label="礼品卡代码：" prop="giftCard">-->
      <!--        <el-input v-model="queryParams.giftCard" placeholder="请输入礼品卡代码" />-->
      <!--      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button type="primary" @click="handleWriteOff">批量核对</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="notEffectiveList"
              @select="selectFn" @select-all="selectAllFn"
              :summary-method="getSummaries" show-summary>
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" type="index" width="55"/>
      <el-table-column label="ID类型" align="center" prop="idType">
        <template slot-scope="scope">
          <span>{{ scope.row.idType==='0' ? '苹果' : '雷蛇' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">{{ scope.row.accountName
            }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <!-- <el-table-column label="密码" align="center" prop="accountPwd" />  -->
      <!-- <el-table-column label="来源群" align="center" prop="sourceGroup" /> -->
      <el-table-column label="核对状态" align="center" prop="writeOffStatus">
        <template slot-scope="scope">
          <span>{{ formatWriteOffStatus(scope.row.writeOffStatus) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="出售群" align="center" prop="sellChatgroupName"></el-table-column>
      <el-table-column label="出售状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ formatStatus(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="充值金额" align="center" prop="rechargeAmt"/>
      <el-table-column label="出售单价" align="center" prop="sellPrice"/>
      <el-table-column label="出售金额" align="center" prop="sellAmt"/>
      <el-table-column label="出售时间" align="center" prop="sellTime"/>


      <!-- <el-table-column label="应充值金额" align="center" prop="shouldAmt" ></el-table-column> -->
      <!-- <el-table-column label="实际充值金额" align="center" prop="cardBalance" /> -->
      <!-- <el-table-column label="完成充值时间" align="center" prop="createTime" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button icon="el-icon-edit" @click="handleUpdateGroup(scope.row)">
            修改出售群
          </el-button>
          <!--          <el-button v-if="scope.row.status === '3'" size="mini" type="text" icon="el-icon-edit"
                      @click="handleUpdate(scope.row)" v-hasPermi="['system:sellcard:edit']">修改</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />


    <!--  修改出售群  -->
    <el-dialog title="修改出售群" :visible.sync="updateSellGroupVisible" width="750px" append-to-body>
      <el-form ref="updateSellGroupForm" :model="updateSellGroupForm" :rules="updateSellGroupRules" label-width="100px"
               label-position="right" :inline="true">
        <el-form-item label="账号">
          <el-input v-model="updateSellGroupForm.accountName" placeholder="请输入账号" :disabled="true"/>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="updateSellGroupForm.accountPwd" placeholder="请输入密码" :disabled="true"/>
        </el-form-item>
        <el-form-item label="出售群" prop="sellChatgroupName">
          <el-input v-model="updateSellGroupForm.sellChatgroupName" placeholder="请输入出售群"/>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="updateSellGroupForm.updateBy" placeholder="请输入操作人" :disabled="true"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormSellGroup">提交</el-button>
      </div>
    </el-dialog>

    <!-- 修改sellcard对话框 -->
    <el-dialog title="修改账号充值信息" :visible.sync="notEffectiveVisible" width="750px" append-to-body>
      <el-form ref="notEffectiveForm" :model="notEffectiveForm" :rules="notEffectiveRules" label-width="100px"
               label-position="right" :inline="true">
        <el-form-item label="账号" prop="acid">
          <el-input v-model="notEffectiveForm.accountName" placeholder="请输入账号" :disabled="true"/>
        </el-form-item>
        <el-form-item label="密码" prop="accountPwd">
          <el-input v-model="notEffectiveForm.accountPwd" placeholder="请输入密码" :disabled="true"/>
        </el-form-item>
        <el-form-item label="区域" prop="accountZone">
          <el-input v-model="notEffectiveForm.accountZone" placeholder="请输入账号区域" :disabled="true"/>
        </el-form-item>

        <el-form-item label="应充值金额" prop="shouldAmt">
          <el-input type="number" v-model="notEffectiveForm.shouldAmt" placeholder="请输入应充值金额" :disabled="true"/>
        </el-form-item>

        <el-form-item label="实际充值金额" prop="cardBalance">
          <el-input type="number" v-model="notEffectiveForm.cardBalance" placeholder="请输入实际充值金额" @input="checkCartAmt"/>
        </el-form-item>

        <el-form-item label="进价" prop="buyPrice">
          <el-input type="number" v-model="notEffectiveForm.buyPrice" placeholder="请输入进价" @input="checkCartAmt"/>
        </el-form-item>

        <el-form-item label="成本" prop="buyAmt">
          <el-input type="number" v-model="notEffectiveForm.buyAmt" :disabled="true"/>
        </el-form-item>

        <el-form-item label="操作人" prop="updateBy">
          <el-input v-model="notEffectiveForm.updateBy" :disabled="true"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button type="primary"
                   @click="copyToClip(`${notEffectiveForm.accountName}----${notEffectiveForm.accountPwd}`)">一键复制账号
        </el-button>
        <el-button @click="cancel">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {batchWriteOff, getAccountSellPendingSaleList} from '@/api/system/notEffective'
  import {updateSellcard, updateSellChatgroupName} from '@/api/system/sellcard'
  import {getDicts} from "@/api/system/dict/data";
  import Cookies from "js-cookie";

  export default {
    name: "accountSellPendingSale",
    data() {
      return {
        //操作人列表
        operatoList: [],
        //未生效账号列表
        notEffectiveList: [],
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 是否显示弹出层
        notEffectiveVisible: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
        },
        //未生效表单参数
        notEffectiveForm: {},
        // 表单校验
        notEffectiveRules: {
          cardBalance: [{required: true, message: '请输入实际充值金额', trigger: 'blur'}],
          buyPrice: [{required: true, message: '请输入进价', trigger: 'blur'}]
        },
        bulkSalesSelects: [],//批量出售选中数组
        copySelects: [],//批量复制选中的数组
        bulkSalesSelectsMap: [],
        // 修改出售群表单
        updateSellGroupForm: {},
        updateSellGroupRules: {
          sellChatgroupName: [{required: true, message: '请输入出售群', trigger: 'blur'}]
        },
        // 修改出售群弹窗
        updateSellGroupVisible: false,
        zonrList: [],
      };
    },
    created() {
      this.into();
    },
    methods: {
      into() {
        let cook = Cookies.get("accountSellPendingSale");
        if (cook != null && cook != undefined) {
          this.queryParams = JSON.parse(cook);
        }
        getDicts("account_zone").then(response => {
            this.zonrList = response.data;
          }
        );
        this.getList();//获取未生效状态的账号
      },
      // 批量核对
      async handleWriteOff() {
        if (this.copySelects && this.copySelects.length != 0) {
          let ids = this.copySelects.map((item) => {
            return item.rechargeId
          });
          //console.log("ids:", ids)
          // let ids = '1,2'
          const request = {
            rechargeIdList: ids
          };
          const res = await batchWriteOff(request)
          if (res.code === 200) {
            this.getList()
            this.$modal.msgSuccess("操作成功");
          }
        }
      },
      /**一键复制账号和密码*/
      copyToClip(content) {
        const input = document.createElement('input');
        input.setAttribute('value', content);
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$modal.msgSuccess("已复制到黏贴版");
      },
      // 转译核销状态
      formatWriteOffStatus(writeOffStatus) {
        let data = ''
        switch (writeOffStatus) {
          case '1':
            data = '未核对';
            break;
          case '2':
            data = '已核对';
            break;
        }
        return data;

      },
      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          const request = {
            ...this.queryParams,
            startSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[0] : null,//开始出售时间
            endSellTime: this.queryParams.sellTime ? this.queryParams.sellTime[1] : null,//结束出售时间
          };
          Cookies.set('accountSellPendingSale',JSON.stringify(this.queryParams));
          const notEffectiveList_res = await getAccountSellPendingSaleList(request);
          if (notEffectiveList_res.code === 200) {
            this.notEffectiveList = notEffectiveList_res.rows;
            this.total = notEffectiveList_res.total;
            this.loading = false;
            this.copySelects = [];
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      selectFn(selection, row) {
        let flag = selection.some((itme) => {
          return itme.rechargeId === row.rechargeId;
        })
        const index = this.arrFindObjIndex(this.copySelects, row, "rechargeId");
        if (!flag) {
          if (index !== -1) {
            this.copySelects.splice(index, 1);
            this.bulkSalesSelects.splice(index, 1);
          }
        } else {
          if (index === -1) {
            this.copySelects.push(row);
          }
          if (index === -1 && row.status === '2') {
            this.bulkSalesSelects.push(row);
          }
        }
        this.bulkSalesSelectsMap = JSON.parse(JSON.stringify(this.bulkSalesSelects));
      },
      selectAllFn(selection) {
        if (!selection.length) {
          this.notEffectiveList.forEach(itme => {
            const index = this.arrFindObjIndex(this.copySelects, itme, "rechargeId");
            if (index !== -1) {
              this.copySelects.splice(index, 1);
              this.bulkSalesSelects.splice(index, 1);
            }
          })
        } else {
          selection.forEach(itme => {
            const index = this.arrFindObjIndex(this.copySelects, itme, "rechargeId");
            if (index === -1) {
              this.copySelects.push(itme);
            }
            if (index === -1 && itme.status === '2') {
              this.bulkSalesSelects.push(itme);
            }
          })

        }
        this.bulkSalesSelectsMap = JSON.parse(JSON.stringify(this.bulkSalesSelects));

      },
      arrFindObjIndex(list, obj, key) {
        let index = -1;
        list.forEach((itme, idx) => {
          if (itme[key] === obj[key]) {
            index = idx;
          }
        })
        return index;
      },
      // 取消按钮
      cancel() {
        this.notEffectiveVisible = false;
        this.reset();
      },
      // 重置出售群修改表单
      resetUpdateSellGroup() {
        this.updateSellGroupForm = {
          rechargeId: null, // 账号出售唯一标识
          sellChatgroupName: null // 出售群
        }

      },
      // 表单重置
      reset() {
        this.notEffectiveForm = {
          accountName: null,//账号
          accountPwd: null,//密码
          accountZone: null,//区域
          shouldAmt: null,//应充值金额
          cardBalance: null,//实际充值金额
          buyPrice: "0",//进价
          buyAmt: null,//成本
          createUser: null,//操作人
        };
        this.resetForm("notEffectiveForm");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
        }
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.rechargeId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      // 修改出售群
      handleUpdateGroup(row) {
        this.resetUpdateSellGroup()
        this.updateSellGroupVisible = true;
        this.updateSellGroupForm = {...row};
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.notEffectiveVisible = true;
        this.notEffectiveForm = row;
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["notEffectiveForm"].validate(valid => {
          if (valid) {
            if (this.notEffectiveForm.cardBalance <= 0 || this.notEffectiveForm.buyPrice <= 0) {
              this.$modal.msgError(`充值金额和进价格必须大于零`);
              return
            }
            if (this.notEffectiveForm.rechargeId != null) {
              updateSellcard(this.notEffectiveForm).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.notEffectiveVisible = false;
                this.getList();
              });
            }
          }
        });
      },
      // 修改出售群提交
      submitFormSellGroup() {
        this.$refs["updateSellGroupForm"].validate(valid => {
          if (valid) {
            let data = {
              rechargeId: this.updateSellGroupForm.rechargeId,
              sellChatgroupName: this.updateSellGroupForm.sellChatgroupName
            }
            updateSellChatgroupName(data).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.updateSellGroupVisible = false;
              this.getList();
            })
          }
        })
      },
      /**
       * 计算成本金额
       */
      checkCartAmt() {
        console.log(this.notEffectiveForm);
        if (this.checkIsNumber(this.notEffectiveForm.buyPrice) && this.checkIsNumber(this.notEffectiveForm.cardBalance)) {
          if (this.notEffectiveForm.buyPrice > 0 && this.notEffectiveForm.cardBalance > 0) {
            //保留两位小数，四舍五入
            this.notEffectiveForm.buyAmt = (this.notEffectiveForm.buyPrice * this.notEffectiveForm.cardBalance).toFixed(2);
          }
        }
      },
      /**
       * 判断是否为数字
       */
      checkIsNumber(number) {
        var numReg = /^\d+(\.\d+)?$/g;
        var numRe = new RegExp(numReg)
        if (numRe.test(number)) {
          return true;
        } else {
          return false;
        }
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3, 4, 5, 6 , 7, 9] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' ';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
