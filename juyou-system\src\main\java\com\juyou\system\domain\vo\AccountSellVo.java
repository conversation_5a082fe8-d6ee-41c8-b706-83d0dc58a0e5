package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountSell;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账号出售-Vo
 */
@Data
@ApiModel("AccountSellVo")
public class AccountSellVo extends AccountSell {

    private static final long serialVersionUID = 1384377347184367630L;

    @ApiModelProperty("已完成时间(小时)")
    private BigDecimal completedTimeHourNum;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmt;

    @ApiModelProperty("充值阶段")
    private String chargeStage;

    @ApiModelProperty("剩余分钟")
    private BigDecimal surplusMinutesNum;

    @ApiModelProperty("一级充值人")
    private String primaryCharger;

    @ApiModelProperty("二级充值人")
    private String secondaryCharger;


}
