package com.juyou.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.system.domain.vo.*;
import com.juyou.system.mapper.AccountRechargeMapper;
import com.juyou.system.mapper.AccountSellMapper;
import com.juyou.system.mapper.PerformanceMapper;
import com.juyou.system.params.CompanyPerformanceQueryParam;
import com.juyou.system.params.PerformanceSearchParam;
import com.juyou.system.service.IPerformanceService;
import com.juyou.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业绩ServiceImpl
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class PerformanceServiceImpl implements IPerformanceService {

    @Autowired
    private AccountRechargeMapper accountRechargeMapper;

    @Autowired
    private AccountSellMapper accountSellMapper;
    @Autowired
    private PerformanceMapper performanceMapper;
    @Autowired
    ISysUserService sysUserService;
    @Override
    public List<PerformanceVoList> list(PerformanceSearchParam param) {

        List<PerformanceVoList> voList = new ArrayList<>();
        List<String> mergeList = new ArrayList<>();

        // 充值账号列表
        List<PerformanceVoList> rechargeList = this.accountRechargeMapper.findPerformanceList(param);

        // 销售账号列表
        List<PerformanceVoList> sellList = this.accountSellMapper.findPerformanceList(param);

        // 统计合并逻辑如下
        // 合并: 工作日期和账号一致的合并成一条数据
        // 不合并: 工作日期和账号没有找到一致的数据

        if (CollUtil.isNotEmpty(rechargeList) && CollUtil.isNotEmpty(sellList)) {
            for (PerformanceVoList recharge : rechargeList) {
                for (PerformanceVoList sell : sellList) {
                    if (recharge.getWorkDate().compareTo(sell.getWorkDate()) == 0
                            && recharge.getAccount().equals(sell.getAccount())) {
                        recharge.setSellCount(sell.getSellCount());
                        recharge.setSellAmt(sell.getSellAmt());
                        voList.add(recharge);
                        mergeList.add(DateUtil.formatDate(recharge.getWorkDate()) + "-" + recharge.getAccount());
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(voList)) {
            // 把不合并的数据加到voList中
            if (CollUtil.isNotEmpty(rechargeList)) {
                for (PerformanceVoList recharge : rechargeList) {
                    String merge = DateUtil.formatDate(recharge.getWorkDate()) + "-" + recharge.getAccount();
                    if (!mergeList.contains(merge)) {
                        voList.add(recharge);
                    }
                }
            }
            if (CollUtil.isNotEmpty(sellList)) {
                for (PerformanceVoList sell : sellList) {
                    String merge = DateUtil.formatDate(sell.getWorkDate()) + "-" + sell.getAccount();
                    if (!mergeList.contains(merge)) {
                        voList.add(sell);
                    }
                }
            }
        } else {
            voList.addAll(rechargeList);
            voList.addAll(sellList);
        }
        // 排序
        if (CollUtil.isNotEmpty(voList)) {
            voList.sort(Comparator.comparing(PerformanceVoList::getWorkDate, Comparator.reverseOrder()));
        }
        return voList;
    }

    @Override
    public CompanyPerformanceVo getPerformanceData() {
        CompanyPerformanceVo companyPerformanceVo = new CompanyPerformanceVo();
        companyPerformanceVo.setCardMoney(performanceMapper.getRemainingActualRechargeTotal());
        // 充值作废率
        BigDecimal rechargeCancelRate = performanceMapper.getRechargeCancelRate();
        if (rechargeCancelRate == null) {
            rechargeCancelRate = BigDecimal.ZERO;
        }
        companyPerformanceVo.setRechargeCancelRate(rechargeCancelRate);
        // 出售作废率
        BigDecimal sellCancelRate = performanceMapper.getSellCancelRate();
        if (sellCancelRate == null) {
            sellCancelRate = BigDecimal.ZERO;
        }
        companyPerformanceVo.setBounceRate(performanceMapper.getBounceRates());

        companyPerformanceVo.setSellCancelRate(sellCancelRate);
        BigDecimal surplusMoney = performanceMapper.getRemainingCostTotal();
        if (surplusMoney == null) {
            surplusMoney = BigDecimal.ZERO;
        }
        // 剩余成本总额
        companyPerformanceVo.setSurplusMoney(surplusMoney);

        return companyPerformanceVo;
    }

    @Override
    public List<DepartmentTreeVo> getDepartmentList() {
        Map<String, List<DepartmentVo>> map = new HashMap<>();
        List<DepartmentTreeVo> departmentTreeVos = new ArrayList<>();
        List<DepartmentVo> departmentVoList = performanceMapper.getDepartmentList();
        List<SysUser> users=  sysUserService.selectUserListAll(new SysUser());

        for (DepartmentVo departmentVo : departmentVoList){
            if ( departmentVo==null || departmentVo.getDeptId()==100){
                continue;
            }
            String userIdString = departmentVo.getUserId();
            List<Long> numbersList = Arrays.stream(userIdString.split(","))
                    .map(String::trim) // 先去除每个元素的前导和尾随空格
                    .map(Long::parseLong) // 然后将每个无空格的字符串转换为Long
                    .collect(Collectors.toList());// 最后收集到List中转换.collect(Collectors.toList());

            DepartmentTreeVo departmentTreeVo = new DepartmentTreeVo();
            departmentTreeVo.setDeptId(departmentVo.getDeptId());

            for (Long number : numbersList)
                for (SysUser user : users) {
                    if (Objects.equals(number, user.getUserId())) {
                        departmentTreeVo.setLabel(user.getDept().getDeptName());
                        DepartmentVo userVo=  new DepartmentVo();
                        userVo.setUserId(user.getUserId().toString());
                        userVo.setUserName(user.getUserName());
                        userVo.setDeptName(user.getDept().getDeptName());
                        userVo.setDeptId(user.getDept().getDeptId());
                        departmentTreeVo.getChildren().add(userVo);
                    }
                }
            departmentTreeVos.add(departmentTreeVo);

        }

        return departmentTreeVos;
    }

    @Override
    public PerformanceReportIsDetailedVo getPerformanceReportIsDetailed(CompanyPerformanceQueryParam param) {

        PerformanceReportIsDetailedVo performanceReportIsDetailedVo = new PerformanceReportIsDetailedVo();
       //充值作废率
       performanceReportIsDetailedVo.setRechargeCancelRate(performanceMapper.getRechargeCancelRateDetail(param));
       //出售作废率
       performanceReportIsDetailedVo.setSellCancelRate(performanceMapper.getSellCancelRateDetail(param));
       //退回率
       performanceReportIsDetailedVo.setBounceRate(performanceMapper.getBounceRatesDetail(param));
       //充值数量
       performanceReportIsDetailedVo.setRechargeCount(performanceMapper.getRechargeNum(param));
       //充值成本总额
       performanceReportIsDetailedVo.setRechargeCostTotal(performanceMapper.getRechargeCostTotal(param));
       //充值作废数量
       performanceReportIsDetailedVo.setRechargeCancelCount(performanceMapper.getRechargeCancelNum(param));
       //充值作废成本总额
       performanceReportIsDetailedVo.setRechargeCancelCostTotal(performanceMapper.getRechargeCancelCostTotal(param));
       //出售数量
       performanceReportIsDetailedVo.setSellCount(performanceMapper.getSellNum(param));
       //出售成本总额
       performanceReportIsDetailedVo.setSellCostTotal(performanceMapper.getSellTotalAmount(param));
       //出售作废数量
       performanceReportIsDetailedVo.setSellCancelCount(performanceMapper.getSellCancelNum(param));
       //出售作废金额总额
       performanceReportIsDetailedVo.setSellCancelCostTotal(performanceMapper.getSellCancelTotalAmount(param));
      //PerformanceReportIsDetailedVo performanceReportIsDetailedVo = performanceMapper.getPerformanceReportIsDetailed(param);

        performanceReportIsDetailedVo.setPerformanceDetailedVoList( performanceMapper.getPerformanceDetailedVo(param));

        return performanceReportIsDetailedVo;
    }


}
