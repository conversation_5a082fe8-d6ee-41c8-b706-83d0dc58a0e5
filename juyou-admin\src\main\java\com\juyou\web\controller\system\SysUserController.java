package com.juyou.web.controller.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.constant.UserConstants;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.domain.TreeSelect;
import com.juyou.common.core.domain.entity.SysDept;
import com.juyou.common.core.domain.entity.SysRole;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.common.utils.StringUtils;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.domain.UserGoogleAuthenticator;
import com.juyou.system.service.*;
import com.juyou.system.utils.GoogleAuthenticator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
@Api("用户管理")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IUserGoogleAuthenticatorService iUserGoogleAuthenticatorService;

    @ApiOperation("获取所有用户列表")
    @PreAuthorize("@ss.hasPermi('system:user:getListAll')")
    @PostMapping("/getListAll")
    public TableDataInfo<SysUser> getListAll(){
        SysUser sysUser = new SysUser();
        sysUser.setDeptId(super.getDeptId());
        List<SysUser> list = userService.selectUserListAll(sysUser);
        return getDataTable(list);
    }

    @ApiOperation("获取用户列表排除自己")
    @PreAuthorize("@ss.hasPermi('system:user:getListExcludeMe')")
    @PostMapping("/getListExcludeMe")
    public TableDataInfo<SysUser> getListExcludeMe() {
        SysUser sysUser = new SysUser();
        sysUser.setDeptId(super.getDeptId());
        List<SysUser> list = userService.selectUserListAll(sysUser);
        List<SysUser> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            voList = list.stream().filter(item -> {
                if (item.getUserId().equals(super.getUserId())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        return getDataTable(voList);
    }

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 获取部门用户下拉树列表
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(SysUser user) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        List<TreeSelect> deptTree = deptService.buildDeptTreeSelect(depts);

        List<SysUser> userList = userService.selectUserList(user);
        return AjaxResult.success(userService.buildDeptUserTreeSelect(deptTree, userList));
    }

    /**
     * 加载对应角色部门用户列表树
     */
    @GetMapping(value = "/roleUserTreeselect/{roleId}")
    public AjaxResult roleUserTreeselect(@PathVariable("roleId") Long roleId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        List<TreeSelect> deptTree = deptService.buildDeptTreeSelect(depts);

        List<SysUser> userList = userService.selectUserList(new SysUser());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", userService.selectUserListByRoleId(roleId));
        ajax.put("users", userService.buildDeptUserTreeSelect(deptTree, userList));
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        if (user.getIsLock().equals("true"))
            user.setIsLock("Y");
        if (user.getIsLock().equals("false"))
            user.setIsLock("N");
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(getUsername());
        if (user.getIsLock().equals("true"))
            user.setIsLock("Y");
        if (user.getIsLock().equals("false"))
            user.setIsLock("N");
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }
    @ApiModelProperty(value = "修改用户锁定状态")
    @PutMapping("/updateLockStatus")
    public AjaxResult updateLockStatus(@RequestBody SysUser sysUser) {
        return AjaxResult.success(userService.updateLockStatus(sysUser));

    }
    @ApiOperation("验证谷歌码")
    @Log(title = "验证谷歌码", businessType = BusinessType.OTHER)
    @PostMapping("/verifyTheGoogleCode")
    public ResultData<Boolean> verifyTheGoogleCode(@RequestBody String code) {
        SysUser sysUser = this.userService.selectUserByUserName(super.getUsername());
        if(!sysUser.isAdmin()) {
            UserGoogleAuthenticator userGoogleAuthenticator = this.iUserGoogleAuthenticatorService.getByUsername(super.getUsername());
            if (ObjUtil.isNull(userGoogleAuthenticator)) {
                throw new ServiceException("该账号没有绑定谷歌验证器,请去绑定!");
            }
            boolean isTrue = GoogleAuthenticator.check_code(userGoogleAuthenticator.getGoogleSecret(), Long.parseLong(code), System.currentTimeMillis());
            if (!isTrue) {
                throw new ServiceException("验证码不正确!");
            }
        }
        return ResultUtil.success(true);
    }
}


