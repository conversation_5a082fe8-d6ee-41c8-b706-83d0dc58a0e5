package com.juyou.web.controller.support;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.domain.vo.GoogleAuthenticatorSecretVo;
import com.juyou.system.params.GenerateGoogleSecretParam;
import com.juyou.system.service.IUserGoogleAuthenticatorService;
import com.juyou.system.utils.QRCodeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户谷歌验证器Controller
 */
@Api(value = "用户谷歌验证器", tags = "用户谷歌验证器")
@RestController
@RequestMapping("/userGoogleAuthenticator")
public class UserGoogleAuthenticatorController extends BaseController {

    @Autowired
    private IUserGoogleAuthenticatorService iUserGoogleAuthenticatorService;

    @ApiOperation("生成谷歌验证器二维码")
    @Log(title = "用户谷歌验证器-生成谷歌验证器二维码", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('support:googleAuthenticator:generateGoogleSecret')")
    @GetMapping("/generateGoogleSecret")
    public ResultData<String> generateGoogleSecret(GenerateGoogleSecretParam param) throws Exception {
        // check
        if (StrUtil.isBlank(param.getUsername())) {
            throw new ServiceException("账号不能为空!");
        }
        GoogleAuthenticatorSecretVo vo = this.iUserGoogleAuthenticatorService.generateGoogleSecret(param.getUsername());

        FastByteArrayOutputStream stream = new FastByteArrayOutputStream();
        QRCodeUtil.encode(vo.getSecretQrCode(), stream);

        String encode = Base64.encode(stream.toByteArray());

        return ResultUtil.success("ok", encode);
    }

    /*@ApiOperation("用户谷歌验证器-生成二维码，这个去地址栏请求，不要用Swagger-ui请求")
    @Log(title = "用户谷歌验证器-生成二维码，这个去地址栏请求，不要用Swagger-ui请求", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('support:googleAuthenticator:genQrCode')")
    @GetMapping("/genQrCode")
    public void genQrCode(String secretQrCode, HttpServletResponse response) throws Exception{
        response.setContentType("image/png");
        OutputStream stream = response.getOutputStream();
        QRCodeUtil.encode(secretQrCode,stream);
    }*/


    /*@ApiOperation("绑定谷歌验证")
    @Log(title = "用户谷歌验证器-绑定谷歌验证", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('support:googleAuthenticator:bindGoogle')")
    @PostMapping("/bindGoogle")
    public ResultData<Integer> bindGoogle(@RequestBody BindGoogleAuthenticatorParam param) {
        // check
        if (ObjUtil.isNull(param.getUserId())) {
            throw new ServiceException("用户id不能为空!");
        }
        if (StrUtil.isBlank(param.getUsername())) {
            throw new ServiceException("用户账号不能为空!");
        }
        if (ObjUtil.isNull(param.getCode())) {
            throw new ServiceException("谷歌动态验证码不能为空!");
        }
        if (StrUtil.isBlank(param.getGoogleSecret())) {
            throw new ServiceException("谷歌验证秘钥不能为空!");
        }
        if (StrUtil.isBlank(param.getSecretQrCode())) {
            throw new ServiceException("谷歌密钥二维码内容不能为空!");
        }

        int count = this.iUserGoogleAuthenticatorService.bindGoogle(param);

        return ResultUtil.success(count);
    }*/


}
