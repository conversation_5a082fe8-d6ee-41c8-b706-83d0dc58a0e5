package com.juyou.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juyou.common.core.domain.entity.SysDictData;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账号充值Service接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface IAccountRechargeService extends IService<AccountRecharge> {


    /**
     * 修改为已生效
     * @param ids
     * @return
     */
    boolean updateNotEffectiveList(Integer[] ids);

    /**
     * 为生效账号列表
     * @param param
     * @return
     */
    List<AccountRechargeNotEffectiveListVo> notEffectiveList(AccountRechargeNotEffectiveParam param);

    /**
     * 到达等待时间放入二级库
     * @return
     */
    boolean pendingMinutesPutTwoChargeStage();

    /**
     * 编辑备注
     * @param param
     * @return
     */
    Boolean editRemark(AccountRechargeEditRemarkParam param);

    /**
     * 代充编辑
     * @param param
     * @return
     */
    boolean chargingInquiryEdit(AccountRechargeParam param);

    /**
     * 代充查询
     * @param param
     * @return
     */
    List<ChargingInquiryListVo> chargingInquiryList(ChargingInquirySearchParam param);

    /**
     * 恢复
     * @param acid
     * @return
     */
    boolean recovery(Integer acid);

    /**
     * 获取详情
     * @param acid
     * @return
     */
    AccountRechargeDetailVo getDetail(Integer acid);

    /**
     * 查询我的剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findMyResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 查询二级库剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findTwoLevelResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 查询一级库剩余账号列表
     * @param param
     * @return
     */
    List<AccountSellResidualAccountVoList> findOneLevelResidualAccountList(AccountRechargeResidualParam param);

    /**
     * 统计根据状态
     * @param status
     * @return
     */
    public Integer countByStatus(String status,String accountZone,String idType);


    /**
     * 账号充值-数据面板
     * @param param
     * @return
     */
    AccountRechargeDataPanelVo getDataPanel(AccountRechargeDataPanelParam param);

    /**
     * 我的充值作废率
     * @param userId
     * @return
     */
    BigDecimal getMyRechargeCancelRate(Long userId);

    /**
     * 我今日已充值金额
     * @param userId
     * @return
     */
    public BigDecimal getTodayRechargeAmount(Long userId,String  accountZone,String idType);

    /**
     * 转交账号
     *
     * @param param
     * @return
     */
    public int transferAccount(AccountTransferAccountParam param);

    /**
     * 查询账号充值
     *
     * @param acid 账号充值主键
     * @return 账号充值
     */
    public AccountRecharge selectAccountRechargeByAcid(Integer acid);


    /**
     * 账号领取
     * @param param
     * @return
     */
    int receiveAccount(AccountRechargeReceiveParam param);

    /**
     * 二级账号领取
     * @param param
     * @return
     */
    int receiveAccountTwo(AccountRechargeReceiveParam param);


    /**
     * 账号领取
     *
     * @param count    领取数量
     * @param userId   领取用户id
     * @param userName 领取用户名称
     * @param deptId  部门id
     */
    public int receiveAccount(int count, Long userId, String userName, Long deptId,String accountZone,String idType);


    /**
     * 领取指定钱包区域的账户
     *
     * @param count    领取数量
     * @param userId   领取用户id
     * @param userName 领取用户名称
     * @param deptId  部门id
     * @param walletArea  钱包区域
     */

    public int designationReceiveAccount(int count, Long userId, String userName, Long deptId,String accountZone,String idType ,String walletArea );

    /**
     * 账号充值导入
     *
     * @param list 导入的列表
     */
    public String importData(List<AccountRecharge> list);

    void importExhibitors(MultipartFile exhibitors);


    /**
     * 查询充值明细报表分页
     * @param param
     * @return
     */
    public List<AccountRecharge> selectAccountRechargeReportPage(AccountRechargePageParam param);

    /**
     * 查询账号充值列表-分页用
     *
     * @param param
     * @return
     */
    public List<AccountRecharge> selectAccountRechargePage(AccountRechargePageParam param);

    /**
     * 查询账号充值列表
     *
     * @param accountRecharge 账号充值
     * @return 账号充值集合
     */
    public List<AccountRecharge> selectAccountRechargeList(AccountRecharge accountRecharge);

    /**
     * 新增账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    public int insertAccountRecharge(AccountRecharge accountRecharge);

    /**
     * 充值
     * @param param
     * @return
     */
    int recharge(AccountRechargeParam param);

    /**
     * 二级充值
     * @param param
     * @return
     */
    int rechargeTwo(AccountRechargeParam param);

    /**
     * 完成充值
     * @param param
     * @param currentUsername
     * @return
     */
    public int finishRecharge(AccountRechargeEditParam param, String currentUsername);

    /**
     * 部分充值
     * @param param
     * @return
     */
    public int partialRecharge(AccountRechargeEditParam param, String currentUsername);

    /**
     * 修改账号充值
     *
     * @param accountRecharge 账号充值
     * @return 结果
     */
    public int updateAccountRecharge(AccountRecharge accountRecharge);


    /**
     * 状态修改为作废
     *
     * @param param
     * @return
     */
    public int cancel(AccountRechargeCancelParam param);

    /**
     * 批量删除账号充值
     *
     * @param acids 需要删除的账号充值主键集合
     * @return 结果
     */
    public int deleteAccountRechargeByAcids(Integer[] acids);

    /**
     * 删除账号充值信息
     *
     * @param acid 账号充值主键
     * @return 结果
     */
    public int deleteAccountRechargeByAcid(Integer acid);

    /**
     * 批量核对
     * @param param
     * @return
     */
    Integer batchWriteOff(@RequestBody AccountRechargeBatchWriteOffParam param);

    public List<AccountRechargeVo> selectAccountRechargeVoList(AccountRecharge accountRecharge);

    public int  putAccountRecharge (AccountRecharge accountRecharge);
    /**
     * 获取一级库用户列表
     *
     * @return
     */
    public List<KeyValueVo> getlevel1UserList();

    /**
     * 获取二级库用户列表
     *
     * @return
     */
    public List<KeyValueVo> getlevel2UserList();
}
