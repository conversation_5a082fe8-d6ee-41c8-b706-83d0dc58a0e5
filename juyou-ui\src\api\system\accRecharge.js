import request from '@/utils/request'

// 查询账号充值列表
export function listAccRecharge(query) {
  return request({
    url: '/system/accRecharge/list',
    method: 'get',
    params: query
  })
}

export function listAccRechargeZone(query) {
  return request({
    url: '/system/accRecharge/listZone',
    method: 'get',
    params: query
  })
}

// 查询账号充值详细
export function getAccRecharge(acid) {
  return request({
    url: '/system/accRecharge/' + acid,
    method: 'get'
  })
}

// 新增账号充值
export function addAccRecharge(data) {
  return request({
    url: '/system/accRecharge',
    method: 'post',
    data: data
  })
}

// 修改账号充值
export function updateAccRecharge(data) {
  return request({
    url: '/system/accRecharge',
    method: 'put',
    data: data
  })
}

// 删除账号充值
export function delAccRecharge(acid) {
  return request({
    url: '/system/accRecharge/' + acid,
    method: 'delete'
  })
}
// 作废账号充值
export function cancelAccRecharge(acid) {
  return request({
    url: 'system/accRecharge/cancel/'+ acid,
    method: 'post',
    data:{}
  })
}

//作废充值账号新接口
export function newCancelAccRecharge(data) {
  return request({
    url: 'system/accRecharge/cancel',
    method: 'post',
    data:data
  })
}

// 领取账号
export function creceiveAccountRecharge(count,accountZone,idType) {
  return request({
    url: 'system/accRecharge/receiveAccount?count='+ count + '&accountZone='+ accountZone+'&idType='+idType,
    method: 'post',
    data:{}
  })
}

// 领取账号
export function getListExcludeMeRecharge(data={}) {
  return request({
    url: 'system/user/getListExcludeMe',
    method: 'post',
    data:data
  })
}

// 转交账号
export function transferAccountRecharge(data={}) {
  return request({
    url: 'system/accRecharge/transferAccount',
    method: 'post',
    data:data
  })
}

// 我今日已充值金额
export function getTodayRechargeAmount(accountZone,idType) {
  return request({
    url: 'system/accRecharge/getTodayRechargeAmount?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}

// 账号注册未领取账号数量
export function getUnclaimedAccountCount(accountZone,idType) {
  return request({
    url: 'system/accRecharge/getUnclaimedAccountCount?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}

//直接完成充值
export function accRechargeCompleteRecharge(acid) {
  return request({
    url: 'system/accRecharge/completeRecharge/' +acid,
    method: 'post',
    data:{}
  })
}



