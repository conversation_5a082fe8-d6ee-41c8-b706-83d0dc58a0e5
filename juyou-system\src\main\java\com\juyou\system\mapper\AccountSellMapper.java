package com.juyou.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.*;
import com.juyou.system.params.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * sellcardMapper接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface AccountSellMapper extends BaseMapper<AccountSell> {

    /**
     * 查询业绩列表
     * @return
     */
    List<PerformanceVoList> findPerformanceList(@Param("param") PerformanceSearchParam param);

    /**
     * 获取账号销售-已出售列表
     * @param param
     * @return
     */
    List<AccountSell> getAccountSellSoldList(@Param("param") AccountSellSoldListSearchParam param);

    /**
     * 出售群统计明细列表
     * @param param
     * @return
     */
    List<AccountSell> getSellGroupDetailList(@Param("param") SellGroupDetailSearchParam param);

    /**
     * 获取账号销售-已出售账号列表
     * @param param
     * @return
     */
    List<AccountSellVo> getAccountSellSoldReportList(@Param("param") AccountSellPendingSaleSearchParam param);

    /**
     * 获取账号销售-待出售账号列表
     * @param param
     * @return
     */
    List<AccountSellVo> getAccountSellPendingSaleList(@Param("param") AccountSellPendingSaleSearchParam param);

    /**
     * 账号出售可领取的系统剩余账号列表
     *
     * @return
     */
    List<AccountSellResidualAccountVoList> getSystemResidualAccountList(@Param("accountSellPendingMinutes")
                                                                        Integer accountSellPendingMinutes,
                                                                        @Param("accountZone")
                                                                        String accountZone,@Param("idType")String idType,@Param("accountSellPendingMinutesTwo")
                                                                        Integer accountSellPendingMinutesTwo);
    /**
     * 雷蛇美国可领取的系统剩余账号列表
     *
     * @return
     */

    List<AccountSellResidualAccountVoList> getSystemResidualAccountLeiSheList(@Param("accountSellPendingMinutes")
                                                                              Integer accountSellPendingMinutes,
                                                                              @Param("accountZone")
                                                                              String accountZone,
                                                                              @Param("walletArea")
                                                                              String walletArea,
                                                                              @Param("idType")
                                                                              String idType);



    /**
     * 我今日已出售金额
     *
     * @param userId
     * @return
     */
    BigDecimal getTodayAmountSold(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate,@Param("accountZone")String accountZone,@Param("idType")String idType);

    /**
     * 获取待领取总金额
     * @return
     */
    BigDecimal getAwaitReceiveTotalAmount(@Param("accountSellPendingMinutes") Integer accountSellPendingMinutes,@Param("accountZone") String accountZone,@Param("idType")String idType);

    /**
     * 账号销售列表统计分页
     *
     * @param param
     * @return
     */
    List<SellChatgroupVo> getSellChatgroupList(@Param("param") SellChatgroupParam param);

    /**
     * 剩余成本总额
     *
     * @return
     */
    public BigDecimal getRemainingCostTotal();

    /**
     * 出售总余额
     *
     * @return
     */
    //public BigDecimal getTheTotalBalanceOfTheSale();


    /**
     * 出售作废率
     *
     * @return
     */
    public BigDecimal getSellVoidedBalances(Long receiveUserId );
    /**
     * 出售作废率
     *
     * @return
     */
    public BigDecimal getSellVoidedBalancesDate(@Param("receiveUserId") Long receiveUserId,
                                                @Param("Date") DateRangeParam dateRangeParam  );
    /**
     * 剩余实际充值总额
     *
     * @return
     */
    public List<ZoneGroupVo> getRemainingActualRechargeTotal();

    /**
     * 转交账号
     *
     * @param param
     * @return
     */
    public int transferAccount(@Param("param") AccountTransferAccountParam param);

    /**
     * 更新领取账号
     *
     * @param ids
     * @param userId
     * @param userName
     * @param deptId
     * @return
     */
    public int updateReceiveAccount(@Param("ids") List<Integer> ids, @Param("userId") Long userId,
                                    @Param("userName") String userName, @Param("deptId") Long deptId);

    /**
     * 查询可领取的数量
     *
     * @param accountSellPendingMinutes 账号出售待生效分钟数
     * @param count
     * @return
     */
    public List<AccountSell> selectCanBeClaimedAccount(@Param("accountSellPendingMinutes")
                                                       Integer accountSellPendingMinutes,
                                                       @Param("count") int count,
                                                       @Param("cardBalanceList") List<Double> cardBalanceList,
                                                       @Param("chargeStage")String chargeStage);
    /**
     * 查询可领取钱包区域的数量
     *
     * @param accountSellPendingMinutes 账号出售待生效分钟数
     * @param count
     * @return
     */
    public List<AccountSell> selectCanBeClaimedAccountByWalletArea(@Param("accountSellPendingMinutes")
                                                       Integer accountSellPendingMinutes,
                                                       @Param("count") int count,
                                                       @Param("cardBalanceList") List<Double> cardBalanceList,
                                                                   @Param("walletArea") String walletArea);


    /**
     * 更新账号出售完成时间分钟数(账号在未生效持续的时间,单位分钟)
     *
     * @return
     */
    public int updateNotEffectiveCompletedTimeMinutesNum();

    /**
     * 未生效账号列表
     *
     * @param param
     * @return
     */
    public List<AccountSellVo> notEffectiveList(@Param("param") AccountSellNotEffectiveParam param);

    /**
     * 获取待生效账号出售列表
     *
     * @return
     */
    public List<AccountSell> selectToBeEffectiveList();

    /**
     * 查询sellcard
     *
     * @param rechargeId sellcard主键
     * @return sellcard
     */
    public AccountSell selectAccountSellByRechargeId(Integer rechargeId);

    /**
     * 查询sellcard
     *
     * @param rechargeId sellcard主键
     * @return sellcard
     */
    public AccountSell selectOldAccountSellByRechargeId(Integer rechargeId);

    /**
     * 出售明细报表列表
     *
     * @param param
     * @return
     */
    public List<AccountSell> selectAccountReportList(@Param("param") AccountSellPageParam param);

    /**
     * 查询sellcard列表
     * <p>
     * 加上管理员只能看or条件的数据
     * <if test="param.params.dataScope != null and param.params.dataScope != ''">
     * 1 = 1 ${param.params.dataScope}
     * or
     * </if>
     *
     * @param param sellcard
     * @return sellcard集合
     */
    public List<AccountSell> selectAccountSellList(@Param("param") AccountSellPageParam param);
    /**
     * 查询903ellcard列表
     * <p>
     * 加上管理员只能看or条件的数据
     * <if test="param.params.dataScope != null and param.params.dataScope != ''">
     * 1 = 1 ${param.params.dataScope}
     * or
     * </if>
     *
     * @param param sellcard
     * @return sellcard集合
     */
    public List<AccountSell> selectAccountSellListByAnchor(@Param("param") SellAnchorVo param);

    /**
     * 新增sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int insertAccountSell(AccountSell accountSell);

    /**
     * 修改sellcard
     *
     * @param accountSell sellcard
     * @return 结果
     */
    public int updateAccountSell(AccountSell accountSell);


    /**
     * 批量修改账号出售状态
     *
     * @param rechargeIds
     * @param status
     * @return
     */
    public int batchUpdateAccountSellStatus(@Param("rechargeIds") List<Integer> rechargeIds, @Param("status") String status);

    /**
     * 修改账号出售状态
     *
     * @param rechargeId 主键
     * @param status     状态
     * @return
     */
    public int updateAccountSellStatus(@Param("rechargeId") Integer rechargeId, @Param("status") String status);
    /**
     * 批量修改账号出售状态
     *
     * @param rechargeId
     * @param status
     *
     * @return
     */
    public int updateOldAccountSellStatus(AccountSell oldAccountSell);
    /**
     * 删除sellcard
     *
     * @param rechargeId sellcard主键
     * @return 结果
     */
    public int deleteAccountSellByRechargeId(Integer rechargeId);
    /**
     * 删除sellcard
     *
     * @param acid acid 主键
     * @return 结果
     */
    public int deleteAccountSellByAcid(Integer acid);
    /**
     * 批量删除sellcard
     *
     * @param rechargeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAccountSellByRechargeIds(Integer[] rechargeIds);

    /**
     * 根据账号充值id集合删除
     * @param acidList
     * @return
     */
    int deleteByAcidList(@Param("acidList") List<Integer> acidList);

    public int updatePinned(AccountSell accountSell);

    void insertRemarks(AccountSell accountSell);

    public List<AccountRechargeVo> selectAccountRechargeVoList(@Param("idType") String idType);



    /**
     *
     * */
    public int filterTheNumberOfClaimedAndUnsoldAccounts(Long receiveUserId);
    //获取所有数据库账号出售列表
    public List<AccountSell> selectAccountSellListAll(@Param("param") AccountSellPageParam param);

    public int updateOldAccountSellByAcid(Integer acid);
}
