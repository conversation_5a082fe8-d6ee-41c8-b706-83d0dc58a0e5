package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 账号充值-数据面板-param
 */
@Data
@ApiModel("AccountRechargeDataPanelParam")
public class AccountRechargeDataPanelParam implements Serializable {

    private static final long serialVersionUID = -1678686955891587436L;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("登陆用户id:后端使用")
    private Long loginUserId;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

}
