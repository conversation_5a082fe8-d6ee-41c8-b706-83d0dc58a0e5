import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/new/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/new/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/new/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/new/system/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/new/system/user/' + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/new/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/new/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/new/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/new/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/new/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/new/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/new/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/new/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 查询部门用户下拉树结构
export function treeselect() {
  return request({
    url: '/new/system/user/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询部门用户树结构
export function roleUserTreeselect(roleId) {
  return request({
    url: '/new/system/user/roleUserTreeselect/' + roleId,
    method: 'get'
  })
}

// 根据username生成google二维码
export function generateGoogleSecret(data = {}) {
  return request({
    url: '/new/userGoogleAuthenticator/generateGoogleSecret',
    method: 'get',
    params: data
  })
}
// 更新锁状态
export function updateLockStatus(data) {
  return request({
    url: '/new/system/user/updateLockStatus',
    method: 'put',
    data
  })
}
// 更新锁状态
export function verifyTheGoogleCode(data) {
  return request({
    url: '/new/system/user/verifyTheGoogleCode',
    method: 'post',
    data
  })
}
