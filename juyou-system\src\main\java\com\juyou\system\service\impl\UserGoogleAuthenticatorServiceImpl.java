package com.juyou.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.exception.ServiceException;
import com.juyou.system.constants.GoogleAuthenticatorConstant;
import com.juyou.system.domain.UserGoogleAuthenticator;
import com.juyou.system.domain.vo.GoogleAuthenticatorSecretVo;
import com.juyou.system.mapper.UserGoogleAuthenticatorMapper;
import com.juyou.system.service.ISysUserService;
import com.juyou.system.service.IUserGoogleAuthenticatorService;
import com.juyou.system.utils.GoogleAuthenticator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户谷歌验证器Service
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class UserGoogleAuthenticatorServiceImpl implements IUserGoogleAuthenticatorService {

    @Autowired
    private UserGoogleAuthenticatorMapper userGoogleAuthenticatorMapper;
    
    @Autowired
    private ISysUserService iSysUserService;

    @Override
    public UserGoogleAuthenticator getByUsername(String username) {
        UserGoogleAuthenticator select = new UserGoogleAuthenticator();
        select.setUserName(username);
        List<UserGoogleAuthenticator> userGooleAuthList = this.selectUserGoogleAuthenticatorList(select);
        if (CollUtil.isNotEmpty(userGooleAuthList)) {
            return userGooleAuthList.get(0);
        }
        return null;
    }


    public GoogleAuthenticatorSecretVo generateGoogleSecret(String currentAccount) {
        GoogleAuthenticatorSecretVo vo = new GoogleAuthenticatorSecretVo();
        
        // check
        SysUser sysUser = this.iSysUserService.selectUserByUserName(currentAccount);
        if(ObjUtil.isNull(sysUser)){
            throw new ServiceException("账号不存在!");
        }

        // 该账号是否存在验证器
        UserGoogleAuthenticator select = new UserGoogleAuthenticator();
        select.setUserName(currentAccount);
        List<UserGoogleAuthenticator> userGooleAuthList = this.selectUserGoogleAuthenticatorList(select);
        if (CollUtil.isNotEmpty(userGooleAuthList)) {
            UserGoogleAuthenticator userGoogleAuthenticator = userGooleAuthList.get(0);
            vo.setGoogleSecret(userGoogleAuthenticator.getGoogleSecret());
            vo.setSecretQrCode(userGoogleAuthenticator.getSecretQrCode());
            return vo;
        }

        //Google密钥
        String randomSecretKey = GoogleAuthenticator.getRandomSecretKey();
        String googleAuthenticatorBarCode = GoogleAuthenticator.getGoogleAuthenticatorBarCode(randomSecretKey, currentAccount, GoogleAuthenticatorConstant.ISSUER);

        vo.setGoogleSecret(randomSecretKey);
        vo.setSecretQrCode(googleAuthenticatorBarCode);

        // 绑定用户
        UserGoogleAuthenticator userGoogleAuthenticator = new UserGoogleAuthenticator();
        userGoogleAuthenticator.setUserId(sysUser.getUserId());
        userGoogleAuthenticator.setUserName(sysUser.getUserName());
        userGoogleAuthenticator.setGoogleSecret(randomSecretKey);
        userGoogleAuthenticator.setSecretQrCode(googleAuthenticatorBarCode);
        this.insertUserGoogleAuthenticator(userGoogleAuthenticator);

        return vo;
    }

    /**
     * 查询用户谷歌验证器关联
     *
     * @param id 用户谷歌验证器关联主键
     * @return 用户谷歌验证器关联
     */
    @Override
    public UserGoogleAuthenticator selectUserGoogleAuthenticatorById(Long id) {
        return userGoogleAuthenticatorMapper.selectUserGoogleAuthenticatorById(id);
    }

    /**
     * 查询用户谷歌验证器关联列表
     *
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 用户谷歌验证器关联
     */
    @Override
    public List<UserGoogleAuthenticator> selectUserGoogleAuthenticatorList(UserGoogleAuthenticator userGoogleAuthenticator) {
        return userGoogleAuthenticatorMapper.selectUserGoogleAuthenticatorList(userGoogleAuthenticator);
    }

    /**
     * 新增用户谷歌验证器关联
     *
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 结果
     */
    @Override
    public int insertUserGoogleAuthenticator(UserGoogleAuthenticator userGoogleAuthenticator) {
        return userGoogleAuthenticatorMapper.insertUserGoogleAuthenticator(userGoogleAuthenticator);
    }

    /**
     * 修改用户谷歌验证器关联
     *
     * @param userGoogleAuthenticator 用户谷歌验证器关联
     * @return 结果
     */
    @Override
    public int updateUserGoogleAuthenticator(UserGoogleAuthenticator userGoogleAuthenticator) {
        return userGoogleAuthenticatorMapper.updateUserGoogleAuthenticator(userGoogleAuthenticator);
    }

    /**
     * 批量删除用户谷歌验证器关联
     *
     * @param ids 需要删除的用户谷歌验证器关联主键
     * @return 结果
     */
    @Override
    public int deleteUserGoogleAuthenticatorByIds(Long[] ids) {
        return userGoogleAuthenticatorMapper.deleteUserGoogleAuthenticatorByIds(ids);
    }

    /**
     * 删除用户谷歌验证器关联信息
     *
     * @param id 用户谷歌验证器关联主键
     * @return 结果
     */
    @Override
    public int deleteUserGoogleAuthenticatorById(Long id) {
        return userGoogleAuthenticatorMapper.deleteUserGoogleAuthenticatorById(id);
    }

}
