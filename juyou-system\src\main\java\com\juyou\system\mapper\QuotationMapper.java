package com.juyou.system.mapper;

import com.juyou.system.domain.Quotation;

import java.util.List;

/**
 * 报价Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface QuotationMapper
{
    /**
     * 查询报价
     *
     * @param id 报价主键
     * @return 报价
     */
    public Quotation selectQuotationById(Long id);

    /**
     * 查询报价列表
     *
     * @param quotation 报价
     * @return 报价集合
     */
    public List<Quotation> selectQuotationList(Quotation quotation);

    public List<Quotation> selectQuotationGroup();

    /**
     * 新增报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int insertQuotation(Quotation quotation);

    /**
     * 修改报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int updateQuotation(Quotation quotation);

    /**
     * 删除报价
     *
     * @param id 报价主键
     * @return 结果
     */
    public int deleteQuotationById(Long id);

    /**
     * 批量删除报价
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuotationByIds(Long[] ids);
}
