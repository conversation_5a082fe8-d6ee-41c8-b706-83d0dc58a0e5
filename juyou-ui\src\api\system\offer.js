import request from '@/utils/request'

// 查询报价列表
export function listQuotation(query) {
  return request({
    url: '/system/quotation/list',
    method: 'get',
    params: query
  })
}

export function groupList() {
  return request({
    url: '/system/quotation/groupList',
    method: 'get'
  })
}

// 查询报价详细
export function getQuotation(id) {
  return request({
    url: '/system/quotation/' + id,
    method: 'get'
  })
}

// 新增报价
export function addQuotation(data) {
  return request({
    url: '/system/quotation',
    method: 'post',
    data: data
  })
}

// 批量新增报价
export function batch(data) {
  return request({
    url: '/system/quotation/batch',
    method: 'post',
    data: data
  })
}

// 批量新增报价
export function batchUpdateQuotation(data) {
  return request({
    url: '/system/quotation/batchUpdateQuotation',
    method: 'post',
    data: data
  })
}

// 修改报价
export function updateQuotation(data) {
  return request({
    url: '/system/quotation',
    method: 'put',
    data: data
  })
}

// 删除报价
export function delQuotation(ids) {
  return request({
    url: '/system/quotation/' + ids,
    method: 'delete'
  })
}

export function openQuotation(ids) {
  return request({
    url: '/system/quotation/openQuotation/'+ids,
    method: 'put'
  })
}

export function openList(ids) {
  return request({
    url: '/system/quotation/openList/' + ids,
    method: 'put'
  })
}
export function historyAdd(data) {
    return request({
      url: 'new/system/history/add' ,
      method: 'post',
      data: data
    })
  }
export function getTheLatestNews(data) {
    return request({
      url: 'new/system/history/getTheLatestNews' ,
      method: 'get',

    })

}




