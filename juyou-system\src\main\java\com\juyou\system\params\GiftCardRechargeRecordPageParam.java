package com.juyou.system.params;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡充值记录分页-param
 */
@Data
@ApiModel("GiftCardRechargeRecordPageParam")
public class GiftCardRechargeRecordPageParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 762194291125955009L;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("来源群")
    private String sourceGroup;

    @ApiModelProperty("开始执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startExecutionTime;

    @ApiModelProperty("结束执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endExecutionTime;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("面值")
    private BigDecimal faceValue;
}
