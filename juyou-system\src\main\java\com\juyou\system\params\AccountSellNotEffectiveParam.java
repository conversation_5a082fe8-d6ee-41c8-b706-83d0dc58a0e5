package com.juyou.system.params;

import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号出售-未生效列表Param
 */
@Data
@ApiModel("AccountSellNotEffectiveParam")
public class AccountSellNotEffectiveParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -1649547273936217053L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("开始生效数")
    private Integer startEffect;

    @ApiModelProperty("结束生效数")
    private Integer endEffect;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("充值阶段")
    private String chargeStage;

    @ApiModelProperty("剩余时间小于")
    private Integer remainingTime;

    @ApiModelProperty("id余额")
    private Double[] idBalances;

    @ApiModelProperty("一级充值生效时间")
    private String firstRechargeEffectTime;

    @ApiModelProperty("二级充值生效时间")
    private String secondRechargeEffectTime;

    @ApiModelProperty("批量生效ids")
    private Integer[] rechargeIds;


}
