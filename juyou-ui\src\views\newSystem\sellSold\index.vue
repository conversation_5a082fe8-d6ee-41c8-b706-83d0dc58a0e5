<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
      <el-form-item label="员工" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入姓名或账号" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="工作日期：" prop="workDate">
        <el-date-picker v-model="queryParams.workDate" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="performanceList" :summary-method="getSummaries" show-summary>
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="账号" align="center" prop=""/>
      <el-table-column label="核对状态" align="center" prop=""/>
      <el-table-column label="来源群" align="center" prop=""/>
      <el-table-column lable="出售群" align="center" prop=""/>
      <el-table-column label="出售状态" align="center" prop=""/>
      <el-table-column label="充值金额" align="center" prop=""/>
      <el-table-column label="出售单价" align="center" prop=""/>
      <el-table-column label="出售金额" align="center" prop=""/>
      <el-table-column label="出售时间" align="center" prop=""/>
      <el-table-column label="操作" align="center" fixed="right" width="145" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button  size="mini" type="text"
                     @click="handleDelete(scope.row)">修改出售群</el-button>
          <el-button />
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"  />
  </div>
</template>

<script>
import { getPerformanceList } from '@/api/newSystem/performance'
import { quantile } from 'echarts/lib/util/number'

export default {
  name: 'accountSellPendingSale',
  data() {
    return {
      //未生效账号列表
      // notEffectiveList: [],
      // 业绩列表
      performanceList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 是否显示弹出层
      notEffectiveVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        userName: null,
        startDate: null,
        endDate: null,
        workDate: [
          new Date().toISOString().slice(0, 10),
          new Date().toISOString().slice(0, 10)
        ]
      },
      //未生效表单参数
      notEffectiveForm: {},
      // 表单校验
      notEffectiveRules: {
        cardBalance: [{ required: true, message: '请输入实际充值金额', trigger: 'blur' }],
        buyPrice: [{ required: true, message: '请输入进价', trigger: 'blur' }]

      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询未生效账号列表 */
    async getList() {
      try {
        this.loading = true
        const request = {
          ...this.queryParams,
          startDate: this.queryParams.workDate ? this.queryParams.workDate[0] : null,
          endDate: this.queryParams.workDate ? this.queryParams.workDate[1] : null,
        }
        const list_res = await getPerformanceList(request)
        if (list_res.code === 200) {
          this.performanceList = list_res.rows
          this.total = list_res.total
          this.loading = false
        }
      } catch (err) {
        console.log(err)
        this.loading = false
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams = {
        pageNum: 1,
        pageSize: 100,
        userName: null,
        startDate: null,
        endDate: null,
        workDate: null
      }
      this.handleQuery()
    },

    /**
     * 计算成本金额
     */
    checkCartAmt() {
      console.log(this.notEffectiveForm)
      if (this.checkIsNumber(this.notEffectiveForm.buyPrice) && this.checkIsNumber(this.notEffectiveForm.cardBalance)) {
        if (this.notEffectiveForm.buyPrice > 0 && this.notEffectiveForm.cardBalance > 0) {
          //保留两位小数，四舍五入
          this.notEffectiveForm.buyAmt = (this.notEffectiveForm.buyPrice * this.notEffectiveForm.cardBalance).toFixed(2)
        }
      }
    },
    /**
     * 判断是否为数字
     */
    checkIsNumber(number) {
      var numReg = /^\d+(\.\d+)?$/g
      var numRe = new RegExp(numReg)
      if (numRe.test(number)) {
        return true
      } else {
        return false
      }
    },
    getSummaries(param) {
      const notTotals = [1, 6] //不需要小计的列数组
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '小计'
          return
        }
        if (notTotals.includes(index)) {
          sums[index] = ''
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2)
            } else {
              return prev
            }
          }, 0)
          sums[index] += ' '
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

