package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业绩查询-param
 */
@Data
@ApiModel("PerformanceSearchParam")
public class PerformanceSearchParam extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -4662789148939682815L;

    @ApiModelProperty("员工")
    private String userName;

    @ApiModelProperty("开始工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("结束工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;



}
