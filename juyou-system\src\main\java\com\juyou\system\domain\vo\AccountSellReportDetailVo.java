package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.GiftCardRechargeRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 出售明细报表-详情-vo
 */
@Data
@ApiModel("AccountSellReportDetailVo-出售明细报表-详情-vo")
public class AccountSellReportDetailVo extends AccountSell implements Serializable {

    private static final long serialVersionUID = 4582322876696211598L;

    @ApiModelProperty("充值礼品卡列表")
    private List<GiftCardRechargeRecord> giftCardRechargeRecordList;
}
