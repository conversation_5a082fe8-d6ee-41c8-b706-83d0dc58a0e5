package com.juyou.system.service;

import com.juyou.system.domain.Quotation;

import java.util.List;

/**
 * 报价Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface IQuotationService
{
    /**
     * 查询报价
     *
     * @param id 报价主键
     * @return 报价
     */
    public Quotation selectQuotationById(Long id);

    public int openQuotation(Long[] ids);

    public int openList(Long[] ids);

    /**
     * 查询报价列表
     *
     * @param quotation 报价
     * @return 报价集合
     */
    public List<Quotation> selectQuotationList(Quotation quotation);

    public List<Quotation> selectQuotationGroup();

    /**
     * 新增报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int insertQuotation(Quotation quotation);

    /**
     * 批量新增报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int batch(List<Quotation> quotation);

    /**
     * 批量修改报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int batchUpdateQuotation(List<Quotation> quotation);
    /**
     * 修改报价
     *
     * @param quotation 报价
     * @return 结果
     */
    public int updateQuotation(Quotation quotation);

    /**
     * 批量删除报价
     *
     * @param ids 需要删除的报价主键集合
     * @return 结果
     */
    public int deleteQuotationByIds(Long[] ids);

    /**
     * 删除报价信息
     *
     * @param id 报价主键
     * @return 结果
     */
    public int deleteQuotationById(Long id);
}
