package com.juyou.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.system.domain.vo.ChargingInquiryExcelVo;
import com.juyou.system.domain.vo.ChargingInquiryListVo;
import com.juyou.system.enums.AccountRechArgeChargeStageEnum;
import com.juyou.system.enums.AccountRechargeEnum;
import com.juyou.system.enums.AccountSellWriteOffStatusEnum;
import com.juyou.system.params.ChargingInquirySearchParam;
import com.juyou.system.utils.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 代充查询-test
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class ChargingInquiryServiceTest {

    @Autowired
    private IAccountRechargeService accountRechargeService;

    @Autowired
    private ISysUserService sysUserService;


    @Test
    public void list() {
        ChargingInquirySearchParam param = new ChargingInquirySearchParam();
        List<ChargingInquiryListVo> voList = this.accountRechargeService.chargingInquiryList(param);
        log.info("voList:{}", JSONUtil.toJsonPrettyStr(voList));
    }
    
    @Test
    public void export() {
        ChargingInquirySearchParam param = new ChargingInquirySearchParam();
        List<ChargingInquiryListVo> voList = this.accountRechargeService.chargingInquiryList(param);
        List<ChargingInquiryExcelVo> excelVoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(voList)) {
            Long i = 1L;
            for (ChargingInquiryListVo item : voList) {
                ChargingInquiryExcelVo excelVo = new ChargingInquiryExcelVo();
                BeanUtil.copyProperties(item, excelVo);
                excelVo.setId(i++);

                AccountSellWriteOffStatusEnum writeOffStatusEnum = EnumUtil.getEnum(AccountSellWriteOffStatusEnum.class, item.getWriteOffStatus());
                excelVo.setWriteOffStatus(writeOffStatusEnum.getCode());

                AccountRechargeEnum statusEnum = EnumUtil.getEnum(AccountRechargeEnum.class, item.getStatus());
                excelVo.setStatus(statusEnum.getDesc());

                AccountRechArgeChargeStageEnum chargeStageEnum = EnumUtil.getEnum(AccountRechArgeChargeStageEnum.class, item.getChargeStage());
                excelVo.setChargeStage(chargeStageEnum.getDesc());

                if (ObjUtil.isNotNull(item.getPrimaryCharger())) {
                    SysUser sysUser = this.sysUserService.selectUserById(item.getPrimaryCharger());
                    excelVo.setPrimaryCharger(sysUser.getUserName());
                }

                if (ObjUtil.isNotNull(item.getSecondaryCharger())) {
                    SysUser sysUser = this.sysUserService.selectUserById(item.getSecondaryCharger());
                    excelVo.setSecondaryCharger(sysUser.getUserName());
                }
                excelVoList.add(excelVo);
            }
        }
        log.info("vo:{}", JSONUtil.toJsonPrettyStr(excelVoList));
    }

}
