package com.juyou.system.constants;

/**
 * 参数配置-常量
 */
public class ConfigConstant {

    // 账号出售待生效分钟数-常量key
    public final static String ACCOUNT_SELL_PENDING_MINUTES = "id2.account.recharge.one.level.pending.minutes";

    public final static String ACCOUNT_SELL_PENDING_MINUTES_TWO = "id2.account.recharge.two.level.pending.minutes";

    public final static String ACCOUNT_ECHARGE_ONE = "account.echarge.one";

    public final static String ACCOUNT_ECHARGE_TWO = "account.echarge.two";

    public final static String ACCOUNT_SELL = "account.sell";

    public final static String DIURNAL_TANGENCY_POINT = "diurnal.tangency.point";

    // 领取账号数量限制
    public final static int MAX_RECEIVE_COUNT = 30;


    /**
     * id2-账号充值-一级等待分钟
     */
//    public final static String ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES = "id2.account.recharge.one.level.pending.minutes";
    public final static String ID2_ACCOUNT_RECHARGE_ONE_LEVEL_PENDING_MINUTES = ACCOUNT_SELL_PENDING_MINUTES;

    /**
     * id2-账号充值-二级等待分钟
     */
//    public final static String ID2_ACCOUNT_RECHARGE_TWO_LEVEL_PENDING_MINUTES = "id2.account.recharge.two.level.pending.minutes";
    public final static String ID2_ACCOUNT_RECHARGE_TWO_LEVEL_PENDING_MINUTES = ACCOUNT_SELL_PENDING_MINUTES_TWO;

}
