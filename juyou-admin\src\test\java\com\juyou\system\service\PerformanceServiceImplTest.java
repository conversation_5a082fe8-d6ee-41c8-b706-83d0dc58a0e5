package com.juyou.system.service;

import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.system.domain.vo.*;
import com.juyou.system.mapper.PerformanceMapper;
import com.juyou.system.params.CompanyPerformanceQueryParam;
import com.juyou.system.params.PerformanceSearchParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业绩ServiceTest
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
public class PerformanceServiceImplTest {

    @Autowired
    private IPerformanceService iPerformanceService;

    @Autowired
    PerformanceMapper performanceMapper;
    @Autowired
    ISysUserService sysUserService;

    @Test
    public void list() {
        PerformanceSearchParam param = new PerformanceSearchParam();
        param.setUserName("");
        param.setStartDate(null);
        param.setEndDate(null);
        List<PerformanceVoList> list = iPerformanceService.list(param);
        log.info("list:{}", list);
    }

    @Test
    public void getPerformanceData() {
        CompanyPerformanceQueryParam param = new CompanyPerformanceQueryParam();
        //param.setUserId(1L);
        Date endTime = new Date();
        // 创建一个 Calendar 实例，并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        // 将日历时间向前推一个月
        calendar.add(Calendar.YEAR, -1);
        // 获取推算后的时间作为 startTime
        Date startTime = calendar.getTime();
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        param.setUserName("adm");
        // BigDecimal total =  performanceMapper.getRechargeCancelRateDetail(param);
        // BigDecimal sell =  performanceMapper.getSellCancelRateDetail(param);
        //BigDecimal sell = performanceMapper.getRechargeNum(param);
        //BigDecimal sell =performanceMapper.getRechargeCancelNum(param);
//        BigDecimal sell = performanceMapper.getRechargeCancelCostTotal(param);
//        int SellNum = performanceMapper.getSellNum(param);
//        BigDecimal SellTotalAmount = performanceMapper.getSellTotalAmount(param);
//        BigDecimal SellCancelTotalAmount = performanceMapper.getSellCancelTotalAmount(param);
//       List<PerformanceDetailedVo>  productDetailVo =  performanceMapper.getPerformanceDetailedVo(param);
        PerformanceReportIsDetailedVo performanceReportIsDetailedVo = performanceMapper.getPerformanceReportIsDetailed(param);
        performanceReportIsDetailedVo.setPerformanceDetailedVoList(performanceMapper.getPerformanceDetailedVo(param));
        log.info("PerformanceReportIsDetailedVo:{}", performanceReportIsDetailedVo);
    }

    @Test
    public void getPerformanceData1() {


        Map<String, List<DepartmentVo>> map = new HashMap<>();
        List<DepartmentTreeVo> departmentTreeVos = new ArrayList<>();
        // List<DepartmentVo> departmentVoList = performanceMapper.getDepartmentList();
        List<DepartmentVo> departmentVoList = new ArrayList<>();
        DepartmentVo a = new DepartmentVo();
        DepartmentVo b = new DepartmentVo();
       // departmentVoList.add(a);
        departmentVoList.add(b);
        b.setDeptId(200L);
        b.setUserId(String.valueOf(123));

        List<SysUser> users = sysUserService.selectUserListAll(new SysUser());

        for (DepartmentVo departmentVo : departmentVoList) {
            if (departmentVo == null || departmentVo.getDeptId() == 100) {
                continue;
            }
            String userIdString = departmentVo.getUserId();
            List<Long> numbersList = Arrays.stream(userIdString.split(","))
                    .map(String::trim) // 先去除每个元素的前导和尾随空格
                    .map(Long::parseLong) // 然后将每个无空格的字符串转换为Long
                    .collect(Collectors.toList());// 最后收集到List中转换.collect(Collectors.toList());

            DepartmentTreeVo departmentTreeVo = new DepartmentTreeVo();
            departmentTreeVo.setDeptId(departmentVo.getDeptId());

            for (Long number : numbersList)
                for (SysUser user : users) {
                    if (Objects.equals(number, user.getUserId())) {
                        departmentTreeVo.setLabel(user.getDept().getDeptName());
                        DepartmentVo userVo = new DepartmentVo();
                        userVo.setUserId(user.getUserId().toString());
                        userVo.setUserName(user.getUserName());
                        userVo.setDeptName(user.getDept().getDeptName());
                        userVo.setDeptId(user.getDept().getDeptId());
                        departmentTreeVo.getChildren().add(userVo);
                    }
                }
            departmentTreeVos.add(departmentTreeVo);

        }
        log.info("departmentTreeVos:{}", departmentTreeVos);

    }
}

