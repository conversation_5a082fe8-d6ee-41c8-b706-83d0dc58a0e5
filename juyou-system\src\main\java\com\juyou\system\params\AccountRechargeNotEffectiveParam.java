package com.juyou.system.params;

import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号充值未生效账号-param
 */
@Data
@ApiModel("AccountRechargeNotEffectiveParam")
public class AccountRechargeNotEffectiveParam extends BaseEntity {

    private static final long serialVersionUID = 7751149763522355068L;


    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("充值阶段")
    private String chargeStage;

    @ApiModelProperty("剩余时间小于")
    private Integer remainingTime;

    @ApiModelProperty("id余额")
    private Double[] idBalances;

    @ApiModelProperty("批量生效ids")
    private Integer[] rechargeIds;

    /* 后端用 */
    @ApiModelProperty("id2-账号充值-一级等待分钟:后端用")
    private Integer onePendingMinutes;

    @ApiModelProperty("id2-账号充值-二级等待分钟:后端用")
    private Integer twoPendingMinutes;

}
