package com.juyou.system.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 枚举工具类
 */
@Slf4j
@Data
public class EnumUtil {


    /**
     * 获取枚举
     *
     * @param enumClass 枚举类
     * @param code      枚举中的code
     * @param <T>
     * @return
     */
    public static <T extends Enum<T>> T getEnum(Class<T> enumClass, Object code) {
        if (ObjectUtil.isNull(enumClass) || ObjectUtil.isNull(code)) {
            return null;
        }
        T[] enumConstants = enumClass.getEnumConstants();
        Field codeField = ReflectUtil.getField(enumClass, "code");
        for (T item : enumConstants) {
            Object fieldValue = ReflectUtil.getFieldValue(item, codeField);
            if (ObjectUtil.equal(fieldValue, code)) {
                return item;
            }
        }
        log.info("getEnum not found!");
        return null;
    }

    /**
     * 获取描述
     *
     * @param enumClass 枚举类
     * @param code      枚举中的code
     * @param <T>
     * @return
     */
    public static <T extends Enum<T>> String getDesc(Class<T> enumClass, Object code) {
        if (ObjectUtil.isNull(enumClass) || ObjectUtil.isNull(code)) {
            return null;
        }
        T[] enumConstants = enumClass.getEnumConstants();
        Field codeField = ReflectUtil.getField(enumClass, "code");
        Field descField = ReflectUtil.getField(enumClass, "desc");
        for (T item : enumConstants) {
            Object fieldValue = ReflectUtil.getFieldValue(item, codeField);
            if (ObjectUtil.equal(fieldValue, code)) {
                Object desc = ReflectUtil.getFieldValue(item, descField);
                if (ObjectUtil.isNotNull(desc)) {
                    return desc.toString();
                }
            }
        }
        log.info("getDesc not found!");
        return null;
    }

    public static void main(String[] args) throws NoSuchFieldException, IllegalAccessException {

    }

}
