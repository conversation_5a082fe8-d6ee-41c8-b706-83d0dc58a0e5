<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juyou.system.mapper.TowUpdateHistoryMapper">
    
    <resultMap type="TowUpdateHistory" id="TowUpdateHistoryResult">
        <result property="id"    column="id"    />
        <result property="currentInfo"    column="current_info"    />
        <result property="previousInfo"    column="previous_info"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTowUpdateHistoryVo">
        select id, current_info, previous_info, create_by, create_time, update_by, update_time from tow_update_history
    </sql>

    <select id="selectTowUpdateHistoryList" parameterType="TowUpdateHistory" resultMap="TowUpdateHistoryResult">
        <include refid="selectTowUpdateHistoryVo"/>
        <where>  
            <if test="currentInfo != null  and currentInfo != ''"> and current_info = #{currentInfo}</if>
            <if test="previousInfo != null "> and previous_info = #{previousInfo}</if>
        </where>
    </select>
    
    <select id="selectTowUpdateHistoryById" parameterType="Long" resultMap="TowUpdateHistoryResult">
        <include refid="selectTowUpdateHistoryVo"/>
        where id = #{id}
    </select>
    <insert id="insertTowUpdateHistory" parameterType="TowUpdateHistory" useGeneratedKeys="true" keyProperty="id">
        insert into tow_update_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="currentInfo != null">current_info,</if>
            <if test="previousInfo != null">previous_info,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="currentInfo != null">#{currentInfo},</if>
            <if test="previousInfo != null">#{previousInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTowUpdateHistory" parameterType="TowUpdateHistory">
        update tow_update_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="currentInfo != null">current_info = #{currentInfo},</if>
            <if test="previousInfo != null">previous_info = #{previousInfo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTowUpdateHistoryById" parameterType="Long">
        delete from tow_update_history where id = #{id}
    </delete>

    <delete id="deleteTowUpdateHistoryByIds" parameterType="String">
        delete from tow_update_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTowUpdateHistoryNew" resultType="com.juyou.system.domain.vo.TowUpdateHistory">

        <include refid="selectTowUpdateHistoryVo"/>
        order by id desc limit 1

    </select>
</mapper>