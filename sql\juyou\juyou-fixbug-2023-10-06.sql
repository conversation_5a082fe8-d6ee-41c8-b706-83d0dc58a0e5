-- 礼品卡修改成平均值线下取整
update two_gift_card_recharge_record r
    left join
    (
    select ar.account_name,MAX(ar.recharge_amt) as recharge_amt,count(*) as arCount
    FROM two_account_recharge ar
    INNER JOIN two_gift_card_recharge_record r on r.account = ar.account_name
    GROUP BY ar.account_name
    ) ar1 on ar1.account_name = r.account
    set r.face_value =
        (
        FLOOR(ar1.recharge_amt/ar1.arCount)
        ),
        r.balance =
        (
        FLOOR(ar1.recharge_amt/ar1.arCount)
        )
WHERE r.account in (
    select
    ar.account_name
    from two_account_recharge ar
    INNER JOIN (
    select r.account,
    SUM(r.face_value) as faceValueSum,
    COUNT(*) as rCount,
    GROUP_CONCAT(r.face_value) as rFvGroup
    from two_gift_card_recharge_record r
    GROUP BY r.account
    ) as g on g.account = ar.account_name
    WHERE ar.recharge_amt != g.faceValueSum
    );


-- 礼品卡取一个补齐向下取整的值
update two_gift_card_recharge_record r
    left join
    (
    select ar.account_name,MAX(ar.recharge_amt) as recharge_amt,SUM(r.face_value) as arSum
    FROM two_account_recharge ar
    INNER JOIN two_gift_card_recharge_record r on r.account = ar.account_name
    GROUP BY ar.account_name
    ) ar1 on ar1.account_name = r.account
    set r.face_value = (r.face_value+(ar1.recharge_amt-arSum))
            ,r.balance = (r.balance+(ar1.recharge_amt-arSum))
WHERE r.id in (
    select
    g.rId
    from two_account_recharge ar
    INNER JOIN (
    select r.account,
    SUM(r.face_value) as faceValueSum,
    COUNT(*) as rCount,
    GROUP_CONCAT(r.face_value) as rFvGroup,
    MAX(r.id) as rId
    from two_gift_card_recharge_record r
    GROUP BY r.account
    ) as g on g.account = ar.account_name
    WHERE ar.recharge_amt != g.faceValueSum
    );

