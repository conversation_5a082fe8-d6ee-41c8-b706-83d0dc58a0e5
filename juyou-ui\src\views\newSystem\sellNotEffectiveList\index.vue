<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="账号：" prop="accountName">
        <el-input v-model="queryParams.accountName" placeholder="请输入账号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="区域：" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option value="" label="全部"></el-option>
          <el-option
            v-for="item in zonrList"
            :key="item.dictLabel"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="充值阶段：" prop="chargestage">
        <el-select v-model="queryParams.chargestage" filterable placeholder="请选择充值阶段">
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="一级充值"></el-option>
          <el-option value="2" label="二级充值"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="" prop="exceed">
        <el-checkbox v-model="queryParams.exceed">超过7天</el-checkbox>
      </el-form-item> -->
      <el-form-item label="剩余时间小于：" prop="accountName">
        <el-input v-model="queryParams.remainingTime" placeholder="请输入剩余时间" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="ID余额：" prop="accountName">
        <el-select
          class="id-select"
          popper-class="select-none"
          v-model="queryParams.idBalances"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入ID余额">
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refreshx" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="float: right;margin-top: 10px;margin-bottom: 10px">
      <el-button @click="batchFinishWaiting" type="primary">批量完成等待</el-button>
    </div>


    <el-table @selection-change="handleSelectionChange" v-loading="loading" :data="notEffectiveList" :summary-method="getSummaries" show-summary>
       <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">
            {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
            scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
          scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="来源群" align="center" prop="sourceGroup" /> -->
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <el-table-column label="充值阶段" align="center" prop="chargeStage">
        <template slot-scope="{row}">
         {{row.chargeStage === '1' ? "一级充值" : "二级充值"}}
        </template>
      </el-table-column>
      <el-table-column label="ID余额" align="center" prop="rechargeAmt"/>
      <el-table-column label="剩余分钟" align="center" prop="surplusMinutesNum">
        <template slot-scope="{row}">
          {{row.surplusMinutesNum < 0 ? "已生效" : row.surplusMinutesNum}}
        </template>
      </el-table-column>
      <el-table-column label="一级充值人" align="center" prop="primaryCharger"/>
      <el-table-column label="二级充值人" align="center" prop="secondaryCharger"/>
      <el-table-column label="完成充值时间" align="center" prop="createTime"/>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />

  </div>
</template>

<script>
import {
  getNotEffectiveList,
  getListAll, updateNotEffectiveList
} from '@/api/newSystem/notEffective'
  import {updateSellcard} from "@/api/newSystem/sellcard";
  import {getDicts} from "@/api/newSystem/dict/data";
  import Cookies from "js-cookie";

  export default {
    name: "SellNotEffectiveList",
    dicts: ['account_sell_not_activated_effective_time'],
    data() {
      return {
        multipleSelection: [],
        //是否超过120小时
        exceed: false,
        //操作人列表
        operatoList: [],
        //未生效账号列表
        notEffectiveList: [],
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 是否显示弹出层
        notEffectiveVisible: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: true,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
        },
        //未生效表单参数
        notEffectiveForm: {},
        zonrList:{},
        // 表单校验
        notEffectiveRules: {
          cardBalance: [{required: true, message: '请输入实际充值金额', trigger: 'blur'}],
          buyPrice: [{required: true, message: '请输入进价', trigger: 'blur'}]

        }
      };
    },
    created() {
      this.getUserListAll();//获取操作人 所有人列表
      this.into();
    },
    activated() {
      this.getList()
    },
    methods: {
      async batchFinishWaiting(){
       const res = await updateNotEffectiveList({
         rechargeIds : this.ids
       })
        this.$modal.msgSuccess('设置成功')
        this.getList()
      },
      into() {
        let cook = Cookies.get("sellNotEffectiveList");
        if (cook != null && cook != undefined) {
          this.queryParams = JSON.parse(cook);
        }
        getDicts("account_zone").then(response => {
          this.zonrList = response.data;
        })
        this.getList();//获取未生效状态的账号
      },
      async getUserListAll() {
        try {
          const userList_res = await getListAll();
          if (userList_res.code === 200) {
            this.operatoList = userList_res.rows;
          }
        } catch (err) {
          console.log(err);
        }
      },
      /**一键复制账号和密码*/
      copyToClip(content) {
        const input = document.createElement('input');
        input.setAttribute('value', content);
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$modal.msgSuccess("已复制到黏贴版");
      },
      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          const takeEffectRanges = this.queryParams.takeEffectRange ? this.queryParams.takeEffectRange.split('-') : '';
          const request = {
            ...this.queryParams,
            // exceed: this.queryParams.exceed ? 1 : 0,
            startEffect: takeEffectRanges[0] || '',
            endEffect: takeEffectRanges[1] || '',
          };
          const notEffectiveList_res = await getNotEffectiveList(request);
          if (notEffectiveList_res.code === 200) {
            this.notEffectiveList = notEffectiveList_res.rows;
            this.total = notEffectiveList_res.total;
            this.loading = false;
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      // 取消按钮
      cancel() {
        this.notEffectiveVisible = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.notEffectiveForm = {
          accountName: null,//账号
          accountPwd: null,//密码
          accountZone: null,//区域
          shouldAmt: null,//应充值金额
          cardBalance: null,//实际充值金额
          buyPrice: "0",//进价
          buyAmt: null,//成本
          createUser: null,//操作人
        };
        this.resetForm("notEffectiveForm");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: false,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
        }
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.acid)
        this.single = selection.length !== 1
        this.multiple = !selection.length
        console.log(this.ids)
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.notEffectiveVisible = true;
        this.notEffectiveForm = row;
      },
      /**
       * 判断是否为数字
       */
      checkIsNumber(number) {
        var numReg = /^\d+(\.\d+)?$/g;
        var numRe = new RegExp(numReg)
        if (numRe.test(number)) {
          return true;
        } else {
          return false;
        }
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3,4, 6,7,8,9] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' ';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
<style lang="scss">
.select-none{
  display: none !important;
}
.id-select{
  .el-input__suffix{
    display: none;
  }
}

</style>
