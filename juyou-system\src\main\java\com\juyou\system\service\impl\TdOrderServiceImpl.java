package com.juyou.system.service.impl;

import com.juyou.common.annotation.DataScope;
import com.juyou.common.utils.DateUtils;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.system.domain.TdOrder;
import com.juyou.system.mapper.TdOrderMapper;
import com.juyou.system.service.ITdOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-17
 */
@Service
public class TdOrderServiceImpl implements ITdOrderService
{
    @Autowired
    private TdOrderMapper tdOrderMapper;

    /**
     * 查询订单
     * 
     * @param id 订单ID
     * @return 订单
     */
    @Override
    public TdOrder selectTdOrderById(Long id)
    {
        return tdOrderMapper.selectTdOrderById(id);
    }

    /**
     * 查询订单列表
     * 
     * @param tdOrder 订单
     * @return 订单
     */
    @Override
    @DataScope(deptAlias = "o",userAlias = "o",userField="create_by")
    public List<TdOrder> selectTdOrderList(TdOrder tdOrder)
    {
        return tdOrderMapper.selectTdOrderList(tdOrder);
    }

    /**
     * 新增订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    @Override
    public int insertTdOrder(TdOrder tdOrder)
    {
        tdOrder.setDeptId(SecurityUtils.getLoginUser().getDeptId());
        tdOrder.setCreateTime(DateUtils.getNowDate());
        tdOrder.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        return tdOrderMapper.insertTdOrder(tdOrder);
    }

    /**
     * 修改订单
     * 
     * @param tdOrder 订单
     * @return 结果
     */
    @Override
    public int updateTdOrder(TdOrder tdOrder)
    {
        tdOrder.setUpdateTime(DateUtils.getNowDate());
        return tdOrderMapper.updateTdOrder(tdOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    @Override
    public int deleteTdOrderByIds(Long[] ids)
    {
        return tdOrderMapper.deleteTdOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int deleteTdOrderById(Long id)
    {
        return tdOrderMapper.deleteTdOrderById(id);
    }
}
