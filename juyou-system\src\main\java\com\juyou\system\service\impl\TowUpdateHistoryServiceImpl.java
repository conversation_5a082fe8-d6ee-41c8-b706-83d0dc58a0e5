package com.juyou.system.service.impl;

import java.util.List;
import com.juyou.common.utils.DateUtils;

import com.juyou.system.domain.vo.TowUpdateHistory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.juyou.system.mapper.TowUpdateHistoryMapper;
import com.juyou.system.service.ITowUpdateHistoryService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class TowUpdateHistoryServiceImpl implements ITowUpdateHistoryService 
{
    @Autowired
    private TowUpdateHistoryMapper towUpdateHistoryMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TowUpdateHistory selectTowUpdateHistoryById(Long id)
    {
        return towUpdateHistoryMapper.selectTowUpdateHistoryById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TowUpdateHistory> selectTowUpdateHistoryList(TowUpdateHistory towUpdateHistory)
    {
        return towUpdateHistoryMapper.selectTowUpdateHistoryList(towUpdateHistory);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTowUpdateHistory(TowUpdateHistory towUpdateHistory)
    {
        towUpdateHistory.setCreateTime(DateUtils.getNowDate());
        return towUpdateHistoryMapper.insertTowUpdateHistory(towUpdateHistory);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param towUpdateHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTowUpdateHistory(TowUpdateHistory towUpdateHistory)
    {
        towUpdateHistory.setUpdateTime(DateUtils.getNowDate());
        return towUpdateHistoryMapper.updateTowUpdateHistory(towUpdateHistory);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTowUpdateHistoryByIds(Long[] ids)
    {
        return towUpdateHistoryMapper.deleteTowUpdateHistoryByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTowUpdateHistoryById(Long id)
    {
        return towUpdateHistoryMapper.deleteTowUpdateHistoryById(id);
    }

    @Override
    public TowUpdateHistory selectTowUpdateHistoryNew() {

        return towUpdateHistoryMapper.selectTowUpdateHistoryNew();
    }
}
