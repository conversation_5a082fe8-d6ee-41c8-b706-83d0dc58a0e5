package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 礼品卡充值统计搜索-param
 */
@Data
@ApiModel("GiftCardRechargeStatisticsSearchParam")
public class GiftCardRechargeStatisticsSearchParam implements Serializable {

    private static final long serialVersionUID = -7495163220764965244L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("礼品卡")
    private String giftCard;

    @ApiModelProperty("充值完成开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startRechargeCompleteTime;

    @ApiModelProperty("充值完成结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endRechargeCompleteTime;

    @ApiModelProperty("来源群")
    private String sourceGroup;





}
