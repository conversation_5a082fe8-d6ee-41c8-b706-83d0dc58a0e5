package com.juyou.system.mapper;

import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.AccountSell;
import com.juyou.system.domain.vo.AccountStatisticsCancelVoList;
import com.juyou.system.domain.vo.GiftCardRechargeStatisticsVo;
import com.juyou.system.params.AccountStatisticsCancelParam;
import com.juyou.system.params.GiftCardRechargeStatisticsSearchParam;
import com.juyou.system.params.RechargeCancelAmtDetailSearchParam;
import com.juyou.system.params.SellCancelAmtDetailParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账号销售统计Mapper
 */
public interface AccountSellStatisticsMapper {

    /**
     * 礼品卡充值来源群统计
     * @param param
     * @return
     */
    List<GiftCardRechargeStatisticsVo> getGiftCardRechargeStatisticsList(@Param("param") GiftCardRechargeStatisticsSearchParam param);

    /**
     * 销售账号作废金额明细列表
     *
     * @param param
     * @return
     */
    List<AccountSell> getSellCancelAmtDetailList(@Param("param") SellCancelAmtDetailParam param);

    /**
     * 充值账号作废金额明细列表
     *
     * @param param
     * @return
     */
    List<AccountRecharge> getRechargeCancelAmtDetailList(@Param("param") RechargeCancelAmtDetailSearchParam param);

    /**
     * 查询作废列表
     *
     * @param param
     * @return
     */
    List<AccountStatisticsCancelVoList> getCancelList(@Param("param") AccountStatisticsCancelParam param);

}
