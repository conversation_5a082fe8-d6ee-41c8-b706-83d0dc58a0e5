import request from '@/utils/request'

// 获取代充查询列表
export function chargingInquiryList(data) {
  return request({
    url: '/new/manager/chargingInquiry/list',
    method: 'get',
    params: data
  })
}

export function removeById(id) {
  return request({
    url: '/new/system/record/removeById/'+id,
    method: 'delete'
  })
}
// 获取出售查询列表
export function sellCardQuery(data) {
  return request({
    url: '/new/system/sellcard/query',
    method: 'get',
    params: data
  })
}
// 出售修改
export function saveSellCard(data) {
  return request({
    url: '/new/system/sellcard/save',
    method: 'post',
    data
  })
}
// 出售修改
export function accountSellSoldReportBatchWriteOff(data) {
  return request({
    url: '/new/accountSellSoldReport/batchWriteOff',
    method: 'post',
    data
  })
}
// 代充-批量核对
export function chargingInquiryBatchWriteOff(data) {
  return request({
    url: '/new/manager/chargingInquiry/batchWriteOff',
    method: 'post',
    data
  })
}
// 出售恢复
export function sellcardRestore(data) {
  return request({
    url: '/new/system/sellcard/restore',
    method: 'post',
    data
  })
}
// 代充恢复
export function chargingInquiryRecovery(acid) {
  return request({
    url: `/new/manager/chargingInquiry/recovery/${acid}`,
    method: 'post',
  })
}

// 获取一级充值人列表
export function getAListOfFirstLevelToppers(query) {
  return request({
    url: '/new/manager/chargingInquiry/getLevel1',
    method: 'get',
    params: query
  })
}
// 获取二级充值人列表
export function getAListOfSecondaryToppers(query) {
  return request({
    url: '/new/manager/chargingInquiry/getLevel2',
    method: 'get',
    params: query
  })
}
