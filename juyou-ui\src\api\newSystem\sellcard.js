import request from '@/utils/request'

// 查询sellcard列表
export function listSellcard(query) {
  return request({
    url: '/new/system/sellcard/list',
    method: 'get',
    params: query
  })
}

// 查询sellcard详细
export function getSellcard(rechargeId) {
  return request({
    url: '/new/system/sellcard/' + rechargeId,
    method: 'get'
  })
}

// 新增sellcard
export function addSellcard(data) {
  return request({
    url: '/new/system/sellcard',
    method: 'post',
    data: data
  })
}

// 修改sellcard
export function updateSellcard(data) {
  return request({
    url: '/new/system/sellcard',
    method: 'put',
    data: data
  })
}

// 修改置顶状态
export function updatePinned(data) {
  return request({
    url: '/new/system/sellcard/pinned',
    method: 'put',
    data: data
  })
}

// 删除sellcard
export function delSellcard(rechargeId) {
  return request({
    url: '/new/system/sellcard/' + rechargeId,
    method: 'delete'
  })
}

// 出售作废
export function cancelSellcard(data={}) {
  return request({
    url: '/new/system/sellcard/cancel',
    method: 'post',
    data:data
  })
}

//批量出售
export function sellcardBatchAccountSell(data=[]) {
  return request({
    url: '/new/system/sellcard/batchAccountSell',
    method: 'post',
    data:data
  })
}

//领取账号
export function receiveAccount(data={}) {
  return request({
    url: '/new/system/sellcard/receiveAccount',
    method: 'post',
    data:data
  })
}
//领取账号
export function designationReceiveAccount(data={}) {
  return request({

    url: '/new/system/sellcard/designationReceiveAccount',
    method: 'post',
    data:data
  })
}
//单个出售账号
export function sellcardSell(data={}) {
  return request({
    url: '/new/system/sellcard/sell',
    method: 'post',
    data:data
  })
}
//转交账号
export function sellcardTransferAccount(data={}) {
  return request({
    url: '/new/system/sellcard/transferAccount',
    method: 'post',
    data:data
  })
}

///获取账号销售系统剩余账号列表
export function getSystemResidualAccountList(accountZone,idType) {
  return request({
    header:{
      repeatSubmit:true
    },
    url: '/new/system/sellcard/getSystemResidualAccountList?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}
//获取雷蛇美国的账户数据
export function getSystemResidualAccountLeiSheList(accountZone,idType,walletArea) {
  return request({
    url: '/new/system/sellcard/getSystemResidualAccountLeiSheList?accountZone='+accountZone+'&idType='+idType+'&walletArea='+walletArea,
    method: 'post'
  })
}

///我今日已出售金额
export function getTodayAmountSold(idType) {
  return request({
    header:{
      repeatSubmit:true
    },
    url: '/new/system/sellcard/getTodayAmountSold?idType='+idType,
    method: 'post'
  })
}
// 获取待领取总金额
export function getAwaitReceiveTotalAmount(accountZone,idType) {
  return request({
    url: '/new/system/sellcard/getAwaitReceiveTotalAmount?accountZone='+accountZone+'&idType='+idType,
    method: 'post'
  })
}
// 获取出售报废率
export function getSellVoidedBalances(data) {
  return request({
    header:{
      repeatSubmit:true
    },
    url: '/new/system/sellcard/getSellVoidedBalances',
    method: 'post',
    data: data
  })
}
// 已出售账号列表
export function getAccountSellSoldListList(data = {}) {
  return request({
    url: '/new/sellcard/sold/getAccountSellSoldListList',
    method: 'get',
    data: data
  })
}

// 修改出售群
export function updateSellChatgroupName(data = {}){
  return request({
    url: '/new/accountSellSoldReport/updateSellChatgroupName',
    method: 'post',
    data: data
  })
}

export function listZone(idType) {
  return request({
    url: '/new/system/sellcard/listZone?idType='+idType,
    method: 'get'
  })
}
export function putItIntoTheSecondaryLibrary(data) {
  return request({
    url: '/new/system/sellcard/put',
    method: 'put',
    data
  })
}
export function editRemark(data) {
  return request({
    url: '/new/manager/twoLevelAccountRecharge/editRemark',
    method: 'post',
    data
  })
}
export function returnAccountSell(data) {
  return request({
    url: '/new/system/sellcard/returnAccountSell',
    method: 'put',
    data
  })
}
export function verifyGoogleCaptcha(data) {
  return request({
    url: '/new/system/sellcard/copy',
    method: 'post',
    data
  })
}
//获取出售详情
export function getTheSaleDetails(rechargeId) {
  return request({
    url: `/new/system/sellcard/${rechargeId}`,
    method: 'get',
  })
}
