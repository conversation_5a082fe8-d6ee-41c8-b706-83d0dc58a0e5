package com.juyou.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.mapper.AccountRechargeMapper;
import com.juyou.system.mapper.GiftCardRechargeRecordMapper;
import com.juyou.system.params.AccountRechargeEditParam;
import com.juyou.system.params.AccountRechargeParam;
import com.juyou.system.params.GiftCardRechargeRecordPageParam;
import com.juyou.system.params.GiftCardRechargeStatisticsDetailSearchParam;
import com.juyou.system.service.IAccountRechargeService;
import com.juyou.system.service.IGiftCardRechargeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 礼品卡充值记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class GiftCardRechargeRecordServiceImpl implements IGiftCardRechargeRecordService {
    @Autowired
    private GiftCardRechargeRecordMapper giftCardRechargeRecordMapper;

    @Autowired
    private AccountRechargeMapper accountRechargeManager;

    @Override
    public List<GiftCardRechargeRecord> getGiftCardRechargeStatisticsDetailList(GiftCardRechargeStatisticsDetailSearchParam param) {
        return this.giftCardRechargeRecordMapper.getGiftCardRechargeStatisticsDetailList(param);
    }

    @Override
    public BigDecimal getBuyAmt(String account) {
        BigDecimal buyAmt = this.giftCardRechargeRecordMapper.getBuyAmt(account);
        return buyAmt;
    }

    @Override
    public BigDecimal getFaceValue(String account) {
        BigDecimal faceValue = this.giftCardRechargeRecordMapper.getFaceValue(account);
        return faceValue;
    }

    @Override
    public int batchInsert(List<AccountRechargeParam.RechargeGiftCardRechargeRecordListVo> list, AccountRecharge recharge,
                           String createBy) {
        Date now = new Date();


        if (CollUtil.isNotEmpty(list)) {
            List<GiftCardRechargeRecord> recordList = new ArrayList<>();
            for (AccountRechargeParam.RechargeGiftCardRechargeRecordListVo card : list) {
                GiftCardRechargeRecord record = new GiftCardRechargeRecord();
                record.setAccount(recharge.getAccountName());
                record.setAccountPwd(recharge.getAccountPwd());
                record.setGiftCard(card.getGiftCard());
                record.setExecutionTime(new Date());
                record.setFaceValue(card.getFaceValue());
                record.setBalance(card.getFaceValue());
                record.setBuyPrice(card.getBuyPrice());
                // 礼品卡成本 = 面值*进价
                BigDecimal buyAmt = card.getFaceValue().multiply(card.getBuyPrice());
                record.setBuyAmt(buyAmt);
                record.setPledgeThirtyMinutes(card.getPledgeThirtyMinutes());
                record.setPledgeStartDate(now);
                record.setPledgeEndDate(DateUtil.offsetMinute(now, 30));
                record.setCreateBy(String.valueOf(createBy));
                record.setCreateTime(now);
                record.setUpdateBy(String.valueOf(createBy));
                record.setUpdateTime(now);
                record.setSourceGroup(card.getSourceGroup());
                this.giftCardRechargeRecordMapper.insertGiftCardRechargeRecord(record);
            }
        }


        return 0;
    }

    @Override
    public int insert(AccountRechargeEditParam param, String currentUsername) {
        Date now = new Date();

        GiftCardRechargeRecord giftCardRechargeRecord = new GiftCardRechargeRecord();
        giftCardRechargeRecord.setAccount(param.getAccountName());
        giftCardRechargeRecord.setAccountPwd(param.getAccountPwd());
        giftCardRechargeRecord.setGiftCard(param.getGiftCard());
        //giftCardRechargeRecord.setCardStatus("有效");
        giftCardRechargeRecord.setExecutionTime(new Date());
        giftCardRechargeRecord.setFaceValue(param.getTimeAmt());
        giftCardRechargeRecord.setBalance(param.getTimeAmt());
        giftCardRechargeRecord.setBuyPrice(BigDecimal.valueOf(param.getBuyPrice()));
        // 礼品卡成本 = 面值*进价
        BigDecimal buyAmt = giftCardRechargeRecord.getFaceValue().multiply(giftCardRechargeRecord.getBuyPrice());
        giftCardRechargeRecord.setBuyAmt(buyAmt);
        giftCardRechargeRecord.setPledgeThirtyMinutes(param.getPledgeThirtyMinutes());
        giftCardRechargeRecord.setPledgeStartDate(now);
        giftCardRechargeRecord.setPledgeEndDate(DateUtil.offsetMinute(now, 30));
        giftCardRechargeRecord.setCreateBy(currentUsername);
        giftCardRechargeRecord.setCreateTime(now);
        giftCardRechargeRecord.setUpdateBy(currentUsername);
        giftCardRechargeRecord.setUpdateTime(now);
        giftCardRechargeRecord.setSourceGroup(param.getSourceGroup());
        int i = this.insertGiftCardRechargeRecord(giftCardRechargeRecord);
        log.info("新增礼品卡成功条数:{}", i);
        return 0;
    }

    @Override
    public int countPledgeThirtyMinutes(String account) {
        return this.giftCardRechargeRecordMapper.countPledgeThirtyMinutes(account);
    }

    /**
     * 查询礼品卡充值记录
     *
     * @param id 礼品卡充值记录主键
     * @return 礼品卡充值记录
     */
    @Override
    public GiftCardRechargeRecord selectGiftCardRechargeRecordById(Long id) {
        return giftCardRechargeRecordMapper.selectGiftCardRechargeRecordById(id);
    }

    @Override
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordPage(GiftCardRechargeRecordPageParam param) {
        return this.giftCardRechargeRecordMapper.selectGiftCardRechargeRecordPage(param);
    }

    /**
     * 查询礼品卡充值记录列表
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 礼品卡充值记录
     */
    @Override
    public List<GiftCardRechargeRecord> selectGiftCardRechargeRecordList(GiftCardRechargeRecord giftCardRechargeRecord) {
        return giftCardRechargeRecordMapper.selectGiftCardRechargeRecordList(giftCardRechargeRecord);
    }

    /**
     * 新增礼品卡充值记录
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    @Override
    public int insertGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord) {
        return giftCardRechargeRecordMapper.insertGiftCardRechargeRecord(giftCardRechargeRecord);
    }

    /**
     * 修改礼品卡充值记录
     *
     * @param giftCardRechargeRecord 礼品卡充值记录
     * @return 结果
     */
    @Override
    public int updateGiftCardRechargeRecord(GiftCardRechargeRecord giftCardRechargeRecord) {
        return giftCardRechargeRecordMapper.updateGiftCardRechargeRecord(giftCardRechargeRecord);
    }

    @Override
    public int deleteByAccountName(String accountName) {
        return this.giftCardRechargeRecordMapper.deleteByAccountName(accountName);
    }

    /**
     * 批量删除礼品卡充值记录
     *
     * @param ids 需要删除的礼品卡充值记录主键
     * @return 结果
     */
    @Override
    public int deleteGiftCardRechargeRecordByIds(Long[] ids) {
        return giftCardRechargeRecordMapper.deleteGiftCardRechargeRecordByIds(ids);
    }

    /**
     * 删除礼品卡充值记录信息
     *
     * @param id 礼品卡充值记录主键
     * @return 结果
     */
    @Override
    public int deleteGiftCardRechargeRecordById(Long id) {
        GiftCardRechargeRecord giftCardRechargeRecord = giftCardRechargeRecordMapper.selectGiftCardRechargeRecordById(id);
        int row = giftCardRechargeRecordMapper.deleteGiftCardRechargeRecordById(id);
        if (row > 0) {
            GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
            rechargeRecord.setAccount(giftCardRechargeRecord.getAccount());
            AccountRecharge accountRecharge = accountRechargeManager.selectAccountRechargeByAccountName(giftCardRechargeRecord.getAccount());
            List<GiftCardRechargeRecord> list = this.giftCardRechargeRecordMapper.selectGiftCardRechargeRecordList(rechargeRecord);
            Double buyAmt = 0.00;
            Double rechargeAmt = 0.00;
            for (GiftCardRechargeRecord record : list) {
                buyAmt += record.getBuyAmt().doubleValue();
                rechargeAmt += record.getFaceValue().doubleValue();
            }
            accountRecharge.setBuyAmt(buyAmt);
            accountRecharge.setRechargeAmt(rechargeAmt);
            accountRechargeManager.updateAccountRecharge(accountRecharge);
        }
        return row;
    }
}
