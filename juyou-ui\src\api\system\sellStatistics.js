
import request from '@/utils/request'

// 新增sellcard
export function getSellChatgroupList(data) {
  return request({
    url: '/system/sellStatistics/getSellChatgroupList',
    method: 'get',
    params: data
  })
}

// 新增sellcard
export function sellStatisticsGetUnsoldTotal(data={}) {
  return request({
    url: '/system/sellStatistics/getUnsoldTotal',
    method: 'get',
    params: data
  })
}

// 获取作废统计列表
export function sellStatisticsGetCancelList(data={}) {
  return request({
    url: '/system/sellStatistics/getCancelList',
    method: 'get',
    params: data
  })
}

// 充值账号统计详情
export function getRechargeCancelAmtDetailList(data={}) {
  return request({
    url: '/system/sellStatistics/getRechargeCancelAmtDetailList',
    method: 'get',
    params: data
  })
}

// 出售账号作废统计明细
export function getSellCancelAmtDetailList(data={}) {
  return request({
    url: '/system/sellStatistics/getSellCancelAmtDetailList',
    method: 'get',
    params: data
  })
}

// 出售群统计明细列表
export function getSellGroupDetailList(data={}) {
  return request({
    url: '/system/sellStatistics/getSellGroupDetailList',
    method: 'get',
    params: data
  })
}

// 礼品卡充值统计
export function getGiftCardRechargeStatisticsList(data={}) {
  return request({
    url: '/system/sellStatistics/getGiftCardRechargeStatisticsList',
    method: 'get',
    params: data
  })
}

// 礼品卡充值来源群统计详情列表
export function getGiftCardRechargeStatisticsDetailList(data={}) {
  return request({
    url: '/system/sellStatistics/getGiftCardRechargeStatisticsDetailList',
    method: 'get',
    params: data
  })
}

