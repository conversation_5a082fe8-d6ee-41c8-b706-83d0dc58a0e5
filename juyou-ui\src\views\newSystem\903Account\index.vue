<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="账号：" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="使用状态：" prop="isUse">
        <el-select v-model="queryParams.isUse" placeholder="请选择使用状态" clearable>
          <el-option
            v-for="dict in [{
              value:'1',
              label:'已使用',
            },{
              value:'0',
              label:'未使用',
            }]"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="出售时间：" prop="sellTime">
        <el-date-picker v-model="sellTime" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="使用时间" prop="status">
        <el-date-picker v-model="useTime" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      <el-button @click="useItem()" style="float: right;margin-right: 10px" type="primary">批量使用</el-button>
    </el-row>
    <el-table @selection-change="handleSelectionChange" v-loading="loading" :data="dataList" :summary-method="getSummaries" show-summary>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" :index="table_index" align="center" type="index" width="50"></el-table-column>
      <el-table-column label="使用状态" align="center" prop="isUse" width="70">
        <template slot-scope="scope">
          {{scope.row.isUse === '1'?'已使用':'未使用'}}
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">
            {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
          scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
        scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="来源群" align="center" prop="sourceGroup" /> -->
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <el-table-column label="出售价格" align="center" prop="sellPrice"/>
      <el-table-column label="出售金额" align="center" prop="sellAmt"/>
      <el-table-column label="ID余额" align="center" prop="cardBalance"/>
      <el-table-column label="出售时间" align="center" prop="sellTime"/>
      <el-table-column label="使用时间" align="center" prop="useTime"/>
      <el-table-column label="操作" width="100px" align="center" prop="accountZone">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isUse !== '1'" size="mini" type="text" @click="useItem(scope.row)">使用
          </el-button>
          <el-button v-if="scope.row.isUse === '1'" size="mini" type="text" @click="handleDetail(scope.row)">查看
          </el-button>
          <el-button v-if="scope.row.isUse === '1'" size="mini" type="text" @click="restoreItem(scope.row)">恢复
          </el-button>
        </template>
      </el-table-column>
    </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

  </div>
</template>

<script>
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getSellAnchorList, sellAnchorBatch, sellAnchorRecovery } from '@/api/newSystem/accountRechargeReport'
import { changeJobStatus } from '@/api/monitor/job'

export default {
  name: "903Account",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      sellTime:[],
      useTime:[],
      ids:[],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        pageNum:1,
        pageSize:100,
        accountName: undefined,
        isUse: undefined
      },
      total:0,
      // 表单参数
      form: {},
    };
  },
  activated() {
    this.getList();
  },
  created() {
    this.getList();
  },
  methods: {
    handleDetail(e){
      this.$router.push('/sellCardDetail/sellCardDetail/'+e.rechargeId+'?type=detail')
    },
    async useItem(e){
      this.$modal.loading("请稍候...");
      let arr
      if (e){
        arr = [e.rechargeId]
        this.$modal.confirm('账号['+e.accountName+']，请确认是否使用？').then(function() {
          return sellAnchorBatch({
            rechargeIds:arr
          })
        }).then(() => {
          this.$modal.msgSuccess('使用成功')
          this.$modal.closeLoading()
          this.getList()
        }).catch(()=> {
          this.$modal.closeLoading()
        });
      }else {
        arr = this.ids
        this.$modal.confirm('请确实是否批量使用已选中的账号？').then(function() {
          return sellAnchorBatch({
            rechargeIds:arr
          })
        }).then(() => {
          this.$modal.msgSuccess('使用成功')
          this.$modal.closeLoading()
          this.getList()
        }).catch(()=> {
          this.$modal.closeLoading()

        });
      }
    },
    async restoreItem(e){
      this.$modal.loading("请稍候...");
      this.$modal.confirm('账号['+e.accountName+']，请确认是否恢复为待使用？').then(function() {
        return sellAnchorRecovery(e.rechargeId)
      }).then(() => {
        this.$modal.msgSuccess('恢复成功')
        this.$modal.closeLoading()
        this.getList()
      }).catch(function() {
        this.$modal.closeLoading()
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.rechargeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      console.log(this.ids)
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      getSellAnchorList(this.dateChange(this.queryParams,this.sellTime,this.useTime)).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //时间转换
    dateChange(params, sellTime, useTime) {
      let search = params;
      search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
      sellTime = Array.isArray(sellTime) ? sellTime : [];
      useTime = Array.isArray(useTime) ? useTime : [];
        search['startSellTime'] = sellTime[0];
        search['endSellTime'] = sellTime[1];
        search['useStartTime'] = useTime[0];
        search['useEndTime'] = useTime[1];
      return search;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0"
      };
      this.useTime = []
      this.sellTime = []
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    table_index(index) {
      return (+this.queryParams.pageNum - 1) * +this.queryParams.pageSize + index + 1
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.useTime = []
      this.sellTime = []
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /**一键复制账号和密码*/
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    getSummaries(param) {
      const notTotals = [1, 2, 3, 4, 5, 6,9,10] //不需要小计的列数组
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '小计';
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += ' ';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
  }
};
</script>
