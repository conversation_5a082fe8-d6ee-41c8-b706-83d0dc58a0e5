package com.juyou.system.service.impl;

import com.juyou.common.annotation.DataScope;
import com.juyou.common.utils.SecurityUtils;
import com.juyou.system.domain.vo.RecordOfChargingCardsVo;
import com.juyou.system.mapper.GiftCardRechargeRecordMapper;
import com.juyou.system.params.RecordOfChargingCardsParam;
import com.juyou.system.service.IRecordOfChargingCardsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
@Service
public class RecordOfChargingCardsServiceImpl implements IRecordOfChargingCardsService {

    @Autowired
    private GiftCardRechargeRecordMapper giftCardRechargeRecordMapper;
    @Override
   // @DataScope(deptAlias = "su", userAlias = "a")
    public List<RecordOfChargingCardsVo> getRecordOfChargingCards(RecordOfChargingCardsParam param) {
        param.setUserID(SecurityUtils.getUsername());

        // 获取第一个列表
        List<RecordOfChargingCardsVo> list1 = giftCardRechargeRecordMapper.getRecordOfChargingCardsAll(param);
//        // 获取第二个列表
//        List<RecordOfChargingCardsVo> list2 = giftCardRechargeRecordMapper.getRecordOfChargingCards(param);
//
//        // 合并两个列表
//        List<RecordOfChargingCardsVo> combinedList = new ArrayList<>();
//
//        // 添加第一个列表的所有元素（空指针检查）
//        if (list1 != null) {
//            combinedList.addAll(list1);
//        }
//
//        // 添加第二个列表的所有元素（空指针检查）
//        if (list2 != null) {
//            combinedList.addAll(list2);
//        }

        return list1;
    }
}
