package com.juyou.system.domain.vo;

import com.juyou.system.domain.AccountRecharge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账号充值-未生效列表-list-vo
 */
@Data
@ApiModel("AccountRechargeNotEffectiveListVo")
public class AccountRechargeNotEffectiveListVo extends AccountRecharge {

    private static final long serialVersionUID = 4522464115408530030L;

    @ApiModelProperty("剩余分钟")
    private BigDecimal surplusMinutesNum;

    @ApiModelProperty("一级充值人")
    private String primaryChargerName;

    @ApiModelProperty("二级充值人")
    private String secondaryChargerName;
}
