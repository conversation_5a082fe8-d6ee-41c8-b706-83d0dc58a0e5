package com.juyou.system.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.juyou.system.domain.GiftCardRechargeRecord;
import com.juyou.system.params.GiftCardRechargeRecordPageParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class GiftCardRechargeRecordServiceImplTest {

    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;

    @Test
    public void selectGiftCardRechargeRecordPage(){
        GiftCardRechargeRecordPageParam param = new GiftCardRechargeRecordPageParam();
        param.setAccount("veckouiyan");
        param.setGiftCard("FMWP");
        param.setStartExecutionTime(DateUtil.parseDate("2023-09-9 14:36:32"));
        param.setEndExecutionTime(DateUtil.parseDate("2023-09-09 14:36:32"));
        param.setOperator("admin");
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordPage(param);
        log.info("list:{}", JSONUtil.toJsonPrettyStr(list));
    }


}
