<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="账号：" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出售状态：" prop="status">
        <el-select v-model="queryParams.status" filterable placeholder="请选择充值状态">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in sellStatus"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="充值阶段：" prop="chargeStage">
        <el-select v-model="queryParams.chargeStage" filterable placeholder="请选择充值阶段">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in rechargePhase"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出售群：" prop="sellChatgroupName">
        <el-select clearable v-model="queryParams.sellChatgroupName" filterable placeholder="请选择出售群">
          <el-option
            label="全部"
            value="">
          </el-option>
          <el-option
            v-for="item in sellGroups"
            :key="item.groupNumber"
            :label="item.groupNumber"
            :value="item.groupNumber">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出售时间：" prop="sellTime">
        <el-date-picker
          v-model="sellTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="售卖人-客户：" prop="custName">
        <el-input
          v-model="queryParams.custName"
          placeholder="请输入售卖人-客户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in zonrList"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="核对状态：" prop="accountZone">
        <el-select v-model="queryParams.writeOffStatus" filterable placeholder="请选择核对状态">
          <el-option key="all" :label="'全部'" :value="null"></el-option>
          <el-option
            v-for="(item,index) in writeOffStatus"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="float: right">
      <el-button :disabled="!ids.length" type="primary" @click="downloadData">导出</el-button>
      <el-button :disabled="!ids.length" type="primary" @click="batchCheck">批量核对</el-button>
    </div>
    <el-table :row-class-name="tableRowClassName" @selection-change="handleSelectionChange" v-loading="loading" :data="recordList" :summary-method="getSummaries" show-summary>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" :index="table_index" align="center" type="index" width="50"></el-table-column>
      <el-table-column label="核对状态" align="center" prop="accountName" width="70">
        <template slot-scope="scope">
          {{scope.row.writeOffStatus === '1'?'未核对':'已核对'}}
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="accountName">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.accountName}----${scope.row.accountPwd}`)">
            {{scope.row.accountName?scope.row.accountName.substr(0,4) + '***' +
          scope.row.accountName.substr(scope.row.accountName.length-4,scope.row.accountName.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
        scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="区域" align="center" prop="accountZone" />
      <el-table-column label="充值阶段" align="center" prop="chargeStage">
        <template slot-scope="scope">
          {{scope.row.chargeStage === '1'?'一级充值':'二级充值'}}
        </template>

      </el-table-column>
      <el-table-column label="出售状态" align="center" prop="status">
        <template slot-scope="scope">
          {{formatStatus(scope.row.status)}}
        </template>
      </el-table-column>
      <el-table-column label="领取人" align="center" prop="receiveUser"/>
      <el-table-column label="出售群" align="center" prop="sellChatgroupName" />
      <el-table-column label="出售价格" align="center" prop="sellPrice" />
      <el-table-column label="出售金额" align="center" prop="sellAmt" />
      <el-table-column label="ID余额" align="center" prop="cardBalance" />
      <el-table-column label="成本金额(CNY)" align="center" prop="buyAmt" />
      <el-table-column label="出售时间" align="center" prop="sellTime" />

      <el-table-column fixed="right" label="操作" align="center" width="145" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status !== '4'" size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status === '4'" size="mini" type="text" @click="handleDetail(scope.row)">查看</el-button>
          <el-button v-if="scope.row.status === '4'" size="mini" type="text" @click="handleReinstate(scope.row)">恢复</el-button>
          <el-button v-if="scope.row.status === '3'" size="mini" type="text" @click="handleDelete(scope.row)">作废</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"

    />
    <!-- 作废弹窗 -->
    <el-dialog title="作废｜作废后无法再出售，请确认是否作废？" :visible.sync="nullifyVisible"
               :close-on-click-modal="false"
               :close-on-press-escape="false" width="600px" append-to-body>
      <el-form ref="nullifyForm" :model="nullifyForm" :rules="nullifyFormRules" label-width="100px"
               label-position="right">

        <el-form-item label="账号：" prop="accountName">
          <el-input type="text" v-model="nullifyForm.accountName" :disabled="true"/>
        </el-form-item>

        <el-form-item label="作废原因：" prop="cancelReason">
          <el-input type="text" placeholder="请输入作废原因" v-model="nullifyForm.cancelReason" />
        </el-form-item>

        <el-form-item label="作废图片:">
          <image-upload v-model="nullifyForm.cancelImgs" :limit="3" :uploadImgUrl="uploadImgUrl" :isShowTip="false"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitNullifyForm">作废</el-button>
        <el-button @click="nullifyCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  accountSellSoldReportBatchWriteOff,
  chargingInquiryList,
  recordList, sellCardQuery, sellcardRestore
} from '@/api/newSystem/rechargeCard'
import {getToken} from "@/utils/auth";
import {getDicts} from "@/api/newSystem/dict/data";
import { listUser } from '@/api/newSystem/user'
import { getSourceGroupsList, sellingGroupsList } from '@/api/newSystem/notEffective'
import { sellAnchorRecovery } from '@/api/newSystem/accountRechargeReport'
import {cancelSellcard} from "@/api/newSystem/sellcard";

export default {
  name: "SellInquiries",
  data() {
    return {
      nullifyVisible:false,
      sellGroups:[],
      ids:[],
      sellStatus:[],
      rechargePhase:[],
      writeOffStatus:[],
      uploadaccFilePath: process.env.VUE_APP_BASE_API + "/newSystem/record/importData",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      //充值记录列表列表
      recordList: [],
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
      },
      zonrList: [],
      sellTime:[],
      nullifyFormRules:{},
      nullifyForm:{}
    };
  },
  activated() {
    if (this.$route.query.group && this.$route.query.group!=='undefined'){
      this.queryParams.sellChatgroupName = this.$route.query.group
    }
    if (this.$route.query.startTime && this.$route.query.endTime){
      this.sellTime = [this.$route.query.startTime,this.$route.query.endTime]
    }
    this.getList()
  },
  created() {
    if (this.$route.query.group && this.$route.query.group!=='undefined'){
      this.queryParams.sellChatgroupName = this.$route.query.group
    }
    if (this.$route.query.startTime && this.$route.query.endTime){
      this.sellTime = [this.$route.query.startTime,this.$route.query.endTime]
    }else {
      this.theLastSevenDays()
    }
    this.into();
  },
  methods: {
    /**作废按钮事件*/
    handleDelete(row) {
      this.nullifyVisible = true;
      this.nullifyForm = {
        ...row
      };
    },
    /**作废提交按钮事件*/
    submitNullifyForm() {
      this.$refs["nullifyForm"].validate(async (valid) => {
        if (valid) {
          const request = {
            ...this.nullifyForm,
          };
          const newCance_res = await cancelSellcard(request);
          if (newCance_res.code === 200) {
            this.getList()
            this.nullifyVisible = false;
            this.$modal.msgSuccess("作废成功");
          }
        }
      });
    },
    // 一键复制账号
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
    theLastSevenDays() {
      // 七天前
      const date = new Date();
      const enddate = new Date(date);
      enddate.setDate(date.getDate() - 7);
      const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
      // 今天
      const today = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.sellTime = [`${yesterday} 00:00:00`, `${today} 23:59:59`]
    },
    table_index(index) {
      return (+this.queryParams.pageNum - 1) * +this.queryParams.pageSize + index + 1
    },
    async batchCheck(){
      if (this.ids.length){
        this.$modal.loading('核对中')
        try{
          await accountSellSoldReportBatchWriteOff({
            rechargeIdList:this.ids
          })
        }catch (e) {
          this.$modal.closeLoading()
          return
        }
        this.$modal.closeLoading()
        this.$modal.msgSuccess('核对成功')
        this.getList()
      }

    },
    handleDetail(e){
      this.$router.push('/sellCardDetail/sellCardDetail/'+e.rechargeId+'?type=detail')
    },
    async handleReinstate(e){
      this.$modal.loading('恢复中')
      this.$modal.confirm('账号['+e.accountName+']，请确认是否恢复为待出售？').then(function() {
        return sellcardRestore({
          rechargeId:e.rechargeId
        })
      }).then(() => {
        this.$modal.msgSuccess('恢复成功')
        this.$modal.closeLoading()
        this.getList()
      }).catch(function() {
        this.$modal.closeLoading()
      });
    },
    into() {
      getDicts("account_zone").then(response => {
        this.zonrList = response.data;
      })
      getDicts("sell_status").then(response => {
        this.sellStatus = response.data;
      })
      getDicts("recharge_phase").then(response => {
        this.rechargePhase = response.data;
      })
      getDicts("writeOff_status").then(response => {
        this.writeOffStatus = response.data;
      })
      this.getList();//获取未生效状态的账号
      sellingGroupsList().then(res=>{
         this.sellGroups = res.rows
       })

    },
    handleUpdate(e){
      this.$router.push('/sellCardDetail/sellCardDetail/'+e.rechargeId+'?type=edit')
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.rechargeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      console.log(this.ids)
    },
    /**转译售卡状态 */
    formatStatus(status) {
      let data = ''
      switch (status) {
        case '1':
          data = '未生效';
          break;
        case '2':
          data = '待出售';
          break;
        case '3':
          data = '已出售';
          break;
        case '4':
          data = '作废';
          break;
      }
      return data
    },
    /** 查询未生效账号列表 */
    async getList() {
      try {
        this.loading = true;
        let request
        if (this.sellTime && this.sellTime.length){
          request = {
            endSellTime:this.sellTime[1],
            startSellTime:this.sellTime[0],
            ...this.queryParams,
          };
        }else {
          request = {
            ...this.queryParams,
          };
        }

        const recordList_res = await sellCardQuery(request);
        if (recordList_res.code === 200) {
          this.recordList = recordList_res.rows;
          this.total = recordList_res.total;
          this.loading = false;
        }
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.sellTime = []
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 100,
        accountName: null,
        accountZone: null,
        createBy: null,
        createTime: null,
        exceed: false,
        operator: null,
        params: null,
        remark: null,
        searchValue: null,
        updateBy: null,
        updateTime: null,
      }
      this.handleQuery();
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      if (res.code === 200) {
        this.$modal.msgSuccess(res.msg || '导入成功！');
        this.getList();//重新刷新列表
      } else {
        this.$modal.msgError(res.msg || '导入失败');
      }
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("导入失败，请重试");
      this.$modal.closeLoading();
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用,function(file, fileList)
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.info(this.fileList);
    },
    downloadData(){
      this.download('/new/system/sellcard/batchExport', {
        ids:this.ids
      }, `sell_${new Date().getTime()}.xlsx`)
    },
    tableRowClassName({ row }) {
      if (row.hasEdit == 'Y') {
        return "red-row";
      }
      return "";
    },
    getSummaries(param) {
      const notTotals = [1, 2, 3, 4, 5,6,7,8,9,13] //不需要小计的列数组
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '小计';
          return;
        }
        if (notTotals.includes(index)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return (Number(prev) + Number(curr)).toFixed(2);
            } else {
              return prev;
            }
          }, 0);
          sums[index] += ' ';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
  }
};
</script>
<style lang="scss">
.el-table .red-row {
  background: #ec808d;
}
</style>
