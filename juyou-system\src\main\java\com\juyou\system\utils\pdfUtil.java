package com.juyou.system.utils;

import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfStream;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.UnitValue;
import com.juyou.system.domain.AccountSell;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class pdfUtil {

    public static void exportToPDF(HttpServletResponse response, List<AccountSell> list,String fileUploadPath) throws IOException {
        try {
            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=example.pdf");

            // 创建 PDF 文档
            PdfWriter writer = new PdfWriter(response.getOutputStream());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 定义表格的列数
            Table table = new Table(UnitValue.createPercentArray(new float[]{20, 20, 60}));
            table.setWidth(UnitValue.createPercentValue(100));

            // 添加表头
            List<String> headers = Arrays.asList("账号", "密码", "备用码");
            headers.forEach(header -> {
                table.addHeaderCell(header);
            });

            // 添加表格数据和图片
            for (AccountSell accountSell : list) {
                table.addCell(accountSell.getAccountName());
                table.addCell(accountSell.getAccountPwd());

                // 如果备用码图片路径存在，则添加备用码图片
                if (accountSell.getSpareCode() != null && !accountSell.getSpareCode().isEmpty()) {
                    try {
                        // 创建备用码图片对象
                        ImageData spareCodeImageData = ImageDataFactory.create(fileUploadPath + accountSell.getSpareCode());
                        // 创建 Image 对象
                        Image spareCodeImage = new Image(spareCodeImageData);

                        // 设置图片宽度为200f，高度会按比例缩放
                        spareCodeImage.scaleToFit(200f, 150f);

                        // 将 Image 对象添加到表格单元格中
                        table.addCell(new Cell().add(spareCodeImage));
                    } catch (IOException e) {
                        // 处理备用码图片读取错误的异常
                        System.err.println("Error loading spare code image: " + e.getMessage());
                        table.addCell(""); // 添加空单元格以保持表格结构
                    }
                } else {
                    // 如果备用码图片路径不存在，则在表格中添加空白单元格
                    table.addCell("");
                }
            }

            // 将表格添加到文档
            document.add(table);

            // 关闭文档
            document.close();
            System.out.println("PDF created.");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
