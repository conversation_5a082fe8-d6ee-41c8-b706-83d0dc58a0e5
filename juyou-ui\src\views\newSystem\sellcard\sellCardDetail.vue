<template>
  <el-main>
    <el-radio-group style="margin-bottom: 20px" v-model="selectType">
      <el-radio-button label="0">出售详情</el-radio-button>
      <el-radio-button label="2">操作日志</el-radio-button>
    </el-radio-group>
    <div v-if="selectType === '0'">
      <el-card style="margin-bottom: 20px" header="充值信息"  v-if="form.accountRechargeDetailVo">
        <el-row type="flex" class="row-bg" justify="space-between">
          <el-col :span="6">
            <div class="label">账号：</div>
            <div class="value">{{form.accountRechargeDetailVo.accountName}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">密码：</div>
            <div class="value">{{form.accountRechargeDetailVo.accountPwd}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">区域：</div>
            <div class="value">{{form.accountRechargeDetailVo.accountZone}}</div>
          </el-col>
        </el-row>
        <el-row type="flex" class="row-bg" justify="space-between">
          <el-col :span="6">
            <div class="label">ID余额：</div>
            <div class="value">{{form.accountRechargeDetailVo.rechargeAmt}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">成本金额：</div>
            <div class="value">{{form.accountSellReportDetailVo.buyAmt}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">一级充值人：</div>
            <div class="value">{{form.accountRechargeDetailVo.primaryCharger}}</div>
          </el-col>
        </el-row>
        <el-row type="flex" class="row-bg" justify="space-between">
          <el-col :span="6">
            <div class="label">二级充值人：</div>
            <div class="value">{{form.accountRechargeDetailVo.secondaryCharger}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">ID归属业务员：</div>
            <div class="value">{{form.accountRechargeDetailVo.idUser}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">充值完成时间：</div>
            <div class="value">{{form.accountRechargeDetailVo.doneTime}}</div>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-bottom: 20px" header="出售信息" v-if="form.accountSellReportDetailVo">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-row type="flex" class="row-bg" justify="space-between">
          <el-col :span="6">
            <el-form-item label="出售群：" prop="accountSellReportDetailVo.sellChatgroupName">
              <div v-if="type==='detail'">{{form.accountSellReportDetailVo.sellChatgroupName}}</div>
              <el-select size="mini" v-else :disabled="selectSellType === '2'" v-model="form.accountSellReportDetailVo.sellChatgroupName" placeholder="请选择出售群">
                <el-option
                  v-for="item in groupList"
                  :key="item.groupNumber"
                  :label="item.groupNumber"
                  :value="item.groupNumber">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="售卖人：" prop="accountSellReportDetailVo.custName">
              <div v-if="type==='detail'">{{form.accountSellReportDetailVo.custName}}</div>
              <el-input v-else :disabled="selectSellType === '2'" size="mini" v-model="form.accountSellReportDetailVo.custName" placeholder="请输入售卖人"></el-input>
            </el-form-item>

          </el-col>
          <el-col :span="6">
            <div v-if="type==='edit'">
              <div class="label">出售对象：</div>
                <el-radio-group @change="selectSellTypeFn" v-model="selectSellType">
                  <el-radio label="1">外部出卡群</el-radio>
                  <el-radio label="2">内部903</el-radio>
                </el-radio-group>
            </div>

          </el-col>
        </el-row>



        <el-row type="flex" class="row-bg" justify="space-between">
          <el-col :span="6">
            <el-form-item label="出售价：" prop="accountSellReportDetailVo.sellPrice">
              <div v-if="type==='detail'" class="value">{{form.accountSellReportDetailVo.sellPrice}}</div>
              <el-input @input="inputSellPrice" v-else size="mini" v-model="form.accountSellReportDetailVo.sellPrice" placeholder="请输入出售价"></el-input>
            </el-form-item>

          </el-col>
          <el-col :span="6">
            <div class="label">出售金额：</div>
            <div class="value">{{form.accountSellReportDetailVo.sellAmt}}</div>
          </el-col>
          <el-col :span="6">
            <div class="label">出售时间：</div>
            <div class="value">{{form.accountSellReportDetailVo.sellTime}}</div>
          </el-col>
        </el-row>

          <el-row type="flex" class="row-bg" justify="space-between" v-if="form.accountSellReportDetailVo.cancelReason != null">
            <el-col :span="6">
              <el-form-item label="作废原因：">
                <div  class="value">{{form.accountSellReportDetailVo.cancelReason}}</div>

              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div class="label" style="float: left;line-height: 36px">作废图片：</div>
              <image-upload disabled v-if="form.accountSellReportDetailVo.cancelImgs" v-model="form.accountSellReportDetailVo.cancelImgs"
                            :isShowTip="false"/>
            </el-col>
            <el-col :span="6">

            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card style="margin-bottom: 20px" header="卡密列表">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            prop="giftCard"
            label="卡密"
            width="180">
          </el-table-column>
          <el-table-column
            prop="faceValue"
            label="面值"
            width="180">
          </el-table-column>
          <el-table-column
            prop="buyPrice"
            label="汇率(价格)">
          </el-table-column>
          <el-table-column
            prop="sourceGroup"
            label="汇来源群">
          </el-table-column>
          <el-table-column
            prop="address"
            label="是否质押30分钟">
            <template slot-scope="{row}">
              {{row.pledgeThirtyMinutes === '1' ? '是':'否'}}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div style="float: right" v-if="type === 'edit'">
        <el-button style="margin-top: 10px" type="primary" @click="submitData()">保存</el-button>
        <el-button @click="copyToClip(`${form.accountRechargeDetailVo.accountName}----${form.accountRechargeDetailVo.accountPwd}`)">一键复制账号</el-button>
      </div>
    </div>

    <div v-else-if="selectType === '2'">
      <operation-logs></operation-logs>
    </div>
  </el-main>
</template>

<script>

import { getTheSaleDetails } from '@/api/newSystem/sellcard'
import OperationLogs from '@/views/newSystem/accRecharge/operationLogs.vue'
import { chargingInquiryEdit } from '@/api/newSystem/accRecharge'
import { saveSellCard } from '@/api/newSystem/rechargeCard'
import { sellingGroupsList } from '@/api/newSystem/notEffective'

export default {
  name: "sellCardDetail",
  components: { OperationLogs },
  data() {
    return {
      rules:{
        'accountSellReportDetailVo.sellPrice': [
          { required: true, message: '请输入出售价', trigger: 'change' }
        ],
        'accountSellReportDetailVo.custName': [
          { required: true, message: '请输入售卖人', trigger: 'change' }
        ],
        'accountSellReportDetailVo.sellChatgroupName': [
          { required: true, message: '请选择出售群', trigger: 'change' }
        ],
      },

      groupList:[],
      selectSellType:'1',
      form:'',
      tableData:[],
      selectType:'0',
      type:'detail',
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    sellingGroupsList().then(e=>{
      this.groupList = e.rows
    })
    this.type = this.$route.query.type
    this.getDetail()
  },
  methods: {
    inputSellPrice(e){
      this.form.accountSellReportDetailVo.sellAmt = +e *  +this.form.accountRechargeDetailVo.rechargeAmt
    },
    selectSellTypeFn(e){
      if (e === '1'){
        this.form.accountSellReportDetailVo.custName = ''
        this.form.accountSellReportDetailVo.sellChatgroupName = ''
      }else {
        this.form.accountSellReportDetailVo.custName = '903'
        this.form.accountSellReportDetailVo.sellChatgroupName = '903'
      }
      console.log(this.form.accountSellReportDetailVo)
    },
    async getDetail(){
       const res = await getTheSaleDetails(this.$route.params.id)
      this.tableData = res.data.accountRechargeDetailVo.giftCardRechargeRecordList
      this.form = res.data
      if (this.form.accountSellReportDetailVo.custName == 903 && this.form.accountSellReportDetailVo.sellChatgroupName == 903){
        this.selectSellType = '2'
      }
    },
    async submitData(){
      this.$refs['ruleForm'].validate(async(valid) => {
        if (valid) {
          this.$modal.loading('修改中')
          try{
            await saveSellCard(this.form.accountSellReportDetailVo)
          }catch (e) {
            this.$modal.closeLoading()
            return
          }
          this.$modal.closeLoading()
          this.$modal.msgSuccess('修改成功')
          this.$router.go(-1)
          this.$tab.closePage()
        } else {

          return false;
        }
      })

    },
    /**一键复制账号和密码*/
    copyToClip(content) {
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$modal.msgSuccess("已复制到黏贴版");
    },
  },
};
</script>
<style lang="scss" scoped>
.label{
  font-size:14px;
  font-weight: 700;
  display: inline-block;
  text-align: right;
  margin-right: 20px;
}
.value{
  display: inline-block;
  margin-bottom: 20px;
}
::v-deep .el-upload--picture-card{
  display: none;
}
::v-deep .el-upload-list__item{
  width: 60px;
  height: 60px;
}
::v-deep .el-upload-list__item-delete{
  display: none !important;
}
</style>
