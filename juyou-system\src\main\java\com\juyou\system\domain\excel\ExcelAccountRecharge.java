package com.juyou.system.domain.excel;

import com.juyou.common.annotation.Excel;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 账号充值对象 excel导入类
 * <AUTHOR>
 * @date 2023-09-07
 */
@Data
public class ExcelAccountRecharge implements Serializable {


    private static final long serialVersionUID = 3354469214623264861L;

    @Excel(name = "账号")
    private String accountName;

    @Excel(name = "密码")
    private String accountPwd;

    @Excel(name = "图片",height=55, cellType = Excel.ColumnType.IMAGE)
    private String img;

    @Excel(name = "钱包区域")
    private String walletArea ;
}
