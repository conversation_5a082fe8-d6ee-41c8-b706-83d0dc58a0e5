package com.juyou.web.task;

import cn.hutool.core.date.DateUtil;
import com.juyou.system.service.IAccountSellService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 账号出售定时任务 -- 需求变化废弃-不需要已生效的中间状态
 */
@Slf4j
// 账号出售定时任务弃用,
// 何总说:【IDS】新增需求 所有的充值完成，对于出售-都无需等待；
@Component("accountSellTask")
public class AccountSellTask {

    @Autowired
    private IAccountSellService iAccountSellService;

    /**
     * 更新账号出售完成时间分钟数(账号在未生效持续的时间,单位分钟)
     * 定时任务：5分钟跑一次
     */
    public void updateStatus() {
//        log.info("start----------更新账号出售完成时间分钟数(账号在未生效持续的时间,单位分钟)：运行时间:{}", DateUtil.formatDateTime(new Date()));
//
//        int i = this.iAccountSellService.updateNotEffectiveCompletedTimeMinutesNum();
//
//        log.info("end----------更新账号出售完成时间分钟数(账号在未生效持续的时间,单位分钟)：更新条数为:{}", i);
    }

}
