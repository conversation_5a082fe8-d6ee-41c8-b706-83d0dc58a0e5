<template>
	<div class="lock-bg">
		<div class="loader">
			<div class="container">
				<div class="coffee-header">
					<div class="coffee-header__buttons coffee-header__button-one"></div>
					<div class="coffee-header__buttons coffee-header__button-two"></div>
					<div class="coffee-header__display"></div>
					<div class="coffee-header__details"></div>
				</div>
				<div class="coffee-medium">
					<div class="coffe-medium__exit"></div>
					<div class="coffee-medium__arm"></div>
					<div class="coffee-medium__liquid"></div>
					<div class="coffee-medium__smoke coffee-medium__smoke-one"></div>
					<div class="coffee-medium__smoke coffee-medium__smoke-two"></div>
					<div class="coffee-medium__smoke coffee-medium__smoke-three"></div>
					<div class="coffee-medium__smoke coffee-medium__smoke-for"></div>
					<div class="coffee-medium__cup"></div>
				</div>
				<div class="coffee-footer"></div>
			</div>
		</div>
    <el-input class="input" size="large" v-model="input" placeholder="请输入谷歌验证码"></el-input>
    <button class="button"  @click="submit">
      <div class="outline"></div>
      <div class="state state--default">

        <p style="font-size: 22px">
          <span style="--i:0;margin: 0 1px">继</span>
          <span style="--i:1;margin: 0 1px">续</span>
          <span style="--i:2;margin: 0 1px">工</span>
          <span style="--i:3;margin: 0 1px">作</span>
        </p>
      </div>
      <div class="state state--sent">
        <p style="font-size: 22px">
          <span style="--i:5;margin: 0 1px">工</span>
          <span style="--i:6;margin: 0 1px">作</span>
          <span style="--i:7;margin: 0 1px">开</span>
          <span style="--i:8;margin: 0 1px">心</span>
        </p>
      </div>
    </button>

  </div>
</template>
<script>

import { updateLockStatus, verifyTheGoogleCode } from '@/api/newSystem/user'

export default {
	name: "index",
  data(){
    return {
      input:''
    }
  },
  methods:{
    submit(){
      if (!this.input) return this.$modal.msgError('谷歌验证码不能为空')
      verifyTheGoogleCode(this.input).then(e=>{
        updateLockStatus({
          userId:this.$store.state.user.userInfo.userId,
          lockStatus:'N',
        }).then(()=>{
          this.$store.dispatch("GetInfo")
        })

      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner:focus{
  border-color:#41bdad;
}
.lock-bg{
	width: 100%;
	height: 100%;
	background-color: #e8e8e8;
}
.input{
    width: 300px;
    position: absolute;
    top: calc(50% + 70px);
    left: calc(50% - 150px);
}
.loader {
}
.button{
  position: absolute;
  top: calc(50% + 130px);
  left: calc(50% - 100px);
}
.container {
	width: 300px;
	height: 280px;
	position: absolute;
	top: calc(50% - 240px);
	left: calc(50% - 150px);
}

.coffee-header {
	width: 100%;
	height: 80px;
	position: absolute;
	top: 0;
	left: 0;
	background-color: #ddcfcc;
	border-radius: 10px;
}

.coffee-header__buttons {
	width: 25px;
	height: 25px;
	position: absolute;
	top: 25px;
	background-color: #282323;
	border-radius: 50%;
}

.coffee-header__buttons::after {
	content: "";
	width: 8px;
	height: 8px;
	position: absolute;
	bottom: -8px;
	left: calc(50% - 4px);
	background-color: #615e5e;
}

.coffee-header__button-one {
	left: 15px;
}

.coffee-header__button-two {
	left: 50px;
}

.coffee-header__display {
	width: 50px;
	height: 50px;
	position: absolute;
	top: calc(50% - 25px);
	left: calc(50% - 25px);
	border-radius: 50%;
	background-color: #9acfc5;
	border: 5px solid #43beae;
	box-sizing: border-box;
}

.coffee-header__details {
	width: 8px;
	height: 20px;
	position: absolute;
	top: 10px;
	right: 10px;
	background-color: #9b9091;
	box-shadow: -12px 0 0 #9b9091, -24px 0 0 #9b9091;
}

.coffee-medium {
	width: 90%;
	height: 160px;
	position: absolute;
	top: 80px;
	left: calc(50% - 45%);
	background-color: #bcb0af;
}

.coffee-medium:before {
	content: "";
	width: 90%;
	height: 100px;
	background-color: #776f6e;
	position: absolute;
	bottom: 0;
	left: calc(50% - 45%);
	border-radius: 20px 20px 0 0;
}

.coffe-medium__exit {
	width: 60px;
	height: 20px;
	position: absolute;
	top: 0;
	left: calc(50% - 30px);
	background-color: #231f20;
}

.coffe-medium__exit::before {
	content: "";
	width: 50px;
	height: 20px;
	border-radius: 0 0 50% 50%;
	position: absolute;
	bottom: -20px;
	left: calc(50% - 25px);
	background-color: #231f20;
}

.coffe-medium__exit::after {
	content: "";
	width: 10px;
	height: 10px;
	position: absolute;
	bottom: -30px;
	left: calc(50% - 5px);
	background-color: #231f20;
}

.coffee-medium__arm {
	width: 70px;
	height: 20px;
	position: absolute;
	top: 15px;
	right: 25px;
	background-color: #231f20;
}

.coffee-medium__arm::before {
	content: "";
	width: 15px;
	height: 5px;
	position: absolute;
	top: 7px;
	left: -15px;
	background-color: #9e9495;
}

.coffee-medium__cup {
	width: 80px;
	height: 47px;
	position: absolute;
	bottom: 0;
	left: calc(50% - 40px);
	background-color: #FFF;
	border-radius: 0 0 70px 70px / 0 0 110px 110px;
}

.coffee-medium__cup::after {
	content: "";
	width: 20px;
	height: 20px;
	position: absolute;
	top: 6px;
	right: -13px;
	border: 5px solid #FFF;
	border-radius: 50%;
}

@keyframes liquid {
	0% {
		height: 0px;
		opacity: 1;
	}

	5% {
		height: 0px;
		opacity: 1;
	}

	20% {
		height: 62px;
		opacity: 1;
	}

	95% {
		height: 62px;
		opacity: 1;
	}

	100% {
		height: 62px;
		opacity: 0;
	}
}

.coffee-medium__liquid {
	width: 6px;
	height: 63px;
	opacity: 0;
	position: absolute;
	top: 50px;
	left: calc(50% - 3px);
	background-color: #74372b;
	animation: liquid 4s 4s linear infinite;
}

.coffee-medium__smoke {
	width: 8px;
	height: 20px;
	position: absolute;
	border-radius: 5px;
	background-color: #b3aeae;
}

@keyframes smokeOne {
	0% {
		bottom: 20px;
		opacity: 0;
	}

	40% {
		bottom: 50px;
		opacity: .5;
	}

	80% {
		bottom: 80px;
		opacity: .3;
	}

	100% {
		bottom: 80px;
		opacity: 0;
	}
}

@keyframes smokeTwo {
	0% {
		bottom: 40px;
		opacity: 0;
	}

	40% {
		bottom: 70px;
		opacity: .5;
	}

	80% {
		bottom: 80px;
		opacity: .3;
	}

	100% {
		bottom: 80px;
		opacity: 0;
	}
}

.coffee-medium__smoke-one {
	opacity: 0;
	bottom: 50px;
	left: 102px;
	animation: smokeOne 3s 4s linear infinite;
}

.coffee-medium__smoke-two {
	opacity: 0;
	bottom: 70px;
	left: 118px;
	animation: smokeTwo 3s 5s linear infinite;
}

.coffee-medium__smoke-three {
	opacity: 0;
	bottom: 65px;
	right: 118px;
	animation: smokeTwo 3s 6s linear infinite;
}

.coffee-medium__smoke-for {
	opacity: 0;
	bottom: 50px;
	right: 102px;
	animation: smokeOne 3s 5s linear infinite;
}

.coffee-footer {
	width: 95%;
	height: 15px;
	position: absolute;
	bottom: 25px;
	left: calc(50% - 47.5%);
	background-color: #41bdad;
	border-radius: 10px;
}

.coffee-footer::after {
	content: "";
	width: 106%;
	height: 26px;
	position: absolute;
	bottom: -25px;
	left: -8px;
	background-color: #000;
}
.button {
  --primary: #41bdad;
  --neutral-1: #f7f8f7;
  --neutral-2: #e7e7e7;
  --radius: 14px;

  cursor: pointer;
  border-radius: var(--radius);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  border: none;
  box-shadow: 0 0.5px 0.5px 1px rgba(255, 255, 255, 0.2),
  0 10px 20px rgba(0, 0, 0, 0.2), 0 4px 5px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  min-width: 200px;
  padding: 20px;
  height: 68px;
  font-family: "Galano Grotesque", Poppins, Montserrat, sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 600;
}
.button:hover {
  transform: scale(1.02);
  box-shadow: 0 0 1px 2px rgba(255, 255, 255, 0.3),
  0 15px 30px rgba(0, 0, 0, 0.3), 0 10px 3px -3px rgba(0, 0, 0, 0.04);
}
.button:active {
  transform: scale(1);
  box-shadow: 0 0 1px 2px rgba(255, 255, 255, 0.3),
  0 10px 3px -3px rgba(0, 0, 0, 0.2);
}
.button:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: var(--radius);
  border: 2.5px solid transparent;
  background: linear-gradient(var(--neutral-1), var(--neutral-2)) padding-box,
  linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.45))
  border-box;
  z-index: 0;
  transition: all 0.4s ease;
}
.button:hover::after {
  transform: scale(1.05, 1.1);
  box-shadow: inset 0 -1px 3px 0 rgba(255, 255, 255, 1);
}
.button::before {
  content: "";
  inset: 7px 6px 6px 6px;
  position: absolute;
  background: linear-gradient(to top, var(--neutral-1), var(--neutral-2));
  border-radius: 30px;
  filter: blur(0.5px);
  z-index: 2;
}
.state p {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Outline */
.outline {
  position: absolute;
  border-radius: inherit;
  overflow: hidden;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.4s ease;
  inset: -2px -3.5px;
}
.outline::before {
  content: "";
  position: absolute;
  inset: -100%;
  background: conic-gradient(
          from 180deg,
          transparent 60%,
          white 80%,
          transparent 100%
  );
  animation: spin 2s linear infinite;
  animation-play-state: paused;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.button:hover .outline {
  opacity: 1;
}
.button:hover .outline::before {
  animation-play-state: running;
}

/* Letters */
.state p span {
  display: block;
  opacity: 0;
  animation: slideDown 0.8s ease forwards calc(var(--i) * 0.03s);
}
.button:hover p span {
  opacity: 1;
  animation: wave 0.5s ease forwards calc(var(--i) * 0.02s);
}
.button:focus p span {
  opacity: 1;
  animation: disapear 0.6s ease forwards calc(var(--i) * 0.03s);
}
@keyframes wave {
  30% {
    opacity: 1;
    transform: translateY(4px) translateX(0) rotate(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px) translateX(0) rotate(0);
    color: var(--primary);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(0) rotate(0);
  }
}
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-20px) translateX(5px) rotate(-90deg);
    color: var(--primary);
    filter: blur(5px);
  }
  30% {
    opacity: 1;
    transform: translateY(4px) translateX(0) rotate(0);
    filter: blur(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px) translateX(0) rotate(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(0) rotate(0);
  }
}
@keyframes disapear {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translateX(5px) translateY(20px);
    color: var(--primary);
    filter: blur(5px);
  }
}

/* Plane */
.state--default .icon svg {
  animation: land 0.6s ease forwards;
}
.button:hover .state--default .icon {
  transform: rotate(45deg) scale(1.25);
}
.button:focus .state--default svg {
  animation: takeOff 0.8s linear forwards;
}
.button:focus .state--default .icon {
  transform: rotate(0) scale(1.25);
}
@keyframes takeOff {
  0% {
    opacity: 1;
  }
  60% {
    opacity: 1;
    transform: translateX(70px) rotate(45deg) scale(2);
  }
  100% {
    opacity: 0;
    transform: translateX(160px) rotate(45deg) scale(0);
  }
}
@keyframes land {
  0% {
    transform: translateX(-60px) translateY(30px) rotate(-50deg) scale(2);
    opacity: 0;
    filter: blur(3px);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0);
    opacity: 1;
    filter: blur(0);
  }
}

/* Contrail */
.state--default .icon:before {
  content: "";
  position: absolute;
  top: 50%;
  height: 2px;
  width: 0;
  left: -5px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.5));
}
.button:focus .state--default .icon:before {
  animation: contrail 0.8s linear forwards;
}
@keyframes contrail {
  0% {
    width: 0;
    opacity: 1;
  }
  8% {
    width: 15px;
  }
  60% {
    opacity: 0.7;
    width: 80px;
  }
  100% {
    opacity: 0;
    width: 160px;
  }
}

/* States */
.state {
  z-index: 2;
  display: flex;
  position: relative;
}
.state--default span:nth-child(4) {
  margin-right: 5px;
}
.state--sent {
  display: none;
}
.state--sent svg {
  transform: scale(1.25);
  margin-right: 8px;
}
.button:focus .state--default {
  position: absolute;
}
.button:focus .state--sent {
  display: flex;
}
.button:focus .state--sent span {
  opacity: 0;
  animation: slideDown 0.8s ease forwards calc(var(--i) * 0.2s);
}
.button:focus .state--sent .icon svg {
  opacity: 0;
  animation: appear 1.2s ease forwards 0.8s;
}
@keyframes appear {
  0% {
    opacity: 0;
    transform: scale(4) rotate(-40deg);
    color: var(--primary);
    filter: blur(4px);
  }
  30% {
    opacity: 1;
    transform: scale(0.6);
    filter: blur(1px);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    filter: blur(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

</style>
