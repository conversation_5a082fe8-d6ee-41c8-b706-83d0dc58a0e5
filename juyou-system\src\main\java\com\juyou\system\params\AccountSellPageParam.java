package com.juyou.system.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 账号出售-param
 */
@Data
@ApiModel("账号出售分页对象Param")
public class AccountSellPageParam extends BaseEntity {

    private static final long serialVersionUID = 4828064220470423776L;

    @ApiModelProperty("账号")
    private String accountName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("钱包区域")
    private String walletArea;

    @ApiModelProperty("idType")
    private String idType;

    @ApiModelProperty("开始应充值金额")
    private Double startShouldAmt;

    @ApiModelProperty("结束应充值金额")
    private Double endShouldAmt;

    @ApiModelProperty("开始充值金额")
    private Double startCardBalance;

    @ApiModelProperty("结束充值金额")
    private Double endCardBalance;

    @ApiModelProperty("开始领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startReceiveTime;

    @ApiModelProperty("结束领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endReceiveTime;

    @ApiModelProperty("开始出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startSellTime;

    @ApiModelProperty("结束出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSellTime;

    @ApiModelProperty("出售群")
    private String sellChatgroupName;

    @ApiModelProperty("售卖人")
    private String custName;

    @ApiModelProperty("ids导出选中数据时使用")
    private List<Integer> ids;

    @ApiModelProperty("充值阶段")
    private Long chargeStage;

}
