package com.juyou.system.domain;

import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 报价对象 quotation
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public class Quotation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 报价所属的群号 */
    @Excel(name = "报价所属的群号")
    private String groupNumber;

    private String groupNumbers;

    /** 报价的卡种类别 */
    @Excel(name = "报价的卡种类别")
    private String cardType;

    /** 报价所适用的国家或地区 */
    @Excel(name = "报价所适用的国家或地区")
    private String country;

    /** 报价的面值范围的最小值 */
    @Excel(name = "报价的面值范围的最小值")
    private BigDecimal minFaceValue;

    /** 报价的面值范围的最大值 */
    @Excel(name = "报价的面值范围的最大值")
    private BigDecimal maxFaceValue;

    /** 报价的价格 */
    @Excel(name = "报价的价格")
    private BigDecimal price;

    private String open;

    public String getGroupNumbers() {
        return groupNumbers;
    }

    public void setGroupNumbers(String groupNumbers) {
        this.groupNumbers = groupNumbers;
    }

    public String getOpen() {
        return open;
    }

    public void setOpen(String open) {
        this.open = open;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setGroupNumber(String groupNumber)
    {
        this.groupNumber = groupNumber;
    }

    public String getGroupNumber()
    {
        return groupNumber;
    }
    public void setCardType(String cardType)
    {
        this.cardType = cardType;
    }

    public String getCardType()
    {
        return cardType;
    }
    public void setCountry(String country)
    {
        this.country = country;
    }

    public String getCountry()
    {
        return country;
    }
    public void setMinFaceValue(BigDecimal minFaceValue)
    {
        this.minFaceValue = minFaceValue;
    }

    public BigDecimal getMinFaceValue()
    {
        return minFaceValue;
    }
    public void setMaxFaceValue(BigDecimal maxFaceValue)
    {
        this.maxFaceValue = maxFaceValue;
    }

    public BigDecimal getMaxFaceValue()
    {
        return maxFaceValue;
    }
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("groupNumber", getGroupNumber())
                .append("cardType", getCardType())
                .append("country", getCountry())
                .append("minFaceValue", getMinFaceValue())
                .append("maxFaceValue", getMaxFaceValue())
                .append("price", getPrice())
                .append("remark", getRemark())
                .toString();
    }
}
