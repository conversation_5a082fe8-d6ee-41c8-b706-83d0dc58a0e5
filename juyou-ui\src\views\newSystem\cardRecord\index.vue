<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="账号：" prop="accountName">
        <el-input v-model="queryParams.accountName" placeholder="请输入账号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="礼品卡：" prop="cardNo">
        <el-input v-model="queryParams.cardNo" placeholder="请输入礼品卡" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="执行时间：" prop="executeTime">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="充值人：" prop="receiveUser">
        <el-input v-model="queryParams.receiveUser" placeholder="请输入充值人" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="来源群：" prop="sourceGroup">
        <el-select v-model="queryParams.sourceGroup" filterable placeholder="请选择来源群">
          <el-option
            v-for="item in sourceGroupList"
            :key="item.groupNumber"
            :label="item.groupNumber"
            :value="item.groupNumber">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域：" prop="accountZone">
        <el-select v-model="queryParams.accountZone" filterable placeholder="请选择区域">
          <el-option value="" label="全部"></el-option>
          <el-option
            v-for="item in zonrList"
            :key="item.dictLabel"
            :label="item.dictLabel"
            :value="item.dictLabel">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="" prop="exceed">
        <el-checkbox v-model="queryParams.exceed">超过7天</el-checkbox>
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refreshx" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table @selection-change="handleSelectionChange" v-loading="loading" :data="notEffectiveList" :summary-method="getSummaries" show-summary>
      <el-table-column label="礼品卡" align="center" prop="giftCard"/>
      <el-table-column label="区域" align="center" prop="accountZone"/>
      <el-table-column label="来源群" align="center" prop="sourceGroup"/>
      <el-table-column label="汇率" align="center" prop="exchangeRate"/>
      <el-table-column label="面值" align="center" prop="faceValue"/>
      <el-table-column label="成本金额" align="center" prop="buyAmt"/>
      <el-table-column label="领取人" align="center" prop="receiveUser"/>
      <el-table-column label="是否质押30分钟" align="center" prop="pledgeThirtyMinutes">
        <template slot-scope="{row}">
          {{row.pledgeThirtyMinutes === '0'?'否':'是'}}
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="account">
        <template slot-scope="scope">
          <el-button style="font-size: 18px;" type="text"
                     @click="copyToClip(`${scope.row.account}----${scope.row.accountPwd}`)">
            {{scope.row.account?scope.row.account.substr(0,4) + '***' +
            scope.row.account.substr(scope.row.account.length-4,scope.row.account.length):'' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center" prop="accountPwd">
        <template slot-scope="scope">
          {{scope.row.accountPwd?scope.row.accountPwd.substr(0,3) + '***' +
        scope.row.accountPwd.substr(scope.row.accountPwd.length,scope.row.accountPwd.length):'' }}
        </template>
      </el-table-column>
      <el-table-column label="执行时间" align="center" prop="executionTime"/>
      <el-table-column label="充值人" align="center" prop="receiveUser"/>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />
  </div>
</template>

<script>
import {
  getListAll, updateNotEffectiveList, getSourceGroupsList, getRecordOfChargingCardsList
} from '@/api/newSystem/notEffective'
  import {updateSellcard} from "@/api/newSystem/sellcard";
  import {getDicts} from "@/api/newSystem/dict/data";
  import Cookies from "js-cookie";

  export default {
    name: "CardRecord",
    dicts: ['two_account_sell_not_activated_effective_time'],
    data() {
      return {
        // 日期范围
        dateRange: [],
        sourceGroupList:[],
        multipleSelection: [],
        //是否超过120小时
        exceed: false,
        //操作人列表
        operatoList: [],
        //未生效账号列表
        notEffectiveList: [],
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 是否显示弹出层
        notEffectiveVisible: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          cardNo: null,
          executeTime: null,
          receiveUser: null,
          sourceGroup: null,
          accountZone: null,
          userID: null,
          startTime:null,
          endTime:null,
        },
        zonrList:{},
      };
    },
    created() {
      if (this.$route.query.group && this.$route.query.group!=='undefined'){
        this.queryParams.sourceGroup = this.$route.query.group
      }
      if (this.$route.query.startTime && this.$route.query.endTime){
        this.dateRange = [this.$route.query.startTime,this.$route.query.endTime]
      }else {
        this.theLastSevenDays()
      }
      this.getUserListAll();//获取操作人 所有人列表
      this.into();
    },
    activated() {
      if (this.$route.query.group && this.$route.query.group!=='undefined'){
        this.queryParams.sourceGroup = this.$route.query.group
      }
      if (this.$route.query.startTime && this.$route.query.endTime){
        this.dateRange = [this.$route.query.startTime,this.$route.query.endTime]
      }
      this.getList()
    },
    methods: {
      theLastSevenDays() {
        // 七天前
        const date = new Date();
        const enddate = new Date(date);
        enddate.setDate(date.getDate() - 7);
        const yesterday = enddate.getFullYear() + "-" + (enddate.getMonth() + 1) + "-" + enddate.getDate();
        // 今天
        const today = this.parseTime(new Date(), '{y}-{m}-{d}');
        this.dateRange = [`${yesterday} 00:00:00`, `${today} 23:59:59`]
      },
      into() {
        getDicts("account_zone").then(response => {
          this.zonrList = response.data;
        })
        this.getList();//获取未生效状态的账号
      },
      async getUserListAll() {
        try {
          const res = await getSourceGroupsList();
          this.sourceGroupList = res.rows
          const userList_res = await getListAll();
          if (userList_res.code === 200) {
            this.operatoList = userList_res.rows;
          }
        } catch (err) {
          console.log(err);
        }
      },
      /**一键复制账号和密码*/
      copyToClip(content) {
        const input = document.createElement('input');
        input.setAttribute('value', content);
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$modal.msgSuccess("已复制到黏贴版");
      },
      /**转译售卡状态 */
      formatStatus(status) {
        let data = ''
        switch (status) {
          case '1':
            data = '未生效';
            break;
          case '2':
            data = '待出售';
            break;
          case '3':
            data = '已出售';
            break;
          case '4':
            data = '作废';
            break;
        }
        return data
      },
      /** 查询未生效账号列表 */
      async getList() {
        try {
          this.loading = true;
          console.log(this.dateRange)
          if (this.dateRange && this.dateRange.length){
            this.queryParams.startTime = this.dateRange[0]
            this.queryParams.endTime = this.dateRange[1]
          }else {
            this.queryParams.startTime = ''
            this.queryParams.endTime = ''
          }
          const notEffectiveList_res = await getRecordOfChargingCardsList(this.queryParams);
          if (notEffectiveList_res.code === 200) {
            this.notEffectiveList = notEffectiveList_res.rows;
            this.total = notEffectiveList_res.total;
            this.loading = false;
          }
        } catch (err) {
          console.log(err);
          this.loading = false;
        }
      },
      // 取消按钮
      cancel() {
        this.notEffectiveVisible = false;
        this.reset();
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.resetForm("queryForm");
        this.queryParams = {
          pageNum: 1,
          pageSize: 100,
          accountName: null,
          accountZone: null,
          createBy: null,
          createTime: null,
          exceed: false,
          operator: null,
          params: null,
          remark: null,
          searchValue: null,
          updateBy: null,
          updateTime: null,
          startTime:null,
          endTime:null,
        }
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.rechargeId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
        console.log(this.ids)
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.notEffectiveVisible = true;
        this.notEffectiveForm = row;
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["notEffectiveForm"].validate(valid => {
          if (valid) {
            if (this.notEffectiveForm.cardBalance <= 0 || this.notEffectiveForm.buyPrice <= 0) {
              this.$modal.msgError(`充值金额和进价格必须大于零`);
              return
            }
            if (this.notEffectiveForm.rechargeId != null) {
              updateSellcard(this.notEffectiveForm).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.notEffectiveVisible = false;
                this.getList();
              });
            }
          }
        });
      },
      /**
       * 判断是否为数字
       */
      checkIsNumber(number) {
        var numReg = /^\d+(\.\d+)?$/g;
        var numRe = new RegExp(numReg)
        if (numRe.test(number)) {
          return true;
        } else {
          return false;
        }
      },
      getSummaries(param) {
        const notTotals = [1, 2, 3, 6,7,8,9,10,11] //不需要小计的列数组
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '小计';
            return;
          }
          if (notTotals.includes(index)) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return (Number(prev) + Number(curr)).toFixed(2);
              } else {
                return prev;
              }
            }, 0);
            sums[index] += ' ';
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    }
  };
</script>
<style lang="scss">
.select-none{
  display: none !important;
}
.id-select{
  .el-input__suffix{
    display: none;
  }
}

</style>
