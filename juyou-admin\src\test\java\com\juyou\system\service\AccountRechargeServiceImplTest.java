package com.juyou.system.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.juyou.system.domain.AccountRecharge;
import com.juyou.system.params.AccountRechargePageParam;
import com.juyou.system.params.AccountRechargeReceiveParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class AccountRechargeServiceImplTest {

    @Autowired
    private IAccountRechargeService iAccountRechargeService;

    @Test
    public void receiveAccount(){
        AccountRechargeReceiveParam param = new AccountRechargeReceiveParam();
        param.setCount(1);
        param.setAccountZone("美国");
        param.setValue(BigDecimal.valueOf(0));
        param.setChargeStage("1");
        param.setLoginUserId(Long.valueOf(106));
        param.setLoginUserName("lijiajia");
        param.setDeptId(Long.valueOf(101));
        int i = this.iAccountRechargeService.receiveAccount(param);
        log.info("i:{}", i);
    }

    @Test
    public void selectAccountRechargeByAcid(){
        AccountRecharge vo = this.iAccountRechargeService.selectAccountRechargeByAcid(31);
        log.info("vo:{}", JSONUtil.toJsonPrettyStr(vo));
    }

    @Test
    public void selectAccountRechargePage() {
        AccountRechargePageParam param = new AccountRechargePageParam();
        param.setReceiveUser("admin");
        param.setAccountName("test");
        param.setStartReceiveTime(DateUtil.parseDateTime("2023-09-08 09:45:34"));
        param.setEndReceiveTime(DateUtil.parseDateTime("2023-09-08 15:45:34"));
        List<AccountRecharge> list = this.iAccountRechargeService.selectAccountRechargePage(param);
        log.info("list:{}", JSONUtil.toJsonPrettyStr(list));
    }


    @Test
    public void add() {
        AccountRecharge accountRecharge = new AccountRecharge();
//        accountRecharge
        this.iAccountRechargeService.insertAccountRecharge(accountRecharge);
    }

    @Test
    public void cancel() {
        //this.iAccountRechargeService.cancel(24);
    }


}
