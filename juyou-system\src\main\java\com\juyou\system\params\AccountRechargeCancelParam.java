package com.juyou.system.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号注册作废参数
 * <AUTHOR>
 */
@Data
@ApiModel("AccountRechargeCancelParam-账号注册作废参数")
public class AccountRechargeCancelParam implements Serializable {

    private static final long serialVersionUID = 6822417853284270957L;

    @ApiModelProperty("acid,唯一标识")
    private Integer acid;

    @ApiModelProperty("作废原因类型: 1 锁定 2 双禁 3 双重验证")
    private String cancelReasonType;

    @ApiModelProperty("双禁余额")
    private Double doubleProhibitedBalance;

    @ApiModelProperty("作废图片")
    private String cancelImgs;

    @ApiModelProperty("修改人-后端用")
    private String updateBy;

}
