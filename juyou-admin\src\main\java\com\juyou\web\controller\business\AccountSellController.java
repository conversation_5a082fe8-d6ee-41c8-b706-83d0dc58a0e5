package com.juyou.web.controller.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.juyou.common.annotation.Log;
import com.juyou.common.core.controller.BaseController;
import com.juyou.common.core.domain.AjaxResult;
import com.juyou.common.core.domain.ResultData;
import com.juyou.common.core.domain.ResultUtil;
import com.juyou.common.core.domain.entity.SysDictData;
import com.juyou.common.core.domain.entity.SysUser;
import com.juyou.common.core.page.TableDataInfo;
import com.juyou.common.enums.BusinessType;
import com.juyou.common.exception.ServiceException;
import com.juyou.common.utils.poi.ExcelUtil;
import com.juyou.system.constants.ConfigConstant;
import com.juyou.system.domain.*;
import com.juyou.system.domain.vo.AccountRechargeVo;
import com.juyou.system.domain.vo.AccountSellReportDetailVo;
import com.juyou.system.domain.vo.AccountSellResidualAccountVoList;
import com.juyou.system.domain.vo.AccountSellVo;
import com.juyou.system.domain.vo.*;
import com.juyou.system.enums.AccountRechArgeChargeStageEnum;
import com.juyou.system.enums.AccountSellEnum;
import com.juyou.system.params.*;
import com.juyou.system.service.*;
import com.juyou.system.utils.GoogleAuthenticator;
import com.juyou.system.utils.JuYouBusinessUtil;
import com.juyou.system.utils.pdfUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * sellcardController-账号出售
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Api(value = "账号出售", tags = "账号出售")
@RestController
@RequestMapping("/system/sellcard")
public class AccountSellController extends BaseController {
    @Autowired
    private IAccountSellService accountSellService;
    @Autowired
    private IAccountRechargeService accountRechargeService;
    @Autowired
    private IGiftCardRechargeRecordService iGiftCardRechargeRecordService;
    @Autowired
    private ISysDictDataService iSysDictDataService;

    @Autowired
    private ISysConfigService sysConfigService;

    private final ReentrantLock lock = new ReentrantLock();

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IUserGoogleAuthenticatorService iUserGoogleAuthenticatorService;
    private void checkBatchSell(List<AccountBatchSellParam> params) {
        if (CollUtil.isNotEmpty(params)) {
            for (AccountBatchSellParam param : params) {
                if (ObjUtil.isNull(param.getRechargeId())) {
                    throw new ServiceException("id不能为空");
                }
                if (StrUtil.isBlank(param.getSellChatgroupName())) {
                    throw new ServiceException("出售群不能为空");
                }
                if (StrUtil.isBlank(param.getCustName())) {
                    throw new ServiceException("售卖人-客户不能为空");
                }
                if (ObjUtil.isNull(param.getSellPrice())) {
                    throw new ServiceException("出售单价不能为空");
                }
                if (StrUtil.isBlank(param.getAccountZone())) {
                    throw new ServiceException("账号区域不能为空");
                }
                if (ObjUtil.isNull(param.getSellAmt())) {
                    throw new ServiceException("出售金额不能为空");
                }
            }
        }
    }

    @ApiOperation("获取账号销售系统剩余账号列表")
    @Log(title = "账号出售-获取账号销售系统剩余账号列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:getSystemResidualAccount')")
    @PostMapping("/getSystemResidualAccountList")
    public ResultData<List<AccountSellResidualAccountVoList>> getSystemResidualAccountList(String accountZone, String idType, boolean special) {
        List<AccountSellResidualAccountVoList> list = this.accountSellService.getSystemResidualAccountList(null, null);
        return ResultUtil.success(list);
    }

    @ApiOperation("获取雷蛇美国账号销售系统剩余账号列表")
    @Log(title = "账号出售-获取雷蛇美国账号销售系统剩余账号列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:getSystemResidualAccount')")
    @PostMapping("/getSystemResidualAccountLeiSheList")
    public ResultData<List<AccountSellResidualAccountVoList>> getSystemResidualAccountLetsheList(String accountZone, String idType, String walletArea) {
        List<AccountSellResidualAccountVoList> list = this.accountSellService.getSystemResidualAccountLeiSheList(accountZone,idType, walletArea);
        return ResultUtil.success(list);
    }

    @ApiOperation("我今日已出售金额")
    @Log(title = "账号出售-我今日已出售金额", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:todayAmountSold')")
    @PostMapping("/getTodayAmountSold")
    public ResultData<BigDecimal> getTodayAmountSold(String accountZone,String idType) {
        BigDecimal todayAmountSold = this.accountSellService.getTodayAmountSold(super.getUserId(),accountZone,idType);
        return ResultUtil.success(todayAmountSold);
    }

    @ApiOperation("我的出售作废率")
    @Log(title = "账号出售-我的出售作废率", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:sellVoidRate')")
    @PostMapping("/getSellVoidRate")
    public ResultData<BigDecimal> sellVoidRate(@RequestBody DateRangeParam param) {

       // this.accountSellService.getSellVoidedBalances(super.getUserId())
        return ResultUtil.success( accountSellService.getSellVoidedBalancesDate(super.getUserId(),param));
    }

    @ApiOperation("待领取总金额")
    @Log(title = "账号出售-待领取总金额", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:awaitReceiveTotalAmount')")
    @PostMapping("/getAwaitReceiveTotalAmount")
    public ResultData<BigDecimal> getAwaitReceiveTotalAmount(String accountZone,String idType) {
        BigDecimal totalAmount = this.accountSellService.getAwaitReceiveTotalAmount(accountZone,idType);
        return ResultUtil.success(totalAmount);
    }
    @ApiOperation("获取作废率全部")
    @Log(title = "账号出售-获取作废率全部", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:getSellVoidedBalances')")
    @PostMapping("/getSellVoidedBalances")
    public ResultData<BigDecimal> getSellVoidedBalances(@RequestBody DateRangeParam param) {
        BigDecimal obsolescenceRate = this.accountSellService.getSellVoidedBalancesDate(super.getUserId(), param);
        return ResultUtil.success(obsolescenceRate);
    }

    @ApiOperation("批量出售账号")
    @Log(title = "账号出售-批量出售账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:batchSell')")
    @PostMapping("/batchAccountSell")
    public AjaxResult batchAccountSell(@RequestBody List<AccountBatchSellParam> params) {
        if (CollUtil.isEmpty(params)) {
            throw new ServiceException("请求的集合为空!");
        }
        // check
        //检查参数是否符合要求
        this.checkBatchSell(params);
        Date now = new Date();
        List<AccountSell> list = params.stream().map(param -> {
            AccountSell sell = new AccountSell();
            sell.setRechargeId(param.getRechargeId());
            sell.setSellChatgroupName(param.getSellChatgroupName());
            sell.setCustName(param.getCustName());
            sell.setSellPrice(param.getSellPrice());
            sell.setAccountZone(param.getAccountZone());
            sell.setSellAmt(param.getSellAmt());
            sell.setSellTime(now);
            sell.setUpdateTime(now);
            sell.setUpdateBy(super.getUsername());
            sell.setDoneTime(now);
            sell.setDoneUser(super.getUsername());
            sell.setStatus(AccountSellEnum.STATUS_3.getStatus());
            return sell;
        }).collect(Collectors.toList()); //接受流中的所有元素
        int count = this.accountSellService.batchAccountSell(list);
        return toAjax(count);
    }
    @ApiOperation("转交账号")
    @Log(title = "账号出售-转交账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:transferAccount')")
    @PostMapping("/transferAccount")
    public AjaxResult transferAccount(@RequestBody AccountTransferAccountParam param) {
        if (ObjUtil.isNull(param.getUserId())) {
            throw new ServiceException("用户id不能为空!");
        }
        if (StrUtil.isBlank(param.getUserName())) {
            throw new ServiceException("用户账号不能为空!");
        }

        param.setCurrentUserId(super.getUserId());

        int i = this.accountSellService.transferAccount(param);

        return toAjax(i);
    }


    @ApiOperation("领取账号")
    @Log(title = "领取账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:receiveAccount')")
    @PostMapping("/receiveAccount")
    public AjaxResult receiveAccount(@RequestBody AccountSellReceiveParam param) {
        if (ObjUtil.isNull(param.getCount())) {
            throw new ServiceException("数量不能为空!");
        }
        if (CollUtil.isEmpty(param.getCardBalanceList())) {
            throw new ServiceException("领取面值集合不能为空!");
        }
        try {
            int i = 0;
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                i = this.accountSellService.receiveAccount(param.getCount(), param.getCardBalanceList(), super.getUserId(), super.getUsername(), super.getDeptId() ,param.getChargeStage());
                if (i == 0) {
                    throw new ServiceException("请稍后重试，没有待分配的账号!");
                }
                return toAjax(i);
            }
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } catch (InterruptedException e) {
            throw new ServiceException("请稍后重试，其他用户正在分配中!");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

//    @ApiOperation("领取账号")
//    @Log(title = "领取账号", businessType = BusinessType.OTHER)
//    @PreAuthorize("@ss.hasPermi('system:sellcard:receiveAccount')")
//    @PostMapping("/designationReceiveAccount")
//    public AjaxResult designationReceiveAccount(@RequestBody AccountSellReceiveParam param) {
//        if (ObjUtil.isNull(param.getCount())) {
//            throw new ServiceException("数量不能为空!");
//        }
//        if (CollUtil.isEmpty(param.getCardBalanceList())) {
//            throw new ServiceException("领取面值集合不能为空!");
//        }
//        try {
//            int i = 0;
//            if (lock.tryLock(5, TimeUnit.SECONDS)) {
//                i = this.accountSellService.DesignationReceiveAccount(param.getCount(),
//                        param.getCardBalanceList(), super.getUserId(), super.getUsername(), super.getDeptId(),param.getWalletArea());
//                if (i == 0) {
//                    throw new ServiceException("请稍后重试，没有待分配的账号!");
//                }
//                return toAjax(i);
//            }
//            throw new ServiceException("请稍后重试，其他用户正在分配中!");
//        } catch (InterruptedException e) {
//            throw new ServiceException("请稍后重试，其他用户正在分配中!");
//        } finally {
//            if (lock.isHeldByCurrentThread()) {
//                lock.unlock();
//            }
//        }
//    }

    @ApiOperation("未生效账号列表")
    @Log(title = "账号出售-未生效账号列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:notEffectiveList')")
    @GetMapping("/notEffectiveList")
    public TableDataInfo<AccountSellVo> notEffectiveList(AccountSellNotEffectiveParam param) {
        startPage();
        String claimableNumStr = sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES);
        String claimableNumStrTwo = sysConfigService.selectConfigByKey(ConfigConstant.ACCOUNT_SELL_PENDING_MINUTES_TWO);
        param.setFirstRechargeEffectTime(claimableNumStr);
        param.setSecondRechargeEffectTime(claimableNumStrTwo);
        List<AccountSellVo> list = this.accountSellService.notEffectiveList(param);
        return getDataTable(list);
    }

    @ApiOperation("批量生效账号")
    @Log(title = "批量生效账号", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:updateNotEffectiveList')")
    @PutMapping("/updateNotEffectiveList")
    public ResultData<Integer> updateNotEffectiveList(@RequestBody AccountSellNotEffectiveParam ids) {
        return ResultUtil.success(accountSellService.updateAccountSellMin(ids.getRechargeIds()));
    }

    @ApiOperation("查询账号出售列表")
    @Log(title = "账号出售-查询账号出售列表", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:list')")
    @GetMapping("/list")
    public TableDataInfo<AccountSell> list(AccountSellPageParam param) {
        startPage();
        List<AccountSell> list = accountSellService.selectAccountSellList(param);
        TableDataInfo<AccountSell> dataTable = getDataTable(list);
        if (CollUtil.isNotEmpty(dataTable.getRows())) {
            for (AccountSell item : dataTable.getRows()) {
                item.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(item.getBuyAmt(), item.getCardBalance()));
            }
        }
        return dataTable;
    }
    @Value("${file.upload.path}")
    private String fileUploadPath;
    @ApiOperation("批量导出")
    @Log(title = "批量导出", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:sellcard:batchExport')")
    @PostMapping("/batchExport")
    public void batchExport(HttpServletResponse response,AccountSellPageParam param) {
        List<AccountSell> list = new ArrayList<>();
        for (Integer id : param.getIds()) {
            AccountSell item = this.accountSellService.selectAccountSellByRechargeId(id);
            list.add(item);
        }
        try {
            ExcelUtil<AccountSell> util = new ExcelUtil<AccountSell>(AccountSell.class);
            util.exportExcel(response, list, "出事ID数据");
           // util.exportExcel
           // pdfUtil.exportToPDF(response, list,fileUploadPath);
        }catch (Exception e) {
            throw new ServiceException("导出失败!");
        }

    }

    /**
     * 导出sellcard列表
     */
    @PreAuthorize("@ss.hasPermi('system:sellcard:export')")
    @Log(title = "账号出售-导出sellcard列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountSellPageParam param) {
        List<AccountSell> list = accountSellService.selectAccountSellList(param);
        if (CollUtil.isNotEmpty(param.getIds())) {
            List<Integer> ids = param.getIds();
            list = new ArrayList<>();
            for (Integer id : ids) {
                AccountSell item = this.accountSellService.selectAccountSellByRechargeId(id);
                list.add(item);
            }
        }
        ExcelUtil<AccountSell> util = new ExcelUtil<AccountSell>(AccountSell.class);
        util.exportExcel(response, list, "sellcard数据");
    }


    @ApiOperation("获取详情")
    @Log(title = "账号出售-获取详情", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:details')")
    @GetMapping(value = "/{rechargeId}")
    public ResultData<AccountSellDetailVo> getInfo(@PathVariable("rechargeId") Integer rechargeId) {
        AccountSellDetailVo accountSellDetailVo = new AccountSellDetailVo();
        if (ObjUtil.isNull(rechargeId)) {
            throw new ServiceException("rechargeId不能为空");
        }
        AccountSellReportDetailVo vo = new AccountSellReportDetailVo();
        AccountSell sell = this.accountSellService.selectAccountSellByRechargeId(rechargeId);
        BeanUtil.copyProperties(sell, vo);
        GiftCardRechargeRecord record = new GiftCardRechargeRecord();
        record.setAccount(sell.getAccountName());
        List<GiftCardRechargeRecord> list = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(record);
        vo.setGiftCardRechargeRecordList(list);
        vo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), vo.getCardBalance()));
        accountSellDetailVo.setAccountSellReportDetailVo(vo);
        if (ObjUtil.isNull(sell.getAcid())) {
            throw new ServiceException("acid不能为空");
        }
        AccountRecharge accountRecharge = this.accountRechargeService.selectAccountRechargeByAcid(sell.getAcid());
        GiftCardRechargeRecord rechargeRecord = new GiftCardRechargeRecord();
        rechargeRecord.setAccount(accountRecharge.getAccountName());
        List<GiftCardRechargeRecord> cardlist = this.iGiftCardRechargeRecordService.selectGiftCardRechargeRecordList(rechargeRecord);
        AccountRechargeDetailVo echargeVo = new AccountRechargeDetailVo();
        BeanUtil.copyProperties(accountRecharge, echargeVo);
        echargeVo.setGiftCardRechargeRecordList(cardlist);
        echargeVo.setBuyPrice(JuYouBusinessUtil.calculateAverageCost(vo.getBuyAmt(), echargeVo.getRechargeAmt()));
        accountSellDetailVo.setAccountRechargeDetailVo(echargeVo);
        return ResultUtil.success(accountSellDetailVo);
    }

    /**
     * 新增sellcard
     */
    @PreAuthorize("@ss.hasPermi('system:sellcard:add')")
    @Log(title = "账号出售-新增sellcard", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountSell accountSell) {
        return toAjax(accountSellService.insertAccountSell(accountSell));
    }

    @ApiOperation("出售")
    @Log(title = "账号出售-出售", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:editSell')")
    @PostMapping("/sell")
    public AjaxResult editSell(@RequestBody AccountSellUpdateParam param) {
        if (ObjUtil.isNull(param.getRechargeId())) {
            throw new ServiceException("rechargeId不能为空");
        }
        if (ObjUtil.isNull(param.getSellPrice())) {
            throw new ServiceException("出售单价不能为空");
        }
        if (ObjUtil.isNull(param.getSellAmt())) {
            throw new ServiceException("出售金额不能为空");
        }
        if (StrUtil.isBlank(param.getSellChatgroupName())) {
            throw new ServiceException("出售群不能为空");
        }
        if (StrUtil.isBlank(param.getCustName())) {
            throw new ServiceException("售卖人不能为空");
        }
        Date now = new Date();

        AccountSell sell = new AccountSell();
        sell.setRechargeId(param.getRechargeId()); // 充值id
        sell.setAccountZone(param.getAccountZone());// 账号区域
        sell.setSellPrice(param.getSellPrice());// 出售单价
        sell.setSellAmt(param.getSellAmt());// 出售金额
        sell.setSellChatgroupName(param.getSellChatgroupName());// 出售群
        sell.setCustName(param.getCustName());// 售卖人-客户
        sell.setStatus(AccountSellEnum.STATUS_3.getStatus());// 出售状态
        sell.setSellTime(now);// 出售时间
        sell.setDoneUser(super.getUsername());// 售卖人-操作人
        sell.setDoneTime(now);// 完成时间
        sell.setUpdateBy(super.getUsername());// 更新人
        sell.setUpdateTime(now);// 更新时间
        int i = this.accountSellService.updateAccountSell(sell);
        return toAjax(i);
    }


    @ApiOperation("修改账号出售")
    @Log(title = "账号出售-修改账号出售", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:sellcard:updateRechargeCardRecord')")
    @PutMapping
    public AjaxResult edit(@RequestBody AccountSell accountSell) {
        accountSell.setUpdateBy(super.getUsername());
        if (StrUtil.isNotBlank(accountSell.getStatus()) && AccountSellEnum.STATUS_3.getStatus().equals(accountSell.getStatus())) {
            accountSell.setDoneTime(new Date());
            accountSell.setDoneUser(super.getUsername());
        }
        return toAjax(accountSellService.updateAccountSell(accountSell));
    }


    @ApiOperation("账号出售作废")
    @Log(title = "账号出售-账号出售作废", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:cancel')")
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody AccountSellCancelParam param) {
        if (ObjUtil.isNull(param.getRechargeId())) {
            throw new ServiceException("rechargeId不能为空!");
        }
        if (StrUtil.isBlank(param.getCancelReason())) {
            throw new ServiceException("作废原因不能为空!");
        }
        param.setUpdateBy(super.getUsername());
        return toAjax(this.accountSellService.cancel(param));
    }
    /**
     * 删除sellcard
     */
//    @PreAuthorize("@ss.hasPermi('system:sellcard:remove')")
//    @Log(title = "账号出售-删除sellcard", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{rechargeIds}")
//    public AjaxResult remove(@PathVariable Integer[] rechargeIds) {
//        return toAjax(accountSellService.deleteAccountSellByRechargeIds(rechargeIds));
//    }

    @ApiOperation("查询账号出售地区列表")
    @GetMapping("/listZone")
    public TableDataInfo listZone(String idType) {
        Map<String, Integer> map = new HashMap<>();
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("account_zone");
        List<SysDictData> data = iSysDictDataService.selectDictDataList(sysDictData);
        List<AccountRechargeVo> list = accountSellService.selectAccountRechargeVoList(idType);
        list.forEach(item -> {
            map.put(item.getCountry(), item.getQuantity());
        });
        for (SysDictData item : data){
            if (map.get(item.getDictLabel()) != null){
                item.setDictValue(map.get(item.getDictLabel()).toString());
            } else {
                item.setDictValue("0");
            }
        }
        return getDataTable(data);
    }

    @ApiOperation("修改置顶状态")
    @Log(title = "账号出售-修改置顶状态", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:sellcard:pinned')")
    @PutMapping("/pinned")
    public AjaxResult updatePinned(@RequestBody AccountSell accountSell) {
        return toAjax(accountSellService.updatePinned(accountSell));
    }

    @ApiOperation("放入二级库")
    @Log(title = "放入二级库", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:sellcard:remove')")
    @PutMapping ("/put")
    @Transactional
    public ResultData putAccountRecharge(@RequestBody List<AccountSell> accountSell) {
        if (accountSell.isEmpty()){
            return  ResultUtil.error("参数不能为空");
        }
        return  ResultUtil.success(accountSellService.putAccountRecharge(accountSell , AccountRechArgeChargeStageEnum.LEVEL_2.getCode()));
    }
    @ApiOperation("放入一级库")
    @Log(title = "放入一级库", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:sellcard:putOne')")
    @PostMapping ("/putOne")
    @Transactional
    public ResultData putAccountRechargeOne(@RequestBody List<AccountBatchSellParam> params) {
        if (CollUtil.isEmpty(params)) {
            throw new ServiceException("请求的集合为空!");
        }
        // check
        //检查参数是否符合要求
        this.checkBatchSell(params);
        Date now = new Date();
        List<AccountSell> list = params.stream().map(param -> {
            AccountSell sell = new AccountSell();
            sell.setRechargeId(param.getRechargeId());
            sell.setSellChatgroupName(param.getSellChatgroupName());
            sell.setCustName(param.getCustName());
            sell.setSellPrice(param.getSellPrice());
            sell.setAccountZone(param.getAccountZone());
            sell.setSellAmt(param.getSellAmt());
            sell.setSellTime(now);
            sell.setUpdateTime(now);
            sell.setUpdateBy(super.getUsername());
            sell.setDoneTime(now);
            sell.setDoneUser(super.getUsername());
            sell.setStatus(AccountSellEnum.STATUS_3.getStatus());
            return sell;
        }).collect(Collectors.toList()); //接受流中的所有元素

        return  ResultUtil.success(accountSellService.putAccountRechargeOne(list,AccountRechArgeChargeStageEnum.LEVEL_1.getCode()));
    }

    @ApiOperation("退回一级库")
    @Log(title = "退回一级库", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('system:sellcard:returnAccountSell')")
    @PutMapping("/returnAccountSell")
    @Transactional
    public ResultData returnAccountSell(@RequestBody AccountSell accountSell) {

        return  ResultUtil.success(accountSellService.returnAccountSell(accountSell));
    }

    @ApiOperation("账号出售作废恢复")
    @Log(title = "账号出售-账号出售作废", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:restore')")
    @PostMapping("/restore")
    public AjaxResult restore(@RequestBody AccountSell param) {
        if (ObjUtil.isNull(param.getRechargeId())) {
            throw new ServiceException("rechargeId不能为空!");
        }
        param.setUpdateBy(super.getUsername());
        return toAjax(this.accountSellService.restore(param));
    }

    @ApiOperation("账号出售查询")
    @Log(title = "账号出售-出售查询", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:query')")
    @GetMapping("/query")
    public TableDataInfo<AccountSell> query(AccountSellPageParam param) {
        startPage();
        return getDataTable(accountSellService.selectAccountSellListAll(param));
    }
    @ApiOperation("账号出售出售查询保存")
    @Log(title = "账号出售-保存", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('system:sellcard:save')")
    @PostMapping("/save")
    public ResultData<String> save(@RequestBody AccountSell accountSell) {
        accountSellService.updateAccountSell(accountSell);
        return  ResultUtil.success("保存成功");
    }

    @ApiOperation("批量复制账号验证角色")
    @Log(title = "账号出售-批量复制账号验证角色", businessType = BusinessType.OTHER)
    @PostMapping("/copy")
    public ResultData<Boolean> copyAccount(@RequestBody String code) {
        SysUser sysUser = this.userService.selectUserByUserName(super.getUsername());
            if(!sysUser.isAdmin()) {
                UserGoogleAuthenticator userGoogleAuthenticator = this.iUserGoogleAuthenticatorService.getByUsername(super.getUsername());
                if (ObjUtil.isNull(userGoogleAuthenticator)) {
                    throw new ServiceException("该账号没有绑定谷歌验证器,请去绑定!");
                }

                boolean isTrue = GoogleAuthenticator.check_code(userGoogleAuthenticator.getGoogleSecret(), Long.parseLong(code), System.currentTimeMillis());
                if (!isTrue) {
                    throw new ServiceException("验证码不正确!");
                }
            }
        return ResultUtil.success(true);
    }

}
