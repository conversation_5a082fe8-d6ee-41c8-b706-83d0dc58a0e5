package com.juyou.system.params;

import com.juyou.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Data
public class RecordOfChargingCardsParam extends BaseEntity {
    //账户
    @ApiModelProperty("账户")
    private String accountName;
    //卡密
    @ApiModelProperty("卡密")
    private String cardNo;
    //开始时间
    @ApiModelProperty("开始时间")
    private Date startTime;
    //结束时间
    @ApiModelProperty("结束时间")
    private Date endTime;
    //充值人
    @ApiModelProperty("充值人")
    private String receiveUser;
    //来源群
    @ApiModelProperty("来源群")
    private String sourceGroup;
    //区域
    @ApiModelProperty("区域")
    private String accountZone;

    @ApiModelProperty("登录者")
    private String userID;

}
