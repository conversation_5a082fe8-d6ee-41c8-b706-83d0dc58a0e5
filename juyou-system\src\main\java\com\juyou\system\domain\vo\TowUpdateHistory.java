package com.juyou.system.domain.vo;

import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 tow_update_history
 * 
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class TowUpdateHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序列 */
    private Long id;

    /** 当前消息 */
    @Excel(name = "当前消息")
    private String currentInfo;

    /** 上一条消息 */
    @Excel(name = "上一条消息")
    private Long previousInfo;


}
