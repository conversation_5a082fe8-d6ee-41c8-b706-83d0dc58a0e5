package com.juyou.system.service;

import com.juyou.system.domain.vo.*;
import com.juyou.system.params.CompanyPerformanceQueryParam;
import com.juyou.system.params.PerformanceSearchParam;

import java.util.List;
import java.util.Map;

/**
 * 业绩Service
 */
public interface IPerformanceService {


    /**
     * 查询业绩列表
     * @param param
     * @return
     */
    List<PerformanceVoList> list(PerformanceSearchParam param);

    CompanyPerformanceVo getPerformanceData();

    List<DepartmentTreeVo> getDepartmentList();

    PerformanceReportIsDetailedVo getPerformanceReportIsDetailed(CompanyPerformanceQueryParam param);
}
