<template>
  <el-main>
    <el-card class="box-card" >
      <div slot="header" class="clearfix">
        <span>等待分钟设置</span>
      </div>
      <el-form ref="timeForm" :inline="true" :rules="rules" :model="form" class="demo-form-inline">
        <el-form-item prop="oneTime" style="margin-left: 20px" label="一级充值完成充值，等待（分钟）：">
          <el-input v-model="form.oneTime" placeholder="请输入天数"></el-input>
        </el-form-item>
        <el-form-item prop="twoTime" style="margin-left: 20px" label="二级充值完成充值，等待（分钟）：">
          <el-input v-model="form.twoTime" placeholder="请输入天数"></el-input>
        </el-form-item>
        <el-form-item style="margin-left: 20px">
          <el-button @click="saveTime" type="primary">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <div class="next-card">
      <el-card style="flex:1" class="box-card" >
        <div slot="header" class="clearfix">
          <span>来源群设置</span>
        </div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
          <el-form-item>
            <el-input v-model="sourceGroupNumber" placeholder="请输入新的群号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="theSourceGroupIsAdded">添加</el-button>
          </el-form-item>
        </el-form>
        <el-table
            v-loading="sourceGroupsLoading"
          :data="listOfSourceGroups"
          style="width: 100%">
          <el-table-column
            align="center"
            prop="id"
            label="ID"
            >
          </el-table-column>
          <el-table-column
            align="center"
            prop="groupNumber"
            label="群号"
            >
          </el-table-column>
          <el-table-column
            align="center"
            prop="status"
            label="状态">
            <template slot-scope="{row}">
              {{row.status == '1' ? '正常':'停用'}}
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
              <el-button @click="modifyTheSourceGroup(scope.row)" type="text" size="small">修改</el-button>
              <el-button @click="deactivatedSourceGroup(scope.row)" type="text" size="small">{{scope.row.status == '1' ? '停用':'正常'}}</el-button>
            </template>
          </el-table-column>

        </el-table>
        <pagination v-show="sourceGroupTotal > 0" :total="sourceGroupTotal" :page.sync="sourceGroupParams.pageNum" :limit.sync="sourceGroupParams.pageSize"
                    @pagination="getSourceGroupsListFn" :pageSizes="[50,100,200,300]" />
      </el-card>
      <el-card style="flex:1" class="box-card" >
        <div slot="header" class="clearfix">
          <span>出售群设置</span>
        </div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
          <el-form-item>
            <el-input v-model="sellGroupNumbers" placeholder="请输入新的群号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="sellGroupAdditions">添加</el-button>
          </el-form-item>
        </el-form>
        <el-table
            v-loading="sellingGroupsLoading"
          :data="listOfSellingGroups"
          style="width: 100%">
          <el-table-column
            align="center"
            prop="id"
            label="ID"
            >
          </el-table-column>
          <el-table-column
            align="center"
            prop="groupNumber"
            label="群号"
            >
          </el-table-column>
          <el-table-column
            align="center"
            prop="status"
            label="状态">
            <template slot-scope="{row}">
              {{row.status == '1' ? '正常':'停用'}}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
              <el-button @click="modifyTheGroupIDForSale(scope.row)" type="text" size="small">修改</el-button>
              <el-button @click="deactivateTheSellGroup(scope.row)" type="text" size="small">{{scope.row.status == '1' ? '停用':'正常'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="sellingGroupTotal > 0" :total="sellingGroupTotal" :page.sync="sellingGroupParams.pageNum" :limit.sync="sellingGroupParams.pageSize"
                    @pagination="getSellingGroupsListFn" :pageSizes="[50,100,200,300]" />
      </el-card>
      <div style="flex:1">
        <el-card class="box-card" >
          <div slot="header" class="clearfix">
            <span>报表日切时间</span>
          </div>
          <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-form-item>
              <el-time-picker
                v-model="date"
                value-format="HH:mm:ss"
                placeholder="请选择日切时间">
              </el-time-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveHiganieTime">保存</el-button>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="box-card" >
          <div slot="header" class="clearfix">
            <span>未完成ID个数设置</span>
          </div>
          <el-form ref="idForm" :rules="idRules" :model="idForm" class="demo-form-inline">
            <el-form-item prop="oneNumber" label="一级充值岗位">
              <el-input v-model="idForm.oneNumber"></el-input>
            </el-form-item>
            <el-form-item prop="twoNumber" label="二级充值岗位">
              <el-input v-model="idForm.twoNumber"></el-input>
            </el-form-item>
            <el-form-item prop="sellNumber" label="出售">
              <el-input v-model="idForm.sellNumber"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveId">保存</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </el-main>

</template>
<script>
import {
  editConfigDate,
  editConfigMinutes,
  editConfigNumbers,
  editSellingGroups,
  editSourceGroups,
  getTheDetailsOfTheTopUpSettings,
  insertSellingGroups,
  insertSourceGroups,
  sellingGroupsList,
  sourceGroupsList
} from '@/api/newSystem/substitutionSettings'

export default {
  name: 'SubstitutionSettings',
  data(){
    return {
      sellingGroupTotal:0,
      sellingGroupsLoading:false,
      sellingGroupParams: {
        pageNum: 1,
        pageSize: 50,
      },
      sourceGroupTotal:0,
      sourceGroupsLoading:false,
      sourceGroupParams: {
        pageNum: 1,
        pageSize: 50,
      },

      date:'',//日切点
      sourceGroupNumber:'',//来源群号
      sellGroupNumbers:'',//出售群号
      idForm:{
        oneNumber:'',
        twoNumber:'',
        sellNumber:'',
      },
      form:{
        oneTime:'',
        twoTime:''
      },
      rules:{
        oneTime: [
          { required: true, message: '请输入一级充值完成充值等待时间', trigger: 'blur' },
        ],
        twoTime: [
          { required: true, message: '请输入二级充值完成充值等待时间', trigger: 'blur' }
        ],
      },
      idRules:{
        oneNumber: [
          { required: true, message: '请输入一级充值岗位数', trigger: 'blur' },
        ],
        twoNumber: [
          { required: true, message: '请输入二级充值岗位数', trigger: 'blur' }
        ],
        sellNumber: [
          { required: true, message: '请输入出售数', trigger: 'blur' }
        ],
      },
      tableData:[],
      listOfSourceGroups:[],
      listOfSellingGroups:[],
      sellGroups:[]
    }
  },
  activated() {
    setTimeout(()=>{
      this.getDetail()
      this.getSourceGroupsListFn();
      this.getSellingGroupsListFn();
    },600)
  },
  created() {
    this.getSourceGroupsListFn();
    this.getSellingGroupsListFn();
    this.getDetail()


  },
  methods:{/** 查询未生效账号列表 */
    getSellingGroupsListFn(){
      this.sellingGroupsLoading = true
      sellingGroupsList(this.sellingGroupParams).then(e=>{
        this.listOfSellingGroups = e.rows
        this.sellingGroupTotal=e.total
        this.sellingGroupsLoading = false
      }).catch(e=>{
        this.sellingGroupsLoading = false
      })
    },
    getSourceGroupsListFn(){
      this.sourceGroupsLoading = true
      sourceGroupsList(this.sourceGroupParams).then(e=>{
        this.listOfSourceGroups = e.rows
        this.sourceGroupTotal = e.total
        this.sourceGroupsLoading = false
      }).catch(e=>{
        this.sourceGroupsLoading = false
      })
    },
    modifyTheSourceGroup(e){
      this.$prompt('请输入新群号', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
      }).then(({ value }) => {
        let request = {
          ...e,
          groupNumber : value
        }
        editSourceGroups(request).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.getSourceGroupsListFn()
        });
      }).catch(() => {});
    },
    modifyTheGroupIDForSale(e){
      this.$prompt('请输入新群号', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
      }).then(({ value }) => {
        let request = {
          ...e,
          groupNumber : value
        }
        editSellingGroups(request).then(response => {
          console.log(response)
          this.$modal.msgSuccess("修改成功");
          this.getSellingGroupsListFn()
        });
      }).catch(() => {});
    },
    async deactivatedSourceGroup(e){
      e.status= e.status === 0 ? 1 : 0
      await editSourceGroups(e)
      this.$modal.msgSuccess('停用成功')
      this.getSourceGroupsListFn()
    },
    async deactivateTheSellGroup(e){
      e.status= e.status === 0 ? 1 : 0
      await editSellingGroups(e)
      this.$modal.msgSuccess('停用成功')
      this.getSellingGroupsListFn()
    },
    saveId(){
      this.$refs['idForm'].validate( async(valid) => {
        if (valid) {
          await editConfigNumbers(this.idForm)
          this.$modal.msgSuccess('修改成功')
          this.getDetail()
        }
      });
    },
    async saveHiganieTime(){
      if (this.date){
        console.log(this.date)
        await editConfigDate(this.date)
        this.$modal.msgSuccess('添加成功')

      } else {
        this.$modal.msgError('请选择日切时间')
      }
    },
    async getDetail(){
     const res = await getTheDetailsOfTheTopUpSettings()
      this.form.oneTime = res.data.oneMinutes
      this.form.twoTime = res.data.twoMinutes
      this.date = res.data.date
      this.idForm.oneNumber = res.data.oneNumber
      this.idForm.twoNumber = res.data.twoNumber
      this.idForm.sellNumber = res.data.sellNumber
    },
    saveTime(){
      this.$refs['timeForm'].validate( async(valid) => {
        if (valid) {
          await editConfigMinutes(this.form)
          this.$modal.msgSuccess('修改成功')
        }
      });
    },
    // 来源群添加
    async theSourceGroupIsAdded(){
      if (this.sourceGroupNumber){
        await insertSourceGroups(this.sourceGroupNumber)
        this.$modal.msgSuccess('添加成功')
        this.sourceGroupNumber = ''
        this.getSourceGroupsListFn()
      } else {
        this.$modal.msgError('请输入来源群号')
      }
    },
    // 出售群添加
    async sellGroupAdditions(){
      if (this.sellGroupNumbers){
        await insertSellingGroups(this.sellGroupNumbers)
        this.$modal.msgSuccess('添加成功')
        this.sellGroupNumbers = ''
        this.getSellingGroupsListFn()
      } else {
        this.$modal.msgError('请输入出售群号')
      }
    },
    handleClick(){

    }
  }
}
</script>

<style scoped lang="scss">
.next-card{
  display: flex;
}
.box-card{
  margin: 10px;
}
</style>
