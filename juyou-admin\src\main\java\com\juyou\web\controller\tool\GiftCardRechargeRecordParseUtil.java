package com.juyou.web.controller.tool;

import com.juyou.system.domain.GiftCardRechargeRecord;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * 礼品卡充值记录解析工具类
 */
@Slf4j
public class GiftCardRechargeRecordParseUtil {


    /**
     * 解析面值
     * @param text
     * @return
     */
    public static BigDecimal parseFaceValue(String text){
        //成功[面值：US$50.00,余额：US$50.00]
        String s = text.split(",")[0];
        String us = s.substring(s.lastIndexOf("US$")+3);
        BigDecimal d = new BigDecimal(us);
        return d;
    }

    /**
     * 解析余额
     * @param text
     * @return
     */
    public static BigDecimal parseBalance(String text){
        //成功[面值：US$50.00,余额：US$50.00]
        String s = text.split(",")[1];
        s = s.substring(0,s.length()-1);
        String us = s.substring(s.lastIndexOf("US$")+3);
        BigDecimal d = new BigDecimal(us);
        return d;
    }

    /**
     * 解析
     * @param text
     * @return
     */
    public static List<GiftCardRechargeRecord> parse(String text){
        String[] split = text.split("\n");

        log.info("s:{}", split);

        return null;
    }

    public static void main(String[] args) {
        /*String s = "NO 帐号 密码 礼品卡 卡片状态 执行时间 执行信息\n" +
                "1 <EMAIL> Qwe223322 XFLW5XH9LTNWFMWP 有效 2023/09/08 14:36:32 成功[面值：US$50.00,余额：US$50.00]\n" +
                "2 <EMAIL> Qwe223322 XGVYVZLPFNLHJQ8P 有效 2023/09/08 14:49:57 成功[面值：US$50.00,余额：US$100.00]\n" +
                "3 <EMAIL> Qwe223322 X58HCWTG3FJ8JQCV 有效 2023/09/08 14:58:31 成功[面值：US$20.00,余额：US$120.00]\n" +
                "4 <EMAIL> Qwe223322 XCXGCFP4ZQCFNC9F 有效 2023/09/08 15:00:00 成功[面值：US$50.00,余额：US$170.00]";
        parse(s);*/
        String s = "成功[面值：US$50.00,余额：US$50.00]";
        BigDecimal d = parseFaceValue(s);
        log.info("d:{}",d);

        BigDecimal d1 = parseBalance(s);
        log.info("d:{}",d1);
    }

}
