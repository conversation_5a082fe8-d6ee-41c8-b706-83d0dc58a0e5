package com.juyou.system.domain;

import com.juyou.common.annotation.Excel;
import com.juyou.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户谷歌验证器关联对象 user_google_authenticator
 * 
 * <AUTHOR>
 * @date 2023-10-07
 */
public class UserGoogleAuthenticator extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 谷歌验证秘钥 */
    @Excel(name = "谷歌验证秘钥")
    private String googleSecret;

    /** 谷歌密钥二维码(给Authenticator app扫描得到code用于校验) */
    @Excel(name = "谷歌密钥二维码(给Authenticator app扫描得到code用于校验)")
    private String secretQrCode;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setGoogleSecret(String googleSecret) 
    {
        this.googleSecret = googleSecret;
    }

    public String getGoogleSecret() 
    {
        return googleSecret;
    }
    public void setSecretQrCode(String secretQrCode) 
    {
        this.secretQrCode = secretQrCode;
    }

    public String getSecretQrCode() 
    {
        return secretQrCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("googleSecret", getGoogleSecret())
            .append("secretQrCode", getSecretQrCode())
            .toString();
    }
}
