package com.juyou.system.domain.vo;

import com.juyou.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代充查询-excel-vo
 */
@Data
@ApiModel("ChargingInquiryExcelVo")
public class ChargingInquiryExcelVo implements Serializable {

    private static final long serialVersionUID = -5170156256549816723L;

    @ApiModelProperty("序号")
    @Excel(name = "序号")
    private Long id;

    @ApiModelProperty("核对状态")
    @Excel(name = "核对状态")
    private String writeOffStatus;

    @ApiModelProperty("账号")
    @Excel(name = "账号")
    private String accountName;

    @ApiModelProperty("密码")
    @Excel(name = "密码")
    private String accountPwd;

    @ApiModelProperty("区域")
    @Excel(name = "区域")
    private String accountZone;

    @ApiModelProperty("充值状态")
    @Excel(name = "充值状态")
    private String status;

    @ApiModelProperty("充值阶段")
    @Excel(name = "充值阶段")
    private String chargeStage;

    @ApiModelProperty("剩余等待时长(分)")
    @Excel(name = "剩余等待时长(分)")
    private Integer surplusWaitDuration;

    @ApiModelProperty("ID余额")
    @Excel(name = "ID余额")
    private Double rechargeAmt;

    @ApiModelProperty("成本金额(CNY)")
    @Excel(name = "成本金额(CNY)")
    private Double buyAmt;

    @ApiModelProperty("一级充值人")
    @Excel(name = "一级充值人")
    private String primaryCharger;

    @ApiModelProperty("二级充值人")
    @Excel(name = "二级充值人")
    private String secondaryCharger;


}
