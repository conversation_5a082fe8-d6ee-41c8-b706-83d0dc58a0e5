<template>
 <div class="app-container">
   <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
     <el-form-item label="群号" prop="groupNumber">
       <el-input v-model="queryParams.groupNumber" placeholder="请输入群号" clearable size="small" style="width: 150px"/>
     </el-form-item>
     <el-form-item label="卡种" prop="cardType">
       <el-select v-model="queryParams.cardType" filterable placeholder="请选择卡种" clearable size="small" style="width: 150px">
         <el-option
           v-for="item in cardTypeList"
           :key="item.dictValue"
           :label="item.dictLabel"
           :value="item.dictValue">
         </el-option>
       </el-select>
     </el-form-item>
     <el-form-item label="国家" prop="country">
       <el-input v-model="queryParams.country" placeholder="请输入国家" clearable size="small" style="width: 240px"/>
     </el-form-item>
     <el-form-item label="面值范围" prop="minFaceValue">
       <el-input v-model="queryParams.minFaceValue" type="number" clearable size="small" style="width: 100px"/>
       <span style="margin: 0 5px">-</span>
       <el-input v-model="queryParams.maxFaceValue" type="number" clearable size="small" style="width: 100px"/>
     </el-form-item>
     <el-form-item label="状态" prop="open">
       <el-select v-model="queryParams.open" placeholder="请选择状态" clearable size="small" style="width: 150px">
         <el-option label="全部"/>
         <el-option value="0" label="关闭"/>
         <el-option value="1" label="开启"/>
       </el-select>
     </el-form-item>
     <el-form-item label="备注" prop="remark">
       <el-input v-model="queryParams.remark" placeholder="请输入备注" clearable size="small" style="width: 240px"/>
     </el-form-item>
     <el-form-item>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="$set(queryParams, 'cardType', '1017')">苹果｜Apple</el-button>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="$set(queryParams, 'cardType', '1028')">黄金雷蛇｜Gold Razer</el-button>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="$set(queryParams, 'cardType', '1001')">蒸汽｜Steam</el-button>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="$set(queryParams, 'cardType', '1029')">谷歌｜Google</el-button>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="$set(queryParams, 'cardType', '1038')">Xbox</el-button>
     </el-form-item>
     <el-form-item>
       <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
       <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
     </el-form-item>
   </el-form>
   <div>
     <h3><strong>报价列表</strong></h3>
     <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:quotation:add']" style="float: right">新增</el-button>
     <el-button type="primary" icon="el-icon-edit" size="mini" @click="handleUpdateQuotationList" v-hasPermi="['system:quotation:edit']" style="float: right;margin-right: 10px">批量修改</el-button>
     <el-button type="primary" icon="el-icon-delete" size="mini" @click="handleDeleteQuotationList" v-hasPermi="['system:quotation:remove']" style="float: right;margin-right: 10px">批量删除</el-button>
     <el-button type="primary" icon="el-icon-delete" size="mini" @click="handleOpenQuotationList" v-hasPermi="['system:quotation:edit']" style="float: right;margin-right: 10px">批量关闭</el-button>
     <el-button type="primary" icon="el-icon-delete" size="mini" @click="handleOpenList" v-hasPermi="['system:quotation:edit']" style="float: right;margin-right: 10px">批量开启1</el-button>
   </div>
   <el-table v-loading="loading" :data="quotationList" @selection-change="handleSelectionChange" ref="multipleTable">
     <el-table-column type="selection" width="55" align="center" />
     <el-table-column label="群号" align="center" prop="groupNumber" />
     <el-table-column label="卡种" align="center" prop="cardType">
       <template slot-scope="scope">
         <span v-for="item in cardTypeList" v-if="item.dictValue == scope.row.cardType">{{ item.dictLabel}}</span>
       </template>
     </el-table-column>
     <el-table-column label="国家" align="center" prop="country" />
     <el-table-column label="面值范围" align="center" prop="minFaceValue">
       <template slot-scope="scope">
         {{ scope.row.minFaceValue }} ~~ {{ scope.row.maxFaceValue }}
       </template>
     </el-table-column>
     <el-table-column label="价格" align="center" prop="price" />
     <el-table-column label="状态" align="center" prop="open">
       <template slot-scope="scope">
         <span v-if="scope.row.open == 0">关闭</span>
         <span v-if="scope.row.open == 1">开启</span>
       </template>
     </el-table-column>
     <el-table-column label="备注" align="center" prop="remark" />
     <el-table-column label="最后一次修改时间" align="center" prop="updateTime" width="180">
       <template slot-scope="scope">
         <span>{{ parseTime(scope.row.updateTime) }}</span>
       </template>
     </el-table-column>
     <el-table-column label="操作" align="center">
       <template slot-scope="scope">
         <el-button
           size="mini"
           type="text"
           icon="el-icon-edit"
           @click="handleUpdate(scope.row)"
           v-hasPermi="['system:quotation:edit']"
         >修改</el-button>
         <el-button
           size="mini"
           type="text"
           icon="el-icon-delete"
           @click="handleDelete(scope.row)"
           v-hasPermi="['system:quotation:remove']"
         >删除</el-button>
       </template>
     </el-table-column>
   </el-table>
   <div style="margin-left: 30%;margin-top: 30px">
     <el-pagination
       @size-change="handleSizeChange"
       @current-change="handleCurrentChangeUser"
       :current-page.sync="queryParams.pageNum"
       :page-sizes="[100, 200, 500, 1000,2000]"
       :page-size="queryParams.pageSize"
       layout="total, sizes, prev, pager, next, jumper"
       :total="total">
     </el-pagination>
   </div>

   <el-dialog title="批量修改报价" :visible.sync="updateOpen" width="1200px" append-to-body @close="updateClose" v-dialogDrag v-dialogDragWidth v-dialogDragHeight>
     <el-form :model="offer" ref="updateForm" :rules="rules" label-width="80px">
       <el-row>
         <el-col :span="6">
           <el-form-item label="群号">
             <el-input v-model="groupNumbers" placeholder="请输入群号" clearable size="small" style="width: 150px"/>
<!--           <el-select v-model="groupNumber" filterable placeholder="请选择群号" clearable size="small" style="width: 150px" v-if="!todo">-->
<!--             <el-option-->
<!--               v-for="item in groupList"-->
<!--               :key="item.groupNumber"-->
<!--               :label="item.groupNumber"-->
<!--               :value="item.groupNumber">-->
<!--             </el-option>-->
<!--           </el-select>-->
<!--             <el-select v-model="groupNumbers" filterable placeholder="请选择群号" clearable size="small" style="width: 150px" multiple collapse-tags v-else>-->
<!--               <el-option-->
<!--                 v-for="item in groupList"-->
<!--                 :key="item.groupNumber"-->
<!--                 :label="item.groupNumber"-->
<!--                 :value="item.groupNumber">-->
<!--               </el-option>-->
<!--             </el-select>-->
           </el-form-item>
         </el-col>
         <el-col :span="6" v-show="todo">
           <el-form-item label="卡种">
             <el-select v-model="cardType" filterable placeholder="请选择卡种" clearable size="small" style="width: 150px">
               <el-option
                 v-for="item in cardTypeList"
                 :key="item.dictValue"
                 :label="item.dictLabel"
                 :value="item.dictValue">
               </el-option>
             </el-select>
           </el-form-item>
         </el-col>
         <el-col :span="6" v-show="todo">
            <el-form-item label="国家">
              <el-input v-model="country" placeholder="请输入国家" clearable size="small" style="width: 150px"/>
            </el-form-item>
         </el-col>
         <el-col :span="6" :push="2" v-show="todo">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="groupChange" :disabled="loadingUpdate">搜索</el-button>
         </el-col>
       </el-row>
     <el-table :data="multipleSelection">
       <el-table-column label="群号" align="center" prop="groupNumber">
         <template slot-scope="scope">
           <el-input v-model="scope.row.groupNumber" placeholder="请输入群号" clearable size="small" style="width: 150px"/>
         </template>
       </el-table-column>
       <el-table-column label="卡种" align="center" prop="cardType">
         <template slot-scope="scope">
            <el-select v-model="scope.row.cardType" filterable placeholder="请选择卡种" clearable size="small" style="width: 150px">
               <el-option
                 v-for="item in cardTypeList"
                 :key="item.dictValue"
                 :label="item.dictLabel"
                 :value="item.dictValue">
               </el-option>
             </el-select>
         </template>
       </el-table-column>
       <el-table-column label="国家" align="center" prop="country">
         <template slot-scope="scope">
           <el-input v-model="scope.row.country" placeholder="请输入国家" clearable size="small" style="width: 150px"/>
         </template>
       </el-table-column>
       <el-table-column label="面值范围" align="center" prop="minFaceValue" width="300">
         <template slot-scope="scope">
           <el-input v-model="scope.row.minFaceValue" type="number" clearable size="small" style="width: 100px"/>
           <span style="margin: 0 5px">-</span>
           <el-input v-model="scope.row.maxFaceValue" type="number" clearable size="small" style="width: 100px"/>
         </template>
       </el-table-column>
       <el-table-column label="价格" align="center" prop="price">
         <template slot-scope="scope">
           <el-input v-model="scope.row.price" type="number" clearable size="small" style="width: 150px"/>
         </template>
       </el-table-column>
       <el-table-column label="备注" align="center" prop="remark">
          <template slot-scope="scope">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              placeholder=""
              v-model="scope.row.remark" style="width: 350px;">
            </el-input>
          </template>
       </el-table-column>
       <el-table-column label="状态" align="center" prop="open">
         <template slot-scope="scope">
           <el-select v-model="scope.row.open" filterable placeholder="请选择状态" clearable size="small" style="width: 150px">
             <el-option value="0" label="关闭" />
             <el-option value="1" label="开启" />
           </el-select>
         </template>
       </el-table-column>
     </el-table>
     <div style="text-align: center;margin-top: 20px">
       <el-button type="primary" @click="batchUpdateQuotation()">确 定</el-button>
       <el-button @click="updateOpen = false">取 消</el-button>
       <el-button type="primary" @click="batchUpdateQuotationList()">保存并继续修改</el-button>
     </div>
     </el-form>
   </el-dialog>

   <el-dialog title="修改报价" :visible.sync="open" width="500px" append-to-body>
     <el-form ref="form" :model="offer" :rules="offerRules" label-width="80px">
       <el-form-item label="群号" prop="groupNumber">
         <el-input v-model="offer.groupNumber" placeholder="请输入群号" />
       </el-form-item>
       <el-form-item label="卡种" prop="cardType">
         <el-select v-model="offer.cardType" placeholder="请选择卡种" filterable>
           <el-option
             v-for="item in cardTypeList"
             :key="item.dictValue"
             :label="item.dictLabel"
             :value="item.dictValue">
           </el-option>
         </el-select>
       </el-form-item>
       <el-form-item label="国家" prop="country">
         <el-input v-model="offer.country" placeholder="请输入国家" />
       </el-form-item>
       <el-form-item label="面值范围" prop="minFaceValue">
         <el-input v-model="offer.minFaceValue" placeholder="请输入面值范围" />
         <el-input v-model="offer.maxFaceValue" placeholder="请输入面值范围" />
       </el-form-item>
       <el-form-item label="价格" prop="price">
         <el-input v-model="offer.price" placeholder="请输入价格" />
       </el-form-item>
       <el-form-item label="备注" prop="remark">
         <el-input v-model="offer.remark" type="textarea" placeholder="请输入内容" />
       </el-form-item>
     </el-form>
     <div slot="footer" class="dialog-footer">
       <el-button type="primary" @click="submitForm">确 定</el-button>
       <el-button @click="open=false">取 消</el-button>
     </div>
   </el-dialog>
 </div>
</template>

<script>
import {getDicts} from "@/api/newSystem/dict/data";
import {
  batchUpdateQuotation,
  delQuotation,
  groupList,
  listQuotation, openList,
  openQuotation,
  updateQuotation
} from "@/api/newSystem/offer";
import {accRechargeCompleteRecharge} from "@/api/newSystem/accRecharge";

export default {
  name: "index",
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 100
      },
      cardTypeList: [],
      quotationList: [],
      total: 0,
      loading: false,
      multipleSelection: [],
      offer: {},
      offerRules: {
        groupNumber: [
          {required: true, message: '请输入报价所属的群号', trigger: 'blur'}
        ],
        country: [
          {required: true, message: '请输入报价所适用的国家或地区', trigger: 'blur'}
        ],
        minFaceValue: [
          {required: true, message: '请输入报价的面值范围的最小值', trigger: 'blur'}
        ],
        maxFaceValue: [
          {required: true, message: '请输入报价的面值范围的最大值', trigger: 'blur'}
        ],
        price: [
          {required: true, message: '请输入报价的价格', trigger: 'blur'}
        ],
        remark: [
          {required: true, message: '请输入备注', trigger: 'blur'}
        ],
        cardType: [
          {required: true, message: '请选择卡种', trigger: 'change'}
        ]
      },
      open: false,
      activeName: 'first',
      updateOpen: false,
      groupNumber: '',
      groupNumbers: '',
      cardType: '',
      country: '',
      rules: {
        groupNumber: [
          {required: true, message: '请输入报价所属的群号', trigger: 'blur'}
        ],
        country: [
          {required: true, message: '请输入报价所适用的国家或地区', trigger: 'blur'}
        ],
        minFaceValue: [
          {required: true, message: '请输入报价的面值范围的最小值', trigger: 'blur'}
        ],
        maxFaceValue: [
          {required: true, message: '请输入报价的面值范围的最大值', trigger: 'blur'}
        ],
        price: [
          {required: true, message: '请输入报价的价格', trigger: 'blur'}
        ],
        remark: [
          {required: true, message: '请输入备注', trigger: 'blur'}
        ],
        cardType: [
          {required: true, message: '请选择卡种', trigger: 'change'}
        ]
      },
      groupList: [],
      todo: true,
      loadingUpdate: false
    }
  },
  created() {
    this.$set(this.queryParams, 'open', '1');
    getDicts("business_gift_card_types").then(response => {
        this.cardTypeList = response.data;
      }
    );
    this.handleQuery();
  },
  methods: {
    updateClose() {
      this.$refs.multipleTable.clearSelection();
      this.multipleSelection = [];
    },
    handleQuery() {
      listQuotation(this.queryParams).then(response => {
        this.quotationList = response.rows;
        this.total = response.total;
      })
    },
    resetQuery() {
      this.queryParams = {
        groupNumber: '',
        cardType: '',
        country: '',
        minFaceValue: '',
        maxFaceValue: '',
        remark: '',
        pageNum: 1,
        pageSize: 100
      }
    },
    batchUpdateQuotation(){
      if (!this.todo) {
        this.multipleSelection.forEach(item => {
          item.groupNumber = this.groupNumber
        })
      }
      let quotationList = {groupNumber : JSON.stringify(this.multipleSelection)}
      batchUpdateQuotation(quotationList).then(response => {
        if (response.code === 200) {
          this.$message({
            message: '批量修改成功',
            type: 'success'
          });
          this.updateOpen = false;
          this.handleQuery();
        }
      })
    },
    batchUpdateQuotationList() {
      let quotationList = {groupNumber : JSON.stringify(this.multipleSelection)}
      batchUpdateQuotation(quotationList).then(response => {
        if (response.code === 200) {
          this.$message({
            message: '批量修改成功',
            type: 'success'
          });
          this.todo = true;
          this.handleQuery()
          this.groupChange();

        }
      })
    },
    groupChange() {
     if ((this.groupNumbers === '' || this.groupNumbers === null || this.groupNumbers === undefined) && (this.country === '' || this.country === null || this.country === undefined) && (this.cardType === '' || this.cardType === null || this.cardType === undefined)) {
      this.$message({
        message: '请选择条件',
        type: 'warning'
      });
      return;
    }


      this.loadingUpdate = true;
      if (this.todo) {
        let obj = {
          groupNumber: this.groupNumbers,
          country: this.country,
          cardType: this.cardType,
          open: ''
        }
        listQuotation(obj).then(response => {
          this.multipleSelection = response.rows;
          this.loadingUpdate = false;
        })
      }
    },
    submitForm() {
      updateQuotation(this.offer).then(response => {
        if (response.code === 200) {
          this.$message({
            message: '修改成功',
            type: 'success'
          });
          this.open = false;
        }
      })
    },
    handleCurrentChangeUser(val) {
      console.log(`当前页: ${val}`);
      this.queryParams.pageNum = val;
      this.handleQuery();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.queryParams.pageSize = val;
      this.handleQuery();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleUpdate(row) {
      this.offer = row;
      this.open = true;
    },
    handleDelete(row) {
      //todo 确认是否删除
      this.$modal.confirm(`请确认是否删除？（删除后不可恢复！！！）`).then(async () => {
        let ids = []
        ids.push(row.id)
        delQuotation(ids).then(response => {
          if (response.code === 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.handleQuery();
          } else {
            this.$message({
              message: response.msg,
              type: 'warning'
            });
          }
        })
      })
    },
    handleOpenQuotationList() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: '请选择报价',
          type: 'warning'
        });
      }
      this.$modal.confirm(`请确认是否关闭？`).then(async () => {
        let ids = []
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        openQuotation(ids).then(response => {
          if (response.code === 200) {
            this.$message({
              message: '关闭成功',
              type: 'success'
            });
            this.handleQuery();
          } else {
            this.$message({
              message: response.msg,
              type: 'warning'
            });
          }
        })
      })
    },
    handleOpenList() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: '请选择报价',
          type: 'warning'
        });
      }
      this.multipleSelection.forEach(item => {
        if (item.open === '1') {
          this.$message({
            message: '该报价已开启，请勿重复开启',
            type: 'warning'
          });
        }
      })
      let ids = []
      this.multipleSelection.forEach(item => {
        ids.push(item.id)
      })
      openList(ids).then(response => {
        if (response.code === 200) {
          this.$message({
            message: '开启成功',
            type: 'success'
          });
          this.handleQuery();
        } else {
          this.$message({
            message: response.msg,
            type: 'warning'
          });
        }
      })
    },
    handleDeleteQuotationList() {
      //todo 确认是否删除
      let i = 0;
      this.groupNumber = this.multipleSelection[0].groupNumber;
      this.multipleSelection.forEach(item => {
        if (item.groupNumber != this.groupNumber) {
          i++;
        }
      })
      if (i > 0) {
        this.$message({
          message: '请选择同一群号的报价',
          type: 'warning'
        });
        return;
      }
      this.$modal.confirm(`请确认是否删除？（删除后不可恢复！！！）`).then(async () => {
        let ids = []
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        delQuotation(ids).then(response => {
          if (response.code === 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.handleQuery();
          } else {
            this.$message({
              message: response.msg,
              type: 'warning'
            });
          }
        })
      })
    },
    handleAdd() {
      this.$router.push({path: '/goAddOffer/offer'})
    },
    handleUpdateQuotationList() {
      groupList().then(response => {
        this.groupList = response.rows;
        this.todo = true
        this.cardType = '';
        this.country = '';
        this.updateOpen = true;
      })

    }
  }
}
</script>

<style scoped>

</style>
